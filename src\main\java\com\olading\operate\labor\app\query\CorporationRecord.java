package com.olading.operate.labor.app.query;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.annotations.Comment;


@ColumnWidth(25)
@Data
public class CorporationRecord {

    @Comment("灵工平台id")
    @ExcelProperty("灵工平台id")
    private Long supplierId;

    @Size(max = 64)
    @NotNull
    @Comment("公司名称")
    @ExcelProperty("公司名称")
    private String name;

    @Size(max = 64)
    @Comment("开户行")
    @ExcelProperty("开户行")
    private String bankName;

    @Size(max = 64)
    @Comment("开户账号")
    @ExcelProperty("开户账号")
    private String bankAccount;

    @Size(max = 64)
    @Comment("企业电话")
    @ExcelProperty("企业电话")
    private String companyTel;

    @Size(max = 64)
    @Comment("统一社会信用代码")
    @ExcelProperty("统一社会信用代码")
    private String socialCreditCode;

    @Size(max = 64)
    @Comment("税局给的平台UUID")
    @ExcelProperty("平台UUID")
    private String taxUuid;

    public CorporationRecord(SupplierCorporationEntity entity) {
        this.supplierId = entity.getSupplierId();
        this.name = entity.getName();
        this.bankName = entity.getBankName();
        this.bankAccount = entity.getBankAccount();
        this.companyTel = entity.getCompanyTel();
        this.socialCreditCode = entity.getSocialCreditCode();
        this.taxUuid = entity.getTaxUuid();
    }

    public CorporationRecord() {
    }
}
