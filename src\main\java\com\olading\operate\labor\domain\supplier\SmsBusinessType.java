package com.olading.operate.labor.domain.supplier;

public enum SmsBusinessType {
    
    ALD_H5,

    ADL_H5_IDCARD,

    /**
     * 充值结果短信
     */
    RECHARGE,

    /**
     * 业务合作协议短信
     */
    BUS_COOPERATION,

    /**
     * 登录验证码
     */
    OLD_land_diy,

    /**
     * 指派任务
     */
    ALD_RWFB_DDJS_V2,

    /**
     * 指派任务(银行)
     */
    ALD_RWFB_DDJS_BANK,

    /**
     * 通道支付验密
     */
    CHANNEL_PAYMENT,

    /**
     * 通道提现验密
     */
    CHANNEL_WITHDRAWAL,

    /**
     * 个体工商户邀请注册
     */
    SELF_EMPLOYED_REG,

    /**
     * 汇薪社个体工商户邀请注册
     */
    HXS_SELF_REG,

    /**
     * 个体工商户上传通知
     */
    SELF_EMPLOYED_UPLOAD,

    /**
     * 个体工商户状态变更通知
     */
    SELF_EMPLOYED_STATUS,

    /**
     * 个体工商户开票审核状态变更通知
     */
    SELF_EMPLOYED_INVOICE_STATUS,

    /**
     * 批次状态变更通知
     */
    BATCH_STATUS_CHANGE,

    /**
     * 云上中国开票通知
     */
    ALD_RCYX_INVOICE,

    /**
     * 灵工二维码认证签约短验
     */
    ALD_QR_SIGN,

    /**
     * 灵工短信签约确认短验
     */
    OLD_land,
    /**
     * 人才优选个体户支付通知
     */
    ALD_RCYX_REGISTERPAY
    ;

}
