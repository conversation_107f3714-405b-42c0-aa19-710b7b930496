package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareManager;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDeclareVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class PersonalIncomeTaxDeclareService {

    private final PersonalIncomeTaxDeclareManager personalIncomeTaxDeclareManager;
    private final AuthorityManager authorityManager;

    /**
     * 新增个税申报记录
     * 业务规则：
     * 1. supplier_corporation_id + tax_payment_period 是唯一的
     * 2. 如果已存在相同的记录，则先删除再重新添加
     */
    public PersonalIncomeTaxDeclareEntity addPersonalIncomeTaxDeclare(TenantInfo tenantInfo, PersonalIncomeTaxDeclareVo vo) {
        // 校验必填字段
        validatePersonalIncomeTaxDeclareVo(vo);

        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareManager.addPersonalIncomeTaxDeclare(tenantInfo, vo);

        return entity;
    }

    /**
     * 更新个税申报记录
     */
    public PersonalIncomeTaxDeclareEntity updatePersonalIncomeTaxDeclare(TenantInfo tenantInfo, PersonalIncomeTaxDeclareVo vo) {

        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareManager.updatePersonalIncomeTaxDeclare(vo);

        return entity;
    }

    /**
     * 查询个税申报记录详情
     */
    public PersonalIncomeTaxDeclareVo queryPersonalIncomeTaxDeclare(Long id) {
        PersonalIncomeTaxDeclareVo vo = personalIncomeTaxDeclareManager.queryPersonalIncomeTaxDeclare(id);
        
        return vo;
    }

    /**
     * 根据作业主体ID查询个税申报记录列表
     */
    public List<PersonalIncomeTaxDeclareEntity> queryPersonalIncomeTaxDeclareBySupplierCorporation(Long supplierCorporationId) {
        return personalIncomeTaxDeclareManager.queryPersonalIncomeTaxDeclareBySupplierCorporation(supplierCorporationId);
    }

    /**
     * 删除个税申报记录
     */
    public void deletePersonalIncomeTaxDeclare(TenantInfo tenantInfo, Long id) {
        // 删除记录
        personalIncomeTaxDeclareManager.deletePersonalIncomeTaxDeclare(id);
    }

    /**
     * 更新申报状态为已申报
     */
    public void updateTaxStatusToDeclared(Long id) {
        personalIncomeTaxDeclareManager.updateTaxStatusToDeclared(id);
    }

    /**
     * 校验个税申报记录VO
     */
    private void validatePersonalIncomeTaxDeclareVo(PersonalIncomeTaxDeclareVo vo) {
        if (vo.getSupplierCorporationId() == null) {
            throw new BusinessException("作业主体ID不能为空");
        }
    }
}
