package com.olading.operate.labor.domain.share.identity;

import cn.hutool.json.JSONUtil;
import com.olading.basic.rpc.api.*;
import com.olading.operate.labor.AppProperties;
import com.olading.operate.labor.domain.share.identity.dto.FaceAuthResult;
import com.olading.operate.labor.domain.share.identity.dto.OcrIdentifyResult;
import com.olading.operate.labor.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class IdentifyProvider {

   private final BasicRpcService basicRpcService;
   private final AppProperties appProperties;


    public String authLimit2(PersonVo personVo) {
        if (appProperties.isMock()) {
            return null;
        }
        try {
            PersonalAuthRequest authBaseLimit2Request = new PersonalAuthRequest();
            authBaseLimit2Request.setAuthMode(AuthMode.ID_NAME);
            authBaseLimit2Request.setName(personVo.getRealName());
            authBaseLimit2Request.setIdNo(personVo.getIdCardNo());
            authBaseLimit2Request.setFlowNo(Utils.uuid());
            final PersonalAuthResponse response = basicRpcService.personalAuth(authBaseLimit2Request);
            if (response.getResult() != 0) {
                log.info("二要素鉴权异常:{}", JSONUtil.toJsonStr(response));
                return StringUtils.isNotBlank(response.getHeader().getErrorMessage()) ? response.getHeader().getErrorMessage() : "二要素鉴权未通过";
            }
            return null;
        } catch (Exception e) {
            log.info("authLimit2-调用平台二要素鉴权-异常", e);
            return "鉴权异常";
        }
    }


    public String authLimit3(PersonVo personVo) {
        if (appProperties.isMock()) {
            return null;
        }
        try {
            PersonalAuthRequest authBaseLimit3Request = new PersonalAuthRequest();
            authBaseLimit3Request.setAuthMode(AuthMode.ID_NAME_CARD);
            authBaseLimit3Request.setName(personVo.getRealName());
            authBaseLimit3Request.setCardNo(personVo.getBankCardNo());
            authBaseLimit3Request.setIdNo(personVo.getIdCardNo());
            authBaseLimit3Request.setFlowNo(Utils.uuid());
            PersonalAuthResponse response = basicRpcService.personalAuth(authBaseLimit3Request);
            if (response.getResult() != 0) {
                log.info("三要素鉴权异常:{}", JSONUtil.toJsonStr(response));
                return StringUtils.isNotBlank(response.getHeader().getErrorMessage()) ? response.getHeader().getErrorMessage() : "鉴权未通过";
            }
            return null;
        } catch (Exception e) {
            log.info("authLimit3-调用平台三要素鉴权-异常", e);
            return "鉴权异常";
        }
    }

    public String operatorLimit3(PersonVo personVo) {
        if (appProperties.isMock()) {
            log.info("运营商三要素鉴权mock ");
            return null;
        }
        try {
            PersonalAuthRequest authBaseLimit3Request = new PersonalAuthRequest();
            authBaseLimit3Request.setAuthMode(AuthMode.ID_NAME_PHONE);
            authBaseLimit3Request.setName(personVo.getRealName());
            authBaseLimit3Request.setPhoneNo(personVo.getCellphoneNo());
            authBaseLimit3Request.setIdNo(personVo.getIdCardNo());
            authBaseLimit3Request.setFlowNo(Utils.uuid());
            PersonalAuthResponse personalAuthResponse = basicRpcService.personalAuth(authBaseLimit3Request);

            if (personalAuthResponse != null && personalAuthResponse.getResult() == 0) {
                return null;
            } else {
                log.info("运营商三要素鉴权异常:{}", JSONUtil.toJsonStr(personalAuthResponse));
                String errorMessage = personalAuthResponse.getHeader().getErrorMessage();
                return errorMessage != null ? errorMessage  : "鉴权失败";
            }
        } catch (Exception e) {
            log.info("authLimit3-调用平台运营商三要素鉴权-异常", e);
            return "鉴权异常";
        }
    }

    /**
     * OCR身份证识别
     * 
     * @param nationalEmblemImg 国徽面图片base64（可选）
     * @param personalIdImg 个人证件照片面图片base64（必填）
     * @return OCR识别响应
     */
    public OcrIdentifyResponse ocrIdentify(String nationalEmblemImg, String personalIdImg) {
        try {
            OcrIdentifyRequest request = new OcrIdentifyRequest();
            request.setFlowNo(Utils.uuid());
            request.setNationalEmblemSurfaceImg(nationalEmblemImg);
            request.setPersonalIdPhotoSurfaceImg(personalIdImg);
            
            log.info("开始OCR身份证识别, flowNo: {}", request.getFlowNo());
            OcrIdentifyResponse response = basicRpcService.ocrIdentify(request);
            
            if (response.getHeader().getErrorCode() != com.olading.basic.rpc.api.ErrorCode.OK) {
                log.warn("OCR识别失败: {}", JSONUtil.toJsonStr(response.getHeader()));
            } else {
                log.info("OCR识别成功, flowNo: {}", request.getFlowNo());
            }
            
            return response;
        } catch (Exception e) {
            log.error("OCR识别异常", e);
            throw new RuntimeException("OCR识别服务异常", e);
        }
    }

    /**
     * 活体人脸识别
     * 
     * @param name 姓名
     * @param idNo 身份证号
     * @param videoBase64 视频base64
     * @return 人脸识别响应
     */
    public AuthFaceActionResponse liveFaceAuth(String name, String idNo, String videoBase64) {
        try {
            AuthFaceActionRequest request = new AuthFaceActionRequest();
            request.setFlowNo(Utils.uuid());
            request.setName(name);
            request.setIdNo(idNo);
            request.setVideo(videoBase64);
            
            log.info("开始活体人脸识别, flowNo: {}, name: {}, idNo: {}", 
                    request.getFlowNo(), name, idNo);
            AuthFaceActionResponse response = basicRpcService.liveFaceAuth(request);
            
            if (response.getHeader().getErrorCode() != com.olading.basic.rpc.api.ErrorCode.OK) {
                log.warn("活体人脸识别失败: {}", JSONUtil.toJsonStr(response.getHeader()));
            } else {
                log.info("活体人脸识别成功, flowNo: {}, similarity: {}, liveRate: {}", 
                        request.getFlowNo(), response.getSimilarity(), response.getLiveRate());
            }
            
            return response;
        } catch (Exception e) {
            log.error("活体人脸识别异常", e);
            throw new RuntimeException("活体人脸识别服务异常", e);
        }
    }

    /**
     * OCR身份证识别（便捷方法，只传个人证件照片面）
     * 
     * @param personalIdImg 个人证件照片面图片base64
     * @return OCR识别响应
     */
    public OcrIdentifyResponse ocrIdentify(String personalIdImg) {
        return ocrIdentify(null, personalIdImg);
    }

    /**
     * OCR身份证识别（返回业务结果）
     * 
     * @param nationalEmblemImg 国徽面图片base64（可选）
     * @param personalIdImg 个人证件照片面图片base64（必填）
     * @return OCR识别业务结果
     */
    public OcrIdentifyResult ocrIdentifyWithResult(String nationalEmblemImg, String personalIdImg) {
        try {
            OcrIdentifyResponse response = ocrIdentify(nationalEmblemImg, personalIdImg);
            
            if (response.getHeader().getErrorCode() != com.olading.basic.rpc.api.ErrorCode.OK) {
                return OcrIdentifyResult.failure(
                    response.getHeader().getErrorMessage(), 
                    ""
                );
            }
            
            OcrIdentifyResult result = OcrIdentifyResult.success("");
            
            // 设置识别出的信息
            if (response.getOcrInfo() != null) {
                // 这里需要根据实际的OcrInfo结构来设置字段
                 result.setName(response.getOcrInfo().getName());
                 result.setIdNo(response.getOcrInfo().getIdCardNo());
                 result.setGender(response.getOcrInfo().getSex());
                 result.setNation(response.getOcrInfo().getNation());
                 result.setBirth(response.getOcrInfo().getBirthday());
                 result.setAddress(response.getOcrInfo().getBirthAddress());
                 result.setAuthority(response.getOcrInfo().getIdCardSigningOrgans());
                 result.setValidDate(response.getOcrInfo().getIdCardValidityPeriod());
            }
            
            return result;
        } catch (Exception e) {
            log.error("OCR识别异常", e);
            return OcrIdentifyResult.failure("OCR识别服务异常: " + e.getMessage(), null);
        }
    }

    /**
     * OCR身份证识别（返回业务结果，便捷方法）
     * 
     * @param personalIdImg 个人证件照片面图片base64
     * @return OCR识别业务结果
     */
    public OcrIdentifyResult ocrIdentifyWithResult(String personalIdImg) {
        return ocrIdentifyWithResult(null, personalIdImg);
    }

    /**
     * 活体人脸识别（返回业务结果）
     * 
     * @param name 姓名
     * @param idNo 身份证号
     * @param videoBase64 视频base64
     * @return 人脸识别业务结果
     */
    public FaceAuthResult liveFaceAuthWithResult(String name, String idNo, String videoBase64) {
        try {
            AuthFaceActionResponse response = liveFaceAuth(name, idNo, videoBase64);
            
            if (response.getHeader().getErrorCode() != com.olading.basic.rpc.api.ErrorCode.OK) {
                return FaceAuthResult.failure(
                    response.getHeader().getErrorMessage(), 
                    ""
                );
            }
            
            return FaceAuthResult.success(
                "",
                response.getSimilarity(), 
                response.getLiveRate()
            );
        } catch (Exception e) {
            log.error("活体人脸识别异常", e);
            return FaceAuthResult.failure("活体人脸识别服务异常: " + e.getMessage(), null);
        }
    }

}
