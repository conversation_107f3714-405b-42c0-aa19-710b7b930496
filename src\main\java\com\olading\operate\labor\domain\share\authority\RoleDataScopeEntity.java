package com.olading.operate.labor.domain.share.authority;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Entity
@Table(name = "t_role_data_scope")
public class RoleDataScopeEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Size(max = 20)
    @Comment("角色名")
    @Column(name = "name", length = 20)
    private String name;

    @Size(max = 10)
    @Comment("角色编码")
    @Column(name = "code", length = 10)
    private String code;

    @NotNull
    @Comment("权限类型:CORPORATION-作业主体,CUSTOMER-客户,CONTRACT-合同")
    @Column(name = "data_type", length = 32)
    @Enumerated(EnumType.STRING)
    private OwnerType dataType;

    @NotNull
    @Comment("权限类型对应数据ID")
    @Column(name = "data_id")
    private Long dataId;


    @NotNull
    @Comment("角色id")
    @Column(name = "role_id", nullable = false)
    private Long roleId;

    public RoleDataScopeEntity(RoleEntity roleEntity, OwnedByFragment ownedBy) {
        this.roleId = roleEntity.getId();
        this.name = roleEntity.getName();
        this.code = roleEntity.getCode();
        this.dataType = ownedBy.getOwnerType();
        this.dataId = ownedBy.getOwnerId();
    }

    protected RoleDataScopeEntity() {
    }

}