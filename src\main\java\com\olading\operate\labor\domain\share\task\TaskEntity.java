package com.olading.operate.labor.domain.share.task;

import com.olading.boot.util.Json;
import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import jakarta.persistence.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Table(name = "t_task")
@Entity
public class TaskEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "task_type", length = 50)
    @Comment("任务类型")
    @Enumerated(EnumType.STRING)
    private TaskType taskType;

    @Column(name = "task_status", length = 25)
    @Comment("任务状态")
    @Enumerated(EnumType.STRING)
    private TaskStatus taskStatus;

    @Column(name = "task_param", length = 1024)
    @Comment("任务参数")
    private String taskParam;

    @Column(name = "task_result", length = 255)
    @Comment("任务执行结果")
    private String taskResult;

    @Column(name = "finish_time")
    @Comment("任务结束时间")
    private LocalDateTime finishTime;


    @Column(name = "file_name", length = 255)
    @Comment("文件名称")
    private String fileName;

    @Lob
    @Comment("附件文件ID列表")
    @Column(name = "attachments", length = 17000000)
    private String attachments;


    @Embedded
    private OwnedByFragment ownedBy;

    protected TaskEntity() {
    }


    public TaskEntity(long owerId, OwnerType ownerType, TaskType taskType, String taskParam, String fileName) {
        this.taskType = taskType;
        this.taskParam = taskParam;
        this.ownedBy = new OwnedByFragment(ownerType, owerId);
        this.fileName = fileName;
        this.taskStatus = TaskStatus.WAITING;
    }


    public Long getId() {
        return id;
    }

    public TaskType getTaskType() {
        return taskType;
    }

    public TaskStatus getTaskStatus() {
        return taskStatus;
    }

    public String getTaskParam() {
        return taskParam;
    }

    public String getTaskResult() {
        return taskResult;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }


    public String getFileName() {
        return fileName;
    }

    public List<String> getAttachments() {
        if (StringUtils.isBlank(attachments)) {
            return Collections.emptyList();
        } else {
            return Json.toList(attachments);
        }
    }

    void setAttachments(List<String> archiveId) {
        if (CollectionUtils.isNotEmpty(archiveId)) {
            this.attachments = Json.toJson(archiveId);
        } else {
            this.attachments = null;
        }
    }

    public void markAsRunning() {
        this.taskStatus = TaskStatus.RUNNING;
    }

    public void markAsSuccess(String result) {
        this.taskStatus = TaskStatus.SUCCESS;
        this.taskResult = result;
        this.finishTime = LocalDateTime.now();
    }

    public void markAsFailed(String error) {
        this.taskStatus = TaskStatus.FAILED;
        this.taskResult = error;
        this.finishTime = LocalDateTime.now();
    }

    public OwnedByFragment getOwnedBy() {
        return ownedBy;
    }
}
