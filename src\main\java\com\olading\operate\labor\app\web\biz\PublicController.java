package com.olading.operate.labor.app.web.biz;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.boot.core.business.BusinessException;
import com.olading.boot.core.business.Tenant;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.component.captcha.CaptchaGenerator;
import com.olading.boot.core.component.ratelimit.Bandwidth;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.core.security.AuthorizationManager;
import com.olading.boot.util.validate.FileSize;
import com.olading.operate.labor.AppProperties;
import com.olading.operate.labor.app.web.biz.supplier.SupplierController;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.identity.AuthContext;
import com.olading.operate.labor.domain.service.BossService;
import com.olading.operate.labor.domain.service.IdentityService;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.authority.SupplierMemberEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.file.FileEntity;
import com.olading.operate.labor.domain.share.file.FileInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.identity.dto.FaceAuthResult;
import com.olading.operate.labor.domain.share.identity.dto.OcrIdentifyResult;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.otp.Otp;
import com.olading.operate.labor.domain.share.otp.OtpManager;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.domain.supplier.SmsBusinessType;
import com.olading.operate.labor.domain.supplier.SupplierDomainData;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierManager;
import com.olading.operate.labor.domain.supplier.SupplierSmsTemplateEntity;
import com.olading.operate.labor.util.RedisUtils;
import com.olading.operate.labor.util.crypto.HashPassword;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;

@Tag(name = "公共接口")
@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
@Slf4j
public class PublicController extends BusinessController {

    private final AuthorizationManager authorizationManager;
    private final FileManager fileManager;
    private final OtpManager otpManager;
    private final CaptchaGenerator captchaGenerator;
    private final BossService bossService;
    private final SupplierService supplierService;
    private final SupplierManager supplierManager;
    private final UserManager userManager;
    private final IdentityService identityService;


    @Operation(summary = "更新token")
    @PostMapping(value = "renewToken")
    public WebApiResponse<RenewTokenResponse> renewToken(@RequestBody RenewTokenRequest request) {

        List<CustomerEntity> list = supplierService.getUserAvailableCustomers(currentSupplierId() , currentUserId());
        if (list.isEmpty()) {
            throw new BusinessException("无可进入企业");
        }
        if (list.stream().noneMatch(o -> o.getId().equals(request.getCustomerId()))) {
            throw new BusinessException("不可切换");
        }
        log.info("用户 {} 切换到企业: {}", currentUser().getId() ,request.getCustomerId());
        String token = authorizationManager.authorize(String.valueOf(currentUserId()), new TenantInfo(TenantInfo.TenantType.CUSTOMER, request.getCustomerId().toString()).toGeneral());
        RenewTokenResponse response = new RenewTokenResponse();
        response.setToken(token);
        return WebApiResponse.success(response);
    }

    @Operation(summary = "获取用户企业列表")
    @PostMapping(value = "listAvailableCustomer")
    public WebApiResponse<List<AvailableCustomerResponse>> listAvailableCustomer() {
        final long l = currentUserId();
        currentCustomerId();
        List<CustomerEntity> list = supplierService.getUserAvailableCustomers(currentSupplierId() , l);
        if (list.isEmpty()) {
            throw new BusinessException("无可进入企业");
        }
        return WebApiResponse.success(list.stream().map(AvailableCustomerResponse::of).collect(Collectors.toList()));
    }




    @Operation(summary = "OCR身份证识别", description = "通过身份证照片识别个人信息")
    @PostMapping("/ocrIdentify")
    public WebApiResponse<OcrIdentifyResult> ocrIdentify(@Valid @RequestBody OcrIdentifyRequest request) {
        Long supplierId = currentSupplierId();
        log.info("OCR身份证识别请求: supplierId={}", supplierId);

        OcrIdentifyResult result = identityService.ocrIdentifyWithContext(
                request.getPersonalIdImg(),
                request.getNationalEmblemImg(),
                AuthContext.builder()
                        .tenantId(currentTenant().toTenantId())
                        .authScene("REAL_NAME_AUTH")
                        .userId(currentUserId())
                        .supplierId(supplierId)
                        .personalIdFileId(request.getPersonalIdImg())
                        .nationalEmblemFileId(request.getNationalEmblemImg())
                        .build()
        );
        result.setFlowNo(HashPassword.hashPassword(result.getIdNo()+result.getName()));
        return WebApiResponse.success(result);
    }

    @Operation(summary = "活体人脸识别", description = "通过视频进行活体人脸识别")
    @PostMapping("/faceAuth")
    public WebApiResponse<FaceAuthResult> liveFaceAuth(@Valid @RequestBody FaceAuthRequest request) {
        Long supplierId = currentSupplierId();
        log.info("活体人脸识别请求: supplierId={}, name={}, idNo={}",
                supplierId, request.getName(), request.getIdNo());

        FaceAuthResult result = identityService.liveFaceAuthWithContext(
                request.getName(),
                request.getIdNo(),
                request.getVideoBase64(),
                AuthContext.builder()
                        .tenantId(currentTenant().toTenantId())
                        .authScene("REAL_NAME_AUTH")
                        .userId(currentUserId())
                        .supplierId(supplierId)
                        .faceVideoFileId(request.getVideoBase64())
                        .build()
        );

        return WebApiResponse.success(result);
    }

    @Operation(summary = "OCR识别并验证", description = "OCR识别身份证并验证与期望信息是否匹配")
    @PostMapping("/ocrVerify")
    public WebApiResponse<OcrIdentifyResult> ocrIdentifyAndVerify(@Valid @RequestBody OcrVerifyRequest request) {
        Long supplierId = currentSupplierId();
        log.info("OCR识别验证请求: supplierId={}, expectedName={}, expectedIdNo={}",
                supplierId, request.getExpectedName(), request.getExpectedIdNo());

        OcrIdentifyResult result = identityService.ocrIdentifyAndVerify(
                request.getPersonalIdImg(),
                request.getExpectedName(),
                request.getExpectedIdNo(),
                AuthContext.builder()
                        .tenantId(currentTenant().toTenantId())
                        .authScene("REAL_NAME_AUTH")
                        .userId(currentUserId())
                        .supplierId(supplierId)
                        .personalIdFileId(request.getPersonalIdImg())
                        .build()
        );
        return WebApiResponse.success(result);
    }


    @Operation(summary = "创建图形验证码")
    @RequestMapping(value = "createCaptcha", method = RequestMethod.POST)
    public WebApiResponse<String> createCaptcha() {
        return WebApiResponse.success(captchaGenerator.create());
    }

    @Operation(summary = "域名获取品牌信息")
    @RequestMapping(value = "domainInfo", method = RequestMethod.POST)
    public WebApiResponse<SupplierDomainData> domainInfo(HttpServletRequest servletRequest,@RequestBody @Valid DomainInfoRequest request) {
        final Long supplierId;
        if(StringUtils.isBlank(request.getDomain())){
            supplierId = this.getSupplierId(servletRequest);
        }else {
            supplierId = this.getSupplierId(BusinessController.extractDomain(request.getDomain()));
        }
        SupplierDomainData supplierDomain = supplierManager.getSupplierDomain(supplierId);
        return WebApiResponse.success(supplierDomain);
    }

    @Operation(summary = "输出图形验证码")
    @RequestMapping(value = "captcha", method = GET)
    public void writeCaptcha(String token, HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.IMAGE_PNG_VALUE);
        captchaGenerator.of(token).write(response.getOutputStream());
    }

    @Bandwidth(key = "#request.receiver", capacity = 5, refillTokens = 5, period = 1, timeUnit = TimeUnit.MINUTES)
    @Operation(summary = "发送登录短信验证码")
    @RequestMapping(value = "sendLoginSms", method = RequestMethod.POST)
    public WebApiResponse<CreateOtpResponse> sendLoginSms(@Valid @RequestBody SendLoginSmsRequest request, HttpServletRequest  servletRequest) {
        captchaGenerator.verify(request.getCaptchaAnswer(), request.getCaptchaToken());
        final SupplierEntity supplierEntity = supplierManager.requireSupplier(this.getSupplierId(servletRequest));
        final SupplierSmsTemplateEntity supplierSmsTemplateEntity = supplierManager.requireSupplierSmsTemplate(this.getSupplierId(servletRequest), SmsBusinessType.OLD_land_diy);
        Otp otp = otpManager.send(request.getReceiver(),supplierSmsTemplateEntity.getTemplateCode(),supplierEntity.getSignatureCode());
        CreateOtpResponse response = new CreateOtpResponse();
        response.setToken(otp.getToken());
        return WebApiResponse.success(response);
    }

    @Operation(summary = "给登录用户发送验证码")
    @RequestMapping(value = "sendOtp", method = RequestMethod.POST)
    public WebApiResponse<CreateOtpResponse> sendOtp(@Valid @RequestBody CreateOtpRequest request) {
        final SupplierEntity supplierEntity = supplierManager.getSupplierById(currentSupplierId());
        final SupplierSmsTemplateEntity supplierSmsTemplateEntity = supplierManager.requireSupplierSmsTemplate(supplierEntity.getId(), request.getBusinessType());
        captchaGenerator.verify(request.getCaptchaAnswer(), request.getCaptchaToken());
        Otp otp = otpManager.send(currentUser().getCellphone(),supplierSmsTemplateEntity.getTemplateCode(),supplierEntity.getSignatureCode());
        CreateOtpResponse response = new CreateOtpResponse();
        response.setToken(otp.getToken());
        return WebApiResponse.success(response);
    }

    @Operation(summary = "用户登录")
    @ResponseBody
    @PostMapping(value = "login")
    public WebApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest servletRequest) {

        if (request.isSmsLogin()) {
            if (!otpManager.verify(request.getAccount(), request.getOtpToken(), request.getCode())) {
                throw new BusinessException("短信验证码不正确");
            }
        }else{
            captchaGenerator.verify(request.getCaptchaAnswer(), request.getCaptchaToken());
        }

        UserEntity user = null;
        Tenant tenant = null;

        if (request.getType() == TenantInfo.TenantType.BOSS){
            user = bossService.login(request.getAccount(),request.getPassword(),!request.isSmsLogin());
            tenant = BossService.DEFAULT_TENANT.toGeneral();
        }else if(request.getType() == TenantInfo.TenantType.SUPPLIER){
            user = supplierService.login(request.getAccount(),request.getPassword(), this.getSupplierId(servletRequest), !request.isSmsLogin());
            tenant = TenantInfo.ofSupplier(this.getSupplierId(servletRequest)).toGeneral();
        }else if(request.getType() == TenantInfo.TenantType.PERSONAL){
            user = userManager.login(request.getAccount(),request.getPassword(),
                    this.getSupplierId(servletRequest), !request.isSmsLogin());
            tenant = TenantInfo.ofPersonal(this.getSupplierId(servletRequest)).toGeneral();
        }else if(request.getType() == TenantInfo.TenantType.CUSTOMER){
            user = supplierService.customerlogin(request.getAccount(),request.getPassword(),
                    this.getSupplierId(servletRequest), !request.isSmsLogin());
            tenant = TenantInfo.ofSupplier(this.getSupplierId(servletRequest)).toGeneral();
        }else {
            throw new BusinessException("暂不支持此登录方式");
        }

        String token = authorizationManager.authorize(user.getId().toString(), tenant);
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        return WebApiResponse.success(response);
    }

    @Operation(summary = "上传文件")
    @ResponseBody
    @PostMapping(value = "uploadFile", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public WebApiResponse<UploadFileResponse> uploadFile(@Valid @ModelAttribute UploadFileRequest request) throws IOException {
        TenantInfo tenant = currentTenant();

        OwnerType ownerType;
        switch (tenant.getType()) {
            case SUPPLIER -> ownerType = OwnerType.SUPPLIER;
            case BOSS -> ownerType = OwnerType.BOSS;
            case PERSONAL -> ownerType = OwnerType.SUPPLIER;
            default -> throw new IllegalStateException();
        }

        String id = fileManager.save(FilenameUtils.getName(request.getFile().getOriginalFilename()),
                request.getFile().getInputStream(),
                LocalDateTime.now().plusYears(100),
                ownerType,
                String.valueOf(tenant.getId()));

        UploadFileResponse response = new UploadFileResponse();
        response.setFileId(id);
        return WebApiResponse.success(response);
    }

    @Operation(summary = "描述文件")
    @PostMapping(value = "describeFile")
    public WebApiResponse<FileVo> downloadFile(@RequestBody DescribeFileRequest request) {
        FileEntity file = fileManager.require(request.getId());
        FileVo vo = new FileVo();
        vo.setName(file.getName());
        return WebApiResponse.success(vo);
    }

    @Operation(summary = "下载文件")
    @GetMapping(value = "downloadFile/{id}")
    public void downloadFile(@PathVariable("id") String id, HttpServletResponse response) {
        download(fileManager.getInfo(id), response, false);
    }

    @Operation(summary = "预览图片文件")
    @GetMapping(value = "previewFile/{id}")
    public void previewFile(@PathVariable("id") String id, HttpServletResponse response) {
        download(fileManager.getInfo(id), response, true);
    }

    private void download(FileInfo file, HttpServletResponse response, boolean preview) {

        try (OutputStream output = response.getOutputStream()) {
            if (!preview) {
                response.setContentType("application/octet-stream");
                String fileName = URLEncoder.encode(file.getName(), "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            }
            fileManager.load(file.getId(), output);
        } catch (IOException e) {
            throw new IllegalStateException("下载文件失败: " + file.getId(), e);
        }
    }

    @Data
    public static class DomainInfoRequest {
        @Schema(description = "域名")
        private String domain;
    }



    @Data
    public static class LoginRequest {
        @Schema(description = "传入手机号")
        @NotEmpty
        private String account;

        @Schema(description = "短信验证码")
        private String code;

        @Schema(description = "密码")
        private String password;

        @Schema(description = "验证码token")
        private String otpToken;

        @Schema(description = "登录系统")
        private TenantInfo.TenantType type=TenantInfo.TenantType.SUPPLIER;

        @Schema(description = "登录类型,true:短信,false:账户密码")
        @NotNull
        private boolean smsLogin;

        @Schema(description = "图形验证码token")
        private String captchaToken;

        @Schema(description = "图形验证码")
        private String captchaAnswer;
    }

    @Data
    public static class LoginResponse {
        private String token;
    }

    @Data
    public static class UploadFileRequest {

        @FileSize("20M")
        private MultipartFile file;
    }

    @Data
    public static class DescribeFileRequest {
        private String id;
    }

    @Data
    public static class FileVo {
        private String name;
    }

    @Data
    public static class SendLoginSmsRequest {

        @NotEmpty
        @Schema(description = "手机号")
        private String receiver;

        @NotNull
        @Schema(description = "图形验证码token")
        private String captchaToken;

        @NotNull
        @Schema(description = "图形验证码")
        private String captchaAnswer;
    }

    @Data
    @Schema(description = "创建验证码请求")
    public static class CreateOtpRequest {

        @NotNull
        @Schema(description = "图形验证码token")
        private String captchaToken;

        @NotNull
        @Schema(description = "图形验证码")
        private String captchaAnswer;

        @NotNull
        @Schema(description = "短信业务类型")
        private SmsBusinessType businessType;
    }

    @Data
    public static class CreateOtpResponse {

        private String token;
    }


    @Data
    public static class UploadFileResponse {

        @Schema(description = "文件ID")
        private String fileId;
    }

    @Data
    @Schema(description = "OCR身份证识别请求")
    public static class OcrIdentifyRequest {

        @NotBlank(message = "个人证件照片面图片不能为空")
        @Schema(description = "个人证件照片面图片", required = true)
        private String personalIdImg;

        @NotBlank(message = "国徽面图片不能为空")
        @Schema(description = "国徽面图片",required = true)
        private String nationalEmblemImg;
    }

    @Data
    @Schema(description = "活体人脸识别请求")
    public static class FaceAuthRequest {

        @NotBlank(message = "姓名不能为空")
        @Schema(description = "姓名", required = true)
        private String name;

        @NotBlank(message = "身份证号不能为空")
        @Schema(description = "身份证号", required = true)
        private String idNo;

        @NotBlank(message = "视频数据不能为空")
        @Schema(description = "视频base64", required = true)
        private String videoBase64;
    }

    @Data
    @Schema(description = "OCR识别验证请求")
    public static class OcrVerifyRequest {

        @NotBlank(message = "个人证件照片面图片不能为空")
        @Schema(description = "个人证件照片面图片id", required = true)
        private String personalIdImg;

        @NotBlank(message = "国徽面图片不能为空")
        @Schema(description = "国徽面图片id",required = true)
        private String nationalEmblemImg;

        @NotBlank(message = "期望姓名不能为空")
        @Schema(description = "期望的姓名", required = true)
        private String expectedName;

        @NotBlank(message = "期望身份证号不能为空")
        @Schema(description = "期望的身份证号", required = true)
        private String expectedIdNo;

    }

    @Data
    public static class RenewTokenRequest {
        @Schema(description = "切换到哪个客户")
        private Long customerId;
    }

    @Data
    public static class RenewTokenResponse {
        @Schema(description = "新的token")
        private String token;
    }


    @Data
    @Schema(description = "可用客户")
    public static class AvailableCustomerResponse {
        @Schema(description = "客户名称")
        private String name;
        @Schema(description = "客户id")
        private Long id;

        public static AvailableCustomerResponse of(CustomerEntity entity) {
            AvailableCustomerResponse response = new AvailableCustomerResponse();
            response.setName(entity.getName());
            response.setId(entity.getId());
            return response;
        }

    }

}
