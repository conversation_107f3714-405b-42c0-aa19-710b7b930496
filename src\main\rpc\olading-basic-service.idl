option java_package = "com.olading.basic.rpc.api";

message Header {
    ErrorCode errorCode;
    string errorMessage;
}

message TemplateVo {
    string value;
    string color;
}

message OcrInfo {
    /** 姓名 */
    string name;
    /** 身份证号 */
    string idCardNo;
    /** 出生日期 */
    string birthday;
    /** 住址 */
    string birthAddress;
    /** 签发机关 */
    string idCardSigningOrgans;
    /** 有效日期 */
    string idCardValidityPeriod;
    /** 民族 */
    string nation;
    /** 性别 */
    string sex;
    /** 是否实名认证 */
    bool idCardConfirmed;
}

message JsApiSignatureVo {
    string appId;
    string nonceStr;
    string signature;
    string timestamp;
    string url;
}

enum ErrorCode {
    /** 成功 */
    OK = 0;
    /** 业务错误 */
    ERROR = 1;
    /** 超时 */
    TIMEOUT = 2;
    //短信验证码限流
    SMS_LIMIT = 3;    
}

enum AuthMode {
    /** 姓名，身份证 */
    ID_NAME = 0;
    /** 身份证，姓名，手机号鉴权 */
    ID_NAME_PHONE = 1;
    /** 姓名，身份证，卡号 */
    ID_NAME_CARD = 2;
    /** 4要素鉴权 */
    ID_NAME_PHONE_CARD =3;
    /** 动作活体认证 */
    FACE_ACTION = 4;
    /** 身份证正面 */
    OCR_ID_FRONT = 5;
    /** 身份证背面 */
    OCR_ID_BEHIND = 6;
}

service BasicRpcService {

    /** 发送通知短信 */
    smsSend(SmsSendRequest {
        /** 短信业务编号 */
        string businessId;
        /** 模板参数 */
        map<string, string> parameters;
        /** 接收手机号 */
        string receiver;
        /** 短信签名，如果null则使用短信业务配置的签名 */
        string sign;
    }) returns (SmsSendResponse {
        Header header;
    });

    /** 发送验证码 */
    verificationSend(VerificationSendRequest {
        /** 验证方式: SMS=短信 */
        string way;
        /** 内容模板 */
        string template;
        /** 接收地址，例如手机号 */
        string receiver;
        /** 过期时间 */
        datetime expiresAt;
        /** 自定义属性 */
        map<string, string> props;
        /** ip  限流使用 */
        string limitIp;          
    }) returns (VerificationSendResponse {
        Header header;
        /** 验证代码，验证时需要 */
        string code;
    }); 

    /** 验证验证码 */
    verificationVerify(VerificationVerifyRequest {
        /** 验证代码 */
        string code;
        /** 用户输入的验证内容 */
        string challenge;
    }) returns (VerificationVerifyResponse {
        Header header;
        /** 是否验证通过 */
        bool success;
        /** 是否超时 */
        bool timeOut;
        /** token内包含的receiver */
        string receiver;
        /** 自定义属性 */
        map<string, string> props;
    });

    /** 活体鉴权接口 */
    liveFaceAuth(AuthFaceActionRequest {
        string flowNo;
        string name;
        string idNo;
        /** 视频的base64 */
        string video;
    }) returns (AuthFaceActionResponse {
        Header header;
        float similarity;
        float liveRate;
    });

    /**个人信息鉴权接口*/
    personalAuth(PersonalAuthRequest {
        string flowNo;
        AuthMode authMode;
        string name;
        string idNo;
        string cardNo;
        string phoneNo;
    }) returns (PersonalAuthResponse {
        Header header;
        int result;
    });

    /** OCR识别 */
    ocrIdentify(OcrIdentifyRequest {
        string flowNo;
        /** 国徽面（*非必填*） */
        string nationalEmblemSurfaceImg;
        /** 个人证件照片面（*必填*） */
        string personalIdPhotoSurfaceImg;
    }) returns(OcrIdentifyResponse {
        Header header;
        /** 识别信息 */
        OcrInfo ocrInfo;
    });

    /** 个人信息认证 */
    identityImageCheck(IdentityImageCheckRequest {
        /** 流水号(唯一) */
        string flowNo;
        /** 平台Code */
        string platCode;
        /** 企业ID */
        int64 merchantId;
        /** 应用Code */
        string appCode;
        /** 图片 */
        string personalImg;
        /** 身份证 */
        string idCardNo;
        /** 姓名 */
        string name;
    }) returns(IdentityImageCheckResponse {
        /** 是否验证通过 */
        bool success;
        Header header;
    });

    /** 发送通知接口 */
    noticeSend(NoticeSendRequest {
        string businessCode;
        string userId;
        string toUser;
        string templateId;
        string detailsUrl;
        map<string,TemplateVo> data;
    }) returns (NoticeSendResponse {
        Header header;
    });

    getAppleteToken(GetAppleteTokenRequest {
    }) returns (GetAppleteTokenResponse {
        string token;
    });

    getGlobalConfiguration(GetGlobalConfigRequest {
    })returns (GetGlobalConfigResponse {
        map<string,string> globalConfig;
    });

    /** 微信公众号 */
    getJsApiSignature(GetJsApiSignatureRequest {
        string url;
    }) returns (GetJsApiSignatureResponse {
        JsApiSignatureVo signatureData;
        Header header;
    });
    
    /** 关闭限流 */
    closeSmsLimit(CloseSmsLimitRequest {
                          string msg;
                      }) returns (CloseSmsLimitResponse {
        Header header;
    });    

    /** 简历识别 */
    resumeIdentify(ResumeIdentifyRequest {
        /** 简历文件的Base64 */
        string resumeFile;
        /** 文件名称(包含文件后缀) */
        string fileName;
    }) returns(ResumeIdentifyResponse{
        Header header;
        ResumeVo resumeVo;
    });

    generateScheme(GenerateSchemeRequest {
        /** 已经发布的小程序存在的页面 */
        string path;
        /** 通过scheme码进入小程序时的query最大1024个字符，
        只支持数字，大小写英文以及部分特殊字符`!#$&'()*+,/:;=?@-._~% */
        string query;
        /** 默认值"release"要打开的小程序版本,正式版为"release",
        体验版为"trial"，开发版为"develop" */
        string envVersion;
    }) returns(GenerateSchemeResponse{
        Header header;
        string openlink;
    });
}

message ResumeVo {
    /** 简历语言 1 中文 2 英文 */
    ResumeLanguageType resumeLanguage;
    /** 简历等级 学生 初级 中级 高级 */
    ResumeGrade resumeGrade;
    /** 姓名 */
    string name;
    /** 姓氏 */
    string familyName;
    /** 年龄 */
    int32 age;
    /** 性别 */
    ResumeGender gender;
    /** 身高 */
    string high;
    /** 体重 */
    string weight;
    /** 身份证号 */
    string idNo;
    /** 手机号 */
    string mobile;
    /** 座机号 */
    string phone;
    /** 传真 */
    string fax;
    /** 邮箱 */
    string email;
    /** QQ */
    string qq;
    /** 微信号 */
    string wechat;
    /** 头像 file base64 */
    string photo;
    /** 婚姻状况:0-未知,1-未婚;2-已婚;3-离异 */
    ResumeMarriage marriage;
    /** 生育情况:0-未知,1-未孕;2-已孕 */
    ResumeFertility fertility;
    /** 出生日期 */
    datetime birthday;
    /** 政治面貌 */
    ResumePolitical political;
    /** 国籍 */
    ResumeNationality nationality;
    /** 民族 */
    ResumeNation nation;
    /** 参加工作时间 */
    datetime beginWorkTime;
    /** 毕业时间 */
    datetime graduateTime;
    /** 最高学历 */
    ResumeEducationType education;
    /** 最高学位 */
    ResumeAdvancedDegree advancedDegree;
    /** 统招或自考 */
    ResumeStudentType studentType;
    /** 毕业院校 */
    string school;
    /** 毕业院校排名（国内700所） */
    int32 schoolRankings;
    /** 院校类别 0：普通，1：211 院校，2：985 院校， 3：既是 211 又是 985 院校，4：外国 院校，5：台湾大学 */
    ResumeSchoolType schoolType;
    /** 专业 */
    string major;
    /** 工作经验 */
    int32 workAge;
    /** 最近工作单位 */
    string lastCompany;
    /** 现工作地点 */
    string currentCompanyLocation;
    /** 现从事行业 */
    string vocation;
    /** 最近职位 */
    string lastJob;
    /** 海外工作经验 true/false */
    bool overseasWork;
    /** 所受奖励 针对毕业生 */
    string encouragement;
    /** 社团活动 针对毕业生 */
    string clubActivity;
    /** 所学课程 */
    string lesson;
    /** 志愿者经历 针对毕业生 */
    string volunteer;
    /** 所获证书 */
    string certificate;
    /** 计算机水平 */
    string computer;
    /** 外语水平 不特指英文 */
    string english;
    /** 兴趣爱好 */
    string interests;
    /** 居住地址 */
    string homeLocation;
    /** 邮编 */
    string postCode;
    /** 籍贯 */
    string nativePlace;
    /** 户口所在地 */
    string domicilePlace;
    /** 自我评价 */
    string selfEvaluation;
    /** 简历关键字 */
    string keyword;
    /** 来源网站 */
    string fromWebsite;
    /** 个人主页 */
    string href;
    /** 期望职位 */
    string expectedJob;
    /** 期望职业 */
    string expectedIndustry;
    /** 期望从事行业 */
    string expectedVocation;
    /** 标准行业名称 */
    string vocationStandard;
    /** 目前薪资 */
    string currentSalary;
    /** 期望薪资 */
    string expectedSalary;
    /** 期望工作地点 */
    string expectedCompanyLocation;
    /** 期望城市 */
    string expectedCity;
    /** 应聘机构（招聘网站发布的公司名称） */
    string aimInstitution;
    /** 求职信、自荐信 */
    string appLetter;
    /** 个人技能 */
    string skill;
    /** 专利 */
    string patent;
    /** 个人主页或者作品集主页地址 */
    string homePageUrl;
    /** 领英个人主页地址 */
    string linkedInUrl;
    /** 学术成果 */
    string academicAchievement;
    /** 工作类型:1-全职;2-兼职;3-实习 */
    ResumeWorkType workType;
    /** 求职状态 在职、离职、观望、正在找工作 */
    ResumeHuntingStatus huntingStatus;
    /** 可到岗时间 */
    string entryTime;
    /** 跳槽频率 */
    int32 jobHoppingFrequency;
    /** 勿投企业 */
    string doNotRecommend;
    /** 简历综合评分 0-25 */
    string score;
    /** 附加信息 */
    string memo;
    /** 如果简历语言为英文，则英文可能会挂在中文之下 */
    ResumeVo resumeEn;

    list<ResumeEducationVo> resumeEducations;
    list<ResumeExperienceVo> resumeExperiences;
    list<ResumeLanguageVo> resumeLanguages;
    list<ResumeProjectVo> resumeProjects;
    list<ResumeSkillVo> resumeSkills;
    list<ResumeTrainingVo> resumeTrainings;
}

message ResumeEducationVo{
    /** 起始时间 */
    datetime startDate;
    /** 结束时间 */
    datetime endDate;
    /** 学校 */
    string school;
    /** 专业 */
    string major;
    /** 学校类型*/
    ResumeSchoolType schoolType;
    /** 学位 */
    ResumeAdvancedDegree degree;
    /** 学历 */
    ResumeEducationType education;
    /** 院系 */
    string department;
    /** 详细介绍 */
    string summary;
    /** 学校特征标签（非简历中内容） */
    string schoolLabel;
    /** 留学经历 */
    string isStudyAbroad;
    /** 统招或自考 */
    ResumeStudentType studentType;
}

message ResumeExperienceVo{
    /** 起始时间 */
    datetime startDate;
    /** 结束时间 */
    datetime endDate;
    /** 工作时间段 eg: 1年7个月 */
    string periodsOfTime;
    /** 工作单位 */
    string company;
    /** 公司性质 */
    string companyType;
    /** 公司规模 */
    string companySize;
    /** 部门 */
    string dept;
    /** 职务 */
    string job;
    /** 行业 */
    string vocation;
    /** 工作地址 */
    string location;
    /** 薪水 */
    string salary;
    /** 汇报对象 */
    string leader;
    /** 下属人数 */
    string underlingNumber;
    /** 工作描述 */
    string summary;
    /** 离职原因 */
    string leavingReason;
    /** 业绩 */
    string achievement;
    /** 实习经历、工作经历 */
    ResumeExperienceType workType;
    /** 公司描述 */
    string companyDesc;
    /** 其它信息 */
    string otherInfo;
}

message ResumeLanguageVo{
    /** 语种 */
    string language;
    /** 掌握程度 */
    string degree;
    /** 听说能力 */
    ResumeLanguageDegree listeningSpeaking;
    /** 读写能力 */
    ResumeLanguageDegree readingWriting;
    /** 分数 */
    string score;
}

message ResumeProjectVo{
    /** 起始时间 */
    datetime startDate;
    /** 结束时间 */
    datetime endDate;
    /** 项目名称 */
    string projectName;
    /** 项目职务/角色 */
    string job;
    /** 项目描述 */
    string description;
    /** 项目职责 */
    string responsibility;
    /** 公司 */
    string company;
}
message ResumeSkillVo{
    /** 技能 */
    string skill;
    /** 掌握程度 */
    ResumeProSkillDegree degree;
    /** 使用时长 */
    string useTime;
}

message ResumeTrainingVo{
    /** 培训机构 */
    string institution;
    /** 培训名称 */
    string name;
    /** 起始时间 */
    datetime startDate;
    /** 结束时间 */
    datetime endDate;
    /** 培训课程 */
    string course;
    /** 培训地点 */
    string location;
    /** 所获证书 */
    string certificate;
    /** 培训详情描述 */
    string description;
}


enum ResumeAdvancedDegree{
    /** 博士 */
    DOCTOR = 0;
    /** 硕士 */
    MASTER = 1;
    /** 学士 */
    BACHELOR = 2;
    /** 未知 */
    NO_MATTER = 3;
}
enum ResumeEducationType {
    /** 博士 */
    DOCTOR = 0;
    /** 研究生 */
    POSTGRADUATE = 1;
    /** MBA */
    MBA = 2;
    /** 本科 */
    UNDERGRADUATE_COURSE = 3;
    /** 大专 */
    COLLEGE = 4;
    /** 大学 */
    UNIVERSITY = 5;
    /** 高中 */
    HIGH_SCHOOL = 6;
    /** 中专 */
    SECONDARY_SPECIALIZED = 7;
    /** 技校 */
    TECHNICAL_SCHOOL = 8;
    /** 中技 */
    TECHNICAL_MIDDLE_SCHOOL = 9;
    /** 初中 */
    JUNIOR_HIGH_SCHOOL = 10 ;
    /** 小学 */
    PRIMARY_SCHOOL = 11;
    /** 未知 */
    NO_MATTER = 12;
}

enum ResumeExperienceType {
    /** 实习经历 */
    INTERNSHIP = 0;
    /** 工作经历 */
    WORK = 1;
    /** 未知 */
    NO_MATTER = 2;
}

enum ResumeFertility {
    /** 未孕 */
    NOT_PREGNANT = 0;
    /** 已孕 */
    PREGNANT = 1;
    /** 未知 */
    NO_MATTER = 2;
}

enum ResumeGender{
    /** 男 */
    MALE = 0;
    /** 女 */
    FEMALE = 1;
    /** 未知 */
    NO_MATTER = 2;
}
enum ResumeGrade {
    /** 学生 */
    STUDENT = 0;
    /** 初级 */
    JUNIOR = 1;
    /** 中级 */
    MIDDLE_LEVEL = 2;
    /** 高级 */
    HIGH_LEVEL = 3;
    /** 未知 */
    NO_MATTER = 4;
}
enum ResumeHuntingStatus {
    /** 在职 */
    WORK = 0;
    /** 离职 */
    RESIGN = 1;
    /** 观望 */
    WAIT_SEE = 2;
    /** 正在找工作 */
    LOOK_FOR = 3;
    /** 未知 */
    NO_MATTER = 4;
}
enum ResumeLanguageDegree {
    /** 熟练 */
    SKILLED = 0;
    /** 流利 */
    FLUENCY = 1;
    /** 精通 */
    PROFICIENT = 2;
    /** 良好 */
    GOOD = 3;
    /** 一般 */
    GENERAL = 4;
    /** 较好 */
    BETTER = 5;
    /** 母语 */
    MOTHER_TONGUE = 6;
    /** 优秀 */
    EXCELLENT = 7;
    /** 较差 */
    POOR = 8;
    /** 未知 */
    NO_MATTER = 9;
}
enum ResumeLanguageType {
    /** 英文 */
    ENGLISH = 0;
    /** 中文 */
    CHINESE = 1;
}
enum ResumeMarriage {
    /** 已婚 */
    MARRIED = 0;
    /** 未婚 */
    UNMARRIED = 1;
    /** 离异 */
    DIVORCED = 2;
    /** 未知 */
    NO_MATTER = 3;
}
enum ResumeNation{
    /** 汉族 */
    HAN = 1;
    /** 蒙古族 */
    MONGOL = 2;
    /** 回族 */
    HUI = 3;
    /** 藏族 */
    ZANG = 4;
    /** 维吾尔族 */
    UYGUR = 5;
    /** 苗族 */
    MIAO = 6;
    /** 彝族 */
    YI = 7;
    /** 壮族 */
    ZHUANG = 8;
    /** 布依族 */
    BUYEI = 9;
    /** 朝鲜族 */
    CHOSEN = 10;
    /** 满族 */
    MAN = 11;
    /** 侗族 */
    DONG = 12;
    /** 瑶族 */
    YAO = 13;
    /** 白族 */
    BAI = 14;
    /** 土家族 */
    TUJIA = 15;
    /** 哈尼族 */
    HANI = 16;
    /** 哈萨克族 */
    KAZAK = 17;
    /** 傣族 */
    DAI = 18;
    /** 黎族 */
    LI = 19;
    /** 傈僳族 */
    LISU = 20;
    /** 佤族 */
    VA = 21;
    /** 畲族 */
    SHE = 22;
    /** 高山族 */
    GAOSHAN = 23;
    /** 拉枯族 */
    LAHU = 24;
    /** 水族 */
    SUI = 25;
    /** 东乡族 */
    DONGXIANG = 26;
    /** 纳西族 */
    NAXI = 27;
    /** 景颇族 */
    JINGPO = 28;
    /** 柯尔克孜族 */
    KIRGIZ = 29;
    /** 土族 */
    TU = 30;
    /** 达斡尔族 */
    DAUR = 31;
    /** 仫佬族 */
    MULAO = 32;
    /** 羌族 */
    QIANG = 33;
    /** 布朗族 */
    BLANG = 34;
    /** 撒拉族 */
    SALAR = 35;
    /** 毛南族 */
    MAONAN = 36;
    /** 仡佬族 */
    GELAO = 37;
    /** 锡伯族 */
    XIBE = 38;
    /** 阿昌族 */
    ACHANG = 39;
    /** 普米族 */
    PUMI = 40;
    /** 塔吉克族 */
    TAJIK = 41;
    /** 怒族 */
    NU = 42;
    /** 乌孜别克族 */
    UZBEK = 43;
    /** 俄罗斯族 */
    RUSS = 44;
    /** 鄂温克族 */
    EWENKI = 45;
    /** 德昂族 */
    DEANG = 46;
    /** 保安族 */
    BONAN = 47;
    /** 裕固族 */
    YUGUR = 48;
    /** 京族 */
    GIN = 49;
    /** 塔塔尔族 */
    TATAR = 50;
    /** 独龙族 */
    DERUNG = 51;
    /** 鄂伦春族 */
    OROQEN = 52;
    /** 赫哲族 */
    HEZHEN = 53;
    /** 门巴族 */
    MONBA = 54;
    /** 珞巴族 */
    LHOBA = 55;
    /** 基诺族 */
    JINO = 56;
    /** 其他 */
    OTHER = 57;
    /** 未知 */
    NO_MATTER = 58;
}

enum ResumeNationality {
    /** 阿富汗 */
    AF = 1;
    /** 奥兰群岛 */
    AX = 2;
    /** 阿尔巴尼亚 */
    AL = 3;
    /** 阿尔及利亚 */
    DZ = 4;
    /** 美属萨摩亚 */
    AS = 5;
    /** 安道尔 */
    AD = 6;
    /** 安哥拉 */
    AO = 7;
    /** 安圭拉 */
    AI = 8;
    /** 南极洲 */
    AQ = 9;
    /** 安提瓜和巴布达 */
    AG = 10;
    /** 阿根廷 */
    AR = 11;
    /** 亚美尼亚 */
    AM = 12;
    /** 阿鲁巴 */
    AW = 13;
    /** 澳大利亚 */
    AU = 14;
    /** 奥地利 */
    AT = 15;
    /** 阿塞拜疆 */
    AZ = 16;
    /** 巴哈马 */
    BS = 17;
    /** 巴林 */
    BH = 18;
    /** 孟加拉国 */
    BD = 19;
    /** 巴巴多斯 */
    BB = 20;
    /** 白俄罗斯 */
    BY = 21;
    /** 比利时 */
    BE = 22;
    /** 伯利兹 */
    BZ = 23;
    /** 贝宁 */
    BJ = 24;
    /** 百慕大 */
    BM = 25;
    /** 不丹 */
    BT = 26;
    /** 玻利维亚 */
    BO = 27;
    /** 波黑 */
    BA = 28;
    /** 博茨瓦纳 */
    BW = 29;
    /** 布维岛 */
    BV = 30;
    /** 巴西 */
    BR = 31;
    /** 英属印度洋领地 */
    IO = 32;
    /** 文莱 */
    BN = 33;
    /** 保加利亚 */
    BG = 34;
    /** 布基纳法索 */
    BF = 35;
    /** 布隆迪 */
    BI = 36;
    /** 柬埔寨 */
    KH = 37;
    /** 喀麦隆 */
    CM = 38;
    /** 加拿大 */
    CA = 39;
    /** 佛得角 */
    CV = 40;
    /** 开曼群岛 */
    KY = 41;
    /** 中非 */
    CF = 42;
    /** 乍得 */
    TD = 43;
    /** 智利 */
    CL = 44;
    /** 中国 */
    CN = 45;
    /** 圣诞岛 */
    CX = 46;
    /** 科科斯（基林）群岛 */
    CC = 47;
    /** 哥伦比亚 */
    CO = 48;
    /** 科摩罗 */
    KM = 49;
    /** 刚果（布） */
    CG = 50;
    /** 刚果（金） */
    CD = 51;
    /** 库克群岛 */
    CK = 52;
    /** 哥斯达黎加 */
    CR = 53;
    /** 科特迪瓦 */
    CI = 54;
    /** 克罗地亚 */
    HR = 55;
    /** 古巴 */
    CU = 56;
    /** 塞浦路斯 */
    CY = 57;
    /** 捷克 */
    CZ = 58;
    /** 丹麦 */
    DK = 59;
    /** 吉布提 */
    DJ = 60;
    /** 多米尼克 */
    DM = 61;
    /** 多米尼加 */
    DO = 62;
    /** 厄瓜多尔 */
    EC = 63;
    /** 埃及 */
    EG = 64;
    /** 萨尔瓦多 */
    SV = 65;
    /** 赤道几内亚 */
    GQ = 66;
    /** 厄立特里亚 */
    ER = 67;
    /** 爱沙尼亚 */
    EE = 68;
    /** 埃塞俄比亚 */
    ET = 69;
    /** 福克兰群岛（马尔维纳斯） */
    FK = 70;
    /** 法罗群岛 */
    FO = 71;
    /** 斐济 */
    FJ = 72;
    /** 芬兰 */
    FI = 73;
    /** 法国 */
    FR = 74;
    /** 法属圭亚那 */
    GF = 75;
    /** 法属波利尼西亚 */
    PF = 76;
    /** 法属南部领地 */
    TF = 77;
    /** 加蓬 */
    GA = 78;
    /** 冈比亚 */
    GM = 79;
    /** 格鲁吉亚 */
    GE = 80;
    /** 德国 */
    DE = 81;
    /** 加纳 */
    GH = 82;
    /** 直布罗陀 */
    GI = 83;
    /** 希腊 */
    GR = 84;
    /** 格陵兰 */
    GL = 85;
    /** 格林纳达 */
    GD = 86;
    /** 瓜德罗普 */
    GP = 87;
    /** 关岛 */
    GU = 88;
    /** 危地马拉 */
    GT = 89;
    /** 格恩西岛 */
    GG = 90;
    /** 几内亚 */
    GN = 91;
    /** 几内亚比绍 */
    GW = 92;
    /** 圭亚那 */
    GY = 93;
    /** 海地 */
    HT = 94;
    /** 赫德岛和麦克唐纳岛 */
    HM = 95;
    /** 梵蒂冈 */
    VA = 96;
    /** 洪都拉斯 */
    HN = 97;
    /** 中国香港 */
    HK = 98;
    /** 匈牙利 */
    HU = 99;
    /** 冰岛 */
    IS = 100;
    /** 印度 */
    IN = 101;
    /** 印度尼西亚 */
    ID = 102;
    /** 伊朗 */
    IR = 103;
    /** 伊拉克 */
    IQ = 104;
    /** 爱尔兰 */
    IE = 105;
    /** 英国属地曼岛 */
    IM = 106;
    /** 以色列 */
    IL = 107;
    /** 意大利 */
    IT = 108;
    /** 牙买加 */
    JM = 109;
    /** 日本 */
    JP = 110;
    /** 泽西岛 */
    JE = 111;
    /** 约旦 */
    JO = 112;
    /** 哈萨克斯坦 */
    KZ = 113;
    /** 肯尼亚 */
    KE = 114;
    /** 基里巴斯 */
    KI = 115;
    /** 朝鲜 */
    KP = 116;
    /** 韩国 */
    KR = 117;
    /** 科威特 */
    KW = 118;
    /** 吉尔吉斯斯坦 */
    KG = 119;
    /** 老挝 */
    LA = 120;
    /** 拉脱维亚 */
    LV = 121;
    /** 黎巴嫩 */
    LB = 122;
    /** 莱索托 */
    LS = 123;
    /** 利比里亚 */
    LR = 124;
    /** 利比亚 */
    LY = 125;
    /** 列支敦士登 */
    LI = 126;
    /** 立陶宛 */
    LT = 127;
    /** 卢森堡 */
    LU = 128;
    /** 中国澳门 */
    MO = 129;
    /** 前南马其顿 */
    MK = 130;
    /** 马达加斯加 */
    MG = 131;
    /** 马拉维 */
    MW = 132;
    /** 马来西亚 */
    MY = 133;
    /** 马尔代夫 */
    MV = 134;
    /** 马里 */
    ML = 135;
    /** 马耳他 */
    MT = 136;
    /** 马绍尔群岛 */
    MH = 137;
    /** 马提尼克 */
    MQ = 138;
    /** 毛利塔尼亚 */
    MR = 139;
    /** 毛里求斯 */
    MU = 140;
    /** 马约特 */
    YT = 141;
    /** 墨西哥 */
    MX = 142;
    /** 密克罗尼西亚联邦 */
    FM = 143;
    /** 摩尔多瓦 */
    MD = 144;
    /** 摩纳哥 */
    MC = 145;
    /** 蒙古 */
    MN = 146;
    /** 黑山 */
    ME = 147;
    /** 蒙特塞拉特 */
    MS = 148;
    /** 摩洛哥 */
    MA = 149;
    /** 莫桑比克 */
    MZ = 150;
    /** 缅甸 */
    MM = 151;
    /** 纳米比亚 */
    NA = 152;
    /** 瑙鲁 */
    NR = 153;
    /** 尼泊尔 */
    NP = 154;
    /** 荷兰 */
    NL = 155;
    /** 荷属安的列斯 */
    AN = 156;
    /** 新喀里多尼亚 */
    NC = 157;
    /** 新西兰 */
    NZ = 158;
    /** 尼加拉瓜 */
    NI = 159;
    /** 尼日尔 */
    NE = 160;
    /** 尼日利亚 */
    NG = 161;
    /** 纽埃 */
    NU = 162;
    /** 诺福克岛 */
    NF = 163;
    /** 北马里亚纳 */
    MP = 164;
    /** 挪威 */
    NO = 165;
    /** 阿曼 */
    OM = 166;
    /** 巴基斯坦 */
    PK = 167;
    /** 帕劳 */
    PW = 168;
    /** 巴勒斯坦 */
    PS = 169;
    /** 巴拿马 */
    PA = 170;
    /** 巴布亚新几内亚 */
    PG = 171;
    /** 巴拉圭 */
    PY = 172;
    /** 秘鲁 */
    PE = 173;
    /** 菲律宾 */
    PH = 174;
    /** 皮特凯恩 */
    PN = 175;
    /** 波兰 */
    PL = 176;
    /** 葡萄牙 */
    PT = 177;
    /** 波多黎各 */
    PR = 178;
    /** 卡塔尔 */
    QA = 179;
    /** 留尼汪 */
    RE = 180;
    /** 罗马尼亚 */
    RO = 181;
    /** 俄罗斯联邦 */
    RU = 182;
    /** 卢旺达 */
    RW = 183;
    /** 圣赫勒拿 */
    SH = 184;
    /** 圣基茨和尼维斯 */
    KN = 185;
    /** 圣卢西亚 */
    LC = 186;
    /** 圣皮埃尔和密克隆 */
    PM = 187;
    /** 圣文森特和格林纳丁斯 */
    VC = 188;
    /** 萨摩亚 */
    WS = 189;
    /** 圣马力诺 */
    SM = 190;
    /** 圣多美和普林西比 */
    ST = 191;
    /** 沙特阿拉伯 */
    SA = 192;
    /** 塞内加尔 */
    SN = 193;
    /** 塞尔维亚 */
    RS = 194;
    /** 塞舌尔 */
    SC = 195;
    /** 塞拉利昂 */
    SL = 196;
    /** 新加坡 */
    SG = 197;
    /** 斯洛伐克 */
    SK = 198;
    /** 斯洛文尼亚 */
    SI = 199;
    /** 所罗门群岛 */
    SB = 200;
    /** 索马里 */
    SO = 201;
    /** 南非 */
    ZA = 202;
    /** 南乔治亚岛和南桑德韦奇岛 */
    GS = 203;
    /** 西班牙 */
    ES = 204;
    /** 斯里兰卡 */
    LK = 205;
    /** 苏丹 */
    SD = 206;
    /** 苏里南 */
    SR = 207;
    /** 斯瓦尔巴岛和扬马延岛 */
    SJ = 208;
    /** 斯威士兰 */
    SZ = 209;
    /** 瑞典 */
    SE = 210;
    /** 瑞士 */
    CH = 211;
    /** 叙利亚 */
    SY = 212;
    /** 中国台湾 */
    TW = 213;
    /** 塔吉克斯坦 */
    TJ = 214;
    /** 坦桑尼亚 */
    TZ = 215;
    /** 泰国 */
    TH = 216;
    /** 东帝汶 */
    TL = 217;
    /** 多哥 */
    TG = 218;
    /** 托克劳 */
    TK = 219;
    /** 汤加 */
    TO = 220;
    /** 特立尼达和多巴哥 */
    TT = 221;
    /** 突尼斯 */
    TN = 222;
    /** 土耳其 */
    TR = 223;
    /** 土库曼斯坦 */
    TM = 224;
    /** 特克斯和凯科斯群岛 */
    TC = 225;
    /** 图瓦卢 */
    TV = 226;
    /** 乌干达 */
    UG = 227;
    /** 乌克兰 */
    UA = 228;
    /** 阿联酋 */
    AE = 229;
    /** 英国 */
    GB = 230;
    /** 美国 */
    US = 231;
    /** 美国本土外小岛屿 */
    UM = 232;
    /** 乌拉圭 */
    UY = 233;
    /** 乌兹别克斯坦 */
    UZ = 234;
    /** 瓦努阿图 */
    VU = 235;
    /** 委内瑞拉 */
    VE = 236;
    /** 越南 */
    VN = 237;
    /** 英属维尔京群岛 */
    VG = 238;
    /** 美属维尔京群岛 */
    VI = 239;
    /** 瓦利斯和富图纳 */
    WF = 240;
    /** 西撒哈拉 */
    EH = 241;
    /** 也门 */
    YE = 242;
    /** 南斯拉夫 */
    YU = 243;
    /** 赞比亚 */
    ZM = 244;
    /** 津巴布韦 */
    ZW = 245;
    /** 未知 */
    NO_MATTER = 246;
}
enum ResumePolitical {
    /** 中共党员*/
    CPC_MEMBER = 0;
    /** 党员*/
    PARTY_MEMBER = 1;
    /** 民盟盟员*/
    NLD_MEMBER = 2;
    /** 致公党*/
    ZHI_GONG_DANG = 3;
    /** 群众*/
    THE_MASSES = 4;
    /** 预备党员*/
    PROBATIONARY_MEMBER = 5;
    /** 民建会员*/
    DAB_MEMBER = 6;
    /** 九三学社*/
    JIUSAN_SOCIETY = 7;
    /** 团员*/
    MEMBER = 8;
    /** 民进会员*/
    DEMOCRATIC_PROGRESSIVE = 9;
    /** 台盟盟员*/
    TAIWAN_ALLIANCE = 10;
    /** 民革会员*/
    REVOLUTIONARY_MEMBER = 11;
    /** 农工党*/
    PEASANTS_LABOUR_PARTY = 12;
    /** 无党派人士*/
    INDEPENDENT = 13;
    /** 无党派民主人士*/
    NONPARTISAN_DEMOCRAT = 14;
    /** 其他*/
    OTHER = 15;
    /** 未知*/
    NO_MATTER = 16;
}

enum ResumeProSkillDegree {
    /** 专家 */
    EXPERT = 0;
    /** 熟练 */
    SKILLED = 1;
    /** 精通 */
    PROFICIENT = 2;
    /** 良好 */
    GOOD = 3;
    /** 了解 */
    UNDERSTAND = 4;
    /** 掌握 */
    MASTERY = 5;
    /** 一般 */
    GENERAL = 6;
    /** 较好 */
    BETTER = 7;
    /** 优秀 */
    EXCELLENT = 8;
    /** 较差 */
    POOR = 9;
    /** 初级 */
    JUNIOR = 10;
    /** 中级 */
    INTERMEDIATE = 11;
    /** 高级 */
    ADVANCED = 12;
    /** 未知 */
    NO_MATTER = 13;
}

enum ResumeSchoolType{
    /** 普通 */
    COMMON = 0;
    /** 211 */
    TWO_ONE_ONE = 1;
    /** 985 */
    NINE_EIGHT_FIVE = 2;
    /** 211&985 */
    BOTH = 3;
    /** 国外大学 */
    FOREIGN = 4;
    /** 台湾大学 */
    TAI_WAN = 5;
    /** 未知 */
    NO_MATTER = 6;
}

enum ResumeStudentType {
    /** 统招 */
    UNIFIED_RECRUIT = 0;
    /** 自考 */
    SELF_EXAMINATION = 1;
    /** 未知 */
    NO_MATTER = 2;
}

enum ResumeWorkType {
    /** 全职 */
    FULL_TIME = 0;
    /** 兼职 */
    PART_TIME = 1;
    /** 实习 */
    PRACTICE = 2;
    /** 义务工 */
    VOLUNTEER = 3;
    /** 未知 */
    NO_MATTER = 4;
}

