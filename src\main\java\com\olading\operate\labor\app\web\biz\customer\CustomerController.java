package com.olading.operate.labor.app.web.biz.customer;


import com.olading.boot.core.business.webapi.WebApiQueryData;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.query.WebApiQueryService;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.domain.query.CustomerQuery;
import com.olading.operate.labor.domain.service.CustomerService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.vo.CustomerVo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.task.TaskManager;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Tag(name = "企业客户相关接口")
@RestController
@RequestMapping("/api/supplier/customer")
@RequiredArgsConstructor
@Slf4j
public class CustomerController extends BusinessController {

    private final WebApiQueryService webApiQueryService;
    private final CustomerService customerService;
    private final QueryService queryService;
    private final TaskManager taskManager;


//    @AuthorityGuard(any = Authority.CUSTOMER_INFORMATION)
    @Operation(summary = "客户列表")
    @PostMapping("/listCustomer")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CUSTOMER, spel = "#request.filters.customerIds")
    })
    public WebApiQueryResponse<CustomerWithInfo> listCustomer(@RequestBody QueryFilter<WebQueryCustomerFilters> request) {

        QueryFilter<CustomerQuery.Filters> filter = request.convert(WebQueryCustomerFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        //数据权限控制
        filter.getFilters().setCustomerIds(currentDataScope().get(OwnerType.CUSTOMER));
        DataSet<CustomerWithInfo> ds = queryService.queryCustomer(filter);

        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "新增客户")
    @PostMapping("addCustomer")
    public WebApiResponse<Long> addCustomer(
            @Valid @RequestBody CustomerParam vo) {
        CustomerVo customerVo = new CustomerVo();
        BeanUtils.copyProperties(vo, customerVo);
        customerVo.setSupplierId(currentSupplierId());
        customerVo.setUserId(currentUserId());
        CustomerEntity entity = customerService.addCustomer(currentTenant(), customerVo);
        return WebApiResponse.success(entity.getId());
    }



    @Operation(summary = "更新客户")
    @PostMapping("updateCustomer")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CUSTOMER, spel = "#vo.id")
    })
    public WebApiResponse<Void> updateCustomer(
            @Valid @RequestBody CustomerParam vo) {
        CustomerVo customerVo = new CustomerVo();
        BeanUtils.copyProperties(vo, customerVo);
        customerVo.setSupplierId(currentSupplierId());
        customerService.updateCustomerInfo(currentTenant(),vo.getId(), customerVo);
        return WebApiResponse.success();
    }

    @Operation(summary = "客户详情")
    @PostMapping("queryCustomer")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CUSTOMER, spel = "#request.id")
    })
    public WebApiResponse<CustomerWithInfo> queryCustomer(
            @Valid @RequestBody IdRequest request) {
        currentUserId();
        CustomerWithInfo customerWithInfo = customerService.queryCustomer(request.getId());
        return WebApiResponse.success(customerWithInfo);
    }

    @Operation(summary = "更新客户状态")
    @PostMapping("updateCustomerStatus")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CUSTOMER, spel = "#param.id")
    })
    public WebApiResponse<Void> updateCustomerStatus(
            @Valid @RequestBody CustomerStatusUpdateParam param) {
        currentUserId();
        customerService.updateCustomerStatus(currentTenant(), param.getId(), param.getStatus());
        return WebApiResponse.success();
    }

    @Data
    public static class IdRequest {
        @NotNull(message = "企业客户不能为空")
        @Schema(description = "企业客户id")
        private Long id;
    }

    @Operation(summary = "查询平台下客户列表")
    @PostMapping("queryCustomerBySupplier")
    public  WebApiResponse<WebApiQueryData<CustomerWithInfo>> queryCustomerBySupplier() {
        SupplierEntity supplierEntity = currentSupplier();
        List<CustomerWithInfo> customerWithInfos = customerService.queryCustomerBySupplier(supplierEntity.getId());
        return WebApiResponse.query(customerWithInfos);
    }


/*    @Operation(summary = "获取管理后台登录用户的信息")
    @PostMapping(value = "/profile")
    public WebApiResponse<CustomerProfileVo> profile() {
        Long customerId = Long.parseLong(customerService.getCustomerId(currentUserId()));
        CustomerEntity customer = customerService.getCustomer(customerId);
        CustomerProfileVo profile = new CustomerProfileVo();
        profile.setUserId(currentUserId());
        profile.setCustomerId(customerId);
        profile.setCustomerName(customer.getName());
//        profile.setPhone(customer.getBalanceAlertPhone());
//        profile.setAmount(customer.getBalanceAlertThreshold());
        return WebApiResponse.success(profile);
    }*/
    





/*    @AuthorityGuard(any = Authority.CUSTOMER_ACCOUNT)
    @Operation(summary = "设置账户余额阈值")
    @PostMapping("/setAccountBalance")
    @Transactional
    public WebApiResponse<Void> setAccountBalanceSms(@Valid @RequestBody SetAccountBalanceSmsRequest request) {
        List<String> phoneNumbers = request.getPhoneNumbers().stream()
                .filter(s -> s != null && !s.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        customerService.saveAccountBalanceSms(currentUserId(), request.getBalanceThreshold(), phoneNumbers);
        return WebApiResponse.success();
    }*/











    /*@AuthorityGuard(any = Authority.CUSTOMER_TASK)
    @Operation(summary = "保存任务")
    @PostMapping("/saveTask")
    public WebApiResponse<String> saveTask(@Valid @RequestBody TaskRequest request) {
        TenantInfo tenant = currentTenant();
        OwnerType ownerType;
        switch (tenant.getType()) {
            case CUSTOMER -> ownerType = OwnerType.CUSTOMER;
            default -> throw new IllegalStateException();
        }
        long ownerId = Long.parseLong(customerService.getCustomerId(currentUserId()));
        String fileName = null;
        if (request.getTaskType() == TaskType.CUSTOMER_WECHAT) {
            fileName = generateFileName("微信转账到零钱交易明细下载-");
        } else if (request.getTaskType() == TaskType.CUSTOMER_ALIPAY) {
            fileName = generateFileName("支付宝交易明细下载-");
        } else if (request.getTaskType() == TaskType.CUSTOMER_WECHAT_RP) {
            fileName = generateFileName("微信红包交易明细下载-");
        } else if (request.getTaskType() == TaskType.CUSTOMER_ACCOUNT) {
            fileName = generateFileName("账户交易明细下载-");
        } else if (request.getTaskType() == TaskType.CUSTOMER_WECHAT_COUPON) {
            fileName = generateFileName("微信代金券发放明细下载-");
        } else if (request.getTaskType() == TaskType.CUSTOMER_ALIPAY_COUPON) {
            fileName = generateFileName("数字分行红包发放明细下载-");
        } else if (request.getTaskType() == TaskType.CUSTOMER_WECHAT_BATCH_NO) {
            fileName = generateFileName("微信优惠券批次明细下载-");
        }

        //设置客户ID
        request.setCustomerId(Collections.singletonList(Long.parseLong(customerService.getCustomerId(currentUserId()))));

        TaskEntity taskEntity = taskManager.submit(ownerId, ownerType, request.getTaskType(), Json.toJson(request), fileName);
        if (taskEntity == null) {
            return WebApiResponse.fail(WebApiCodes.BUSINESS_FAIL, "任务提交失败");
        }
        return WebApiResponse.success("任务提交成功");
    }*/


   /* @AuthorityGuard(any = Authority.CUSTOMER_TASK)
    @Operation(summary = "查询任务")
    @PostMapping("/queryTask")
    public WebApiQueryResponse<TaskInfoVo> queryTask(@Valid @RequestBody QueryFilter<WebQueryTaskFilters> request) throws IOException {
        long customerId = Long.parseLong(customerService.getCustomerId(currentUserId()));
        WebQueryTaskFilters webQueryTaskFilters = new WebQueryTaskFilters(customerId, request.getFilters().getTaskType());
        request.setFilters(webQueryTaskFilters);
        QueryFilter<OrderDetailTaskQuery.Filters> filter = request.convert(WebQueryTaskFilters::convert);
        DataSet<TaskEntity> ds = queryService.queryOrderDetailTask(filter);
        // 过滤查询结果
        List<TaskInfoVo> filteredTaskInfo = ds.getData().stream()
                .map(o -> {
                        TaskInfoVo vo = new TaskInfoVo();
                        vo.setTaskId(o.getId());
                        vo.setCreateTime(o.getCreateTime());
                        vo.setFileName(o.getFileName());
                        vo.setAttachments(o.getAttachments());
                        long daysBetween = ChronoUnit.DAYS.between(o.getCreateTime(), LocalDateTime.now());
                        if (daysBetween > 7) {
                            vo.setStatus("已过期");
                        } else if (o.getTaskStatus() == TaskStatus.FAILED) {
                            vo.setStatus("失败");
                        } else if (o.getTaskStatus() == TaskStatus.RUNNING || o.getTaskStatus() == TaskStatus.WAITING) {
                            vo.setStatus("生成中");
                        } else if (o.getTaskStatus() == TaskStatus.SUCCESS) {
                            vo.setStatus("已生成");
                        }
                        return vo;
                }).collect(Collectors.toList());
        return WebApiQueryResponse.success(filteredTaskInfo, ds.getTotal());
    }*/


/*
    @AuthorityGuard(any = Authority.CUSTOMER_TASK)
    @Operation(summary = "删除任务")
    @PostMapping("/deleteTask")
    public WebApiResponse<Void> deleteTask(@RequestBody DeleteTaskRequest request) {
        taskManager.delete(request.getTaskId());
        return WebApiResponse.success();
    }
*/




/*
    private String generateFileName(String fileName) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String date = currentDate.format(formatter);
        // 生成四位随机码
        Random random = new Random();
        int randomCode = 1000 + random.nextInt(9000); // 生成1000到9999之间的随机数
        // 拼接文件名称
        return fileName + date + "-" + randomCode + ".zip";
    }
*/




    /*private void validateDateRange(LocalDateTime createTimeBegin, LocalDateTime createTimeEnd, String orderNo) {
        long daysBetween = Duration.between(createTimeBegin, createTimeEnd).toDays();
        if (StringUtils.isNotBlank(orderNo)) {
            if (daysBetween > 91L) {
                throw new BusinessException("时间范围不能超过90天");
            }
        } else {
            if (daysBetween > 31L) {
                throw new BusinessException("时间范围不能超过一个月");
            }
        }
    }*/


    /*@Data
    @AllArgsConstructor
    public static class WebQueryTaskFilters {
        private Long ownerId;
        private TaskType taskType;
        public OrderDetailTaskQuery.Filters convert() {
            return Beans.copyBean(this, OrderDetailTaskQuery.Filters.class);
        }
    }*/

    /*@Data
    public static class DeleteTaskRequest {
        private Long taskId;
    }*/


    /*@Data
    public static class TaskInfoVo {
        private Long taskId;
        private LocalDateTime createTime;
        private String fileName;
        private String status;
        private List<String> attachments;
    }*/

    @Data
    public static class WebQueryCustomerFilters {
        @Schema(description = "编号")
        private Long id;

        @Schema(description = "客户名称")
        private String name;

        @Schema(description = "创建时间-起")
        private LocalDate createTimeStart;

        @Schema(description = "创建时间-止")
        private LocalDate createTimeEnd;

        @Schema(description = "统一社会信用代码")
        private String socialCreditCode;

        private Set<Long> customerIds;

        public CustomerQuery.Filters convert() {
            return Beans.copyBean(this, CustomerQuery.Filters.class);
        }
    }


    @Data
    public static class CustomerStatusUpdateParam {
        @NotNull(message = "客户ID不能为空")
        @Schema(description = "客户ID")
        private Long id;

        @NotBlank(message = "客户状态不能为空")
        @Schema(description = "客户状态", allowableValues = {"NOT", "NEGOTIATION", "ONGOING", "STOPPED"})
        private String status;
    }

    @Data
    public static class CustomerParam {
        @Schema(description = "用户ID")
        private Long userId;
        @Schema(description = "客户ID")
        private Long id;
        @NotBlank(message = "营业执照图片不能为空")
        @Schema(description = "营业执照图片id")
        private String businessLicenseImage;
        @NotBlank(message = "公司名称不能为空")
        @Schema(description = "公司名称")
        private String name;
        @NotBlank(message = "统一社会信用代码不能为空")
        @Schema(description = "统一社会信用代码")
        private String socialCreditCode;
        @NotBlank(message = "注册地址不能为空")
        @Schema(description = "注册地址")
        private String registerAddress;
        @NotBlank(message = "法定代表人证件正面照不能为空")
        @Schema(description = "法定代表人证件正面照")
        private String certificateFrontImage;
        @NotBlank(message = "法定代表人证件背面照不能为空")
        @Schema(description = "法定代表人证件背面照")
        private String certificateBackImage;
        @NotBlank(message = "法定代表人姓名不能为空")
        @Schema(description = "法定代表人姓名")
        private String representativeName;
        @NotNull(message = "法定代表人证件类型不能为空")
        @Schema(description = "法定代表人证件类型")
        private CertificateTypeEnum certificateType;
        @Schema(description = "法定代表人证件号")
        private String certificateNo;
        @Schema(description = "联系人姓名")
        private String contactName;
        @Schema(description = "联系人电话")
        private String contactMobile;
        @Schema(description = "灵工平台id")
        private Long supplierId;
        @Schema(description = "角色Id")
        private List<Long> roleIds;
        @Schema(description = "公司名称")
        private String shortName;
        @Schema(description = "管理员姓名")
        private String adminName;
        @Schema(description = "管理员手机")
        private String adminMobile;
    }




   /* @Data
    public static class CustomerProfileVo {
        @Schema(description = "用户ID")
        private Long userId;
        @Schema(description = "客户ID")
        private Long customerId;
        @Schema(description = "客户名称")
        private String customerName;
        @Schema(description = "手机号")
        private List<String> phone;
        @Schema(description = "金额")
        private BigDecimal amount;
    }*/

    /*@Data
    public static class TaskRequest {
        @Schema(description = "客户ID")
        private List<Long> customerId;
        @Schema(description = "账户ID")
        private Long accountId;
        @Schema(description = "账单类型")
        public  String businessType;
        @Schema(description = "订单号")
        private String orderNo;
        @NotNull
        private LocalDateTime createTimeBegin;
        @NotNull
        private LocalDateTime createTimeEnd;
        @Schema(description = "发放状态")
        private String payeeAccountNo;
        @Schema(description = "任务类型")
        private TaskType taskType;
        @Schema(description = "批次号")
        private String batchNo;
        @Schema(description = "批次名称")
        private String batchName;
    }*/



    /*@Data
    public static class SetAccountBalanceSmsRequest {
        @Schema(description = "余额阈值")
        @NotNull
        private BigDecimal balanceThreshold;
        @Schema(description = "一行一个手机号，最多5个")
        @NotNull
        private List<String> phoneNumbers;
    }*/

}
