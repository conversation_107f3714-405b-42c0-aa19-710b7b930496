package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.LocalDate;

@Getter
@Setter
@Comment("协议信息表")
@Entity
@Table(name = "t_corporation_protocol")
@AttributeOverrides({
        @AttributeOverride(name = "version", column = @Column(name = "version"))
})
public class CorporationProtocolEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Comment("平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id")
    private Long supplierCorporationId;

    @Size(max = 60)
    @Comment("协议名")
    @Column(name = "protocol_name", length = 60)
    private String protocolName;

    @Size(max = 20)
    @Comment("签约证件号")
    @Column(name = "id_Card", length = 60)
    private String idCard;

    @Comment("模板id")
    @Column(name = "template_id")
    private Long templateId;

    @Comment("客户id")
    @Column(name = "customer_id")
    private Long customerId;

    @Comment("服务合同id")
    @Column(name = "contract_id")
    private Long contractId;

    @Comment("合同开始日期")
    @Column(name = "start_date")
    private LocalDate startDate;

    @Comment("合同结束日期")
    @Column(name = "end_date")
    private LocalDate endDate;

    @Comment("企业静默签")
    @Column(name = "silence_sign")
    private Boolean silenceSign;

    @Size(max = 32)
    @Comment("文档id 要签署的文档id（已填入模板域）")
    @Column(name = "file_id", length = 32)
    private String fileId;

    @Size(max = 32)
    @Comment("合同文档id，有盖章的文档")
    @Column(name = "protocol_file_id", length = 32)
    private String protocolFileId;

    @Size(max = 30)
    @Comment("协议类型")
    @Column(name = "protocol_type", length = 30)
    private String protocolType;

    @Comment("签署状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "sign_status", length = 20)
    private EnumContractSignStatus signStatus;

    @Comment("创建日期")
    @Column(name = "create_date")
    private LocalDate createDate;

    @Comment("签署完成日期")
    @Column(name = "finish_date")
    private LocalDate finishDate;

    @Size(max = 30)
    @Comment("云签模板编号")
    @Column(name = "agent_template", length = 30)
    private String agentTemplate;

    @Comment("云签文件编号")
    @Column(name = "agent_contract_id")
    private Long agentContractId;

    @Comment("创建人员id")
    @Column(name = "create_user_id")
    private Long createUserId;

    @Size(max = 200)
    @Comment("拒签理由")
    @Column(name = "handle_info", length = 200)
    private String handleInfo;

    @Size(max = 1024)
    @Comment("文档上传url")
    @Column(name = "upload_url", length = 1024)
    private String uploadUrl;

    @Size(max = 1024)
    @Comment("文档编辑url")
    @Column(name = "edit_url", length = 1024)
    private String editUrl;

    public CorporationProtocolEntity(TenantInfo tenant) {
        setTenant(tenant);
    }

    public CorporationProtocolEntity() {
    }
}