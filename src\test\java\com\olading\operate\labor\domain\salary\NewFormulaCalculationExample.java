package com.olading.operate.labor.domain.salary;

import com.olading.operate.labor.domain.salary.engine.SalaryTaxCalculationService;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationRequest;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 新公式个税计算示例
 * 
 * 新公式：
 * 1. 累计费用 = 累计收入 * 20%
 * 2. 累计应纳税所得额 = 累计收入 - 累计费用 - 累计减除费用 - 累计免税收入 - 累计依法确定的其他扣除
 * 3. 累计应纳税额 = (累计收入-累计费用-累计免税收入-累计减除费用-累计依法确定的其他扣除) × 预扣率 - 速算扣除数 - 累计减免税额
 * 4. 本期应预扣预缴税额 = 累计应纳税额 - 累计已预缴税额（如果为负数则直接等于0）
 * 5. 本次应预扣预缴税额 = 本期应预扣预缴税额 - 本月已预扣预缴税额
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NewFormulaCalculationExample {
    
    private final SalaryTaxCalculationService salaryTaxCalculationService;
    
    /**
     * 示例：张三连续3个月的薪酬个税计算（新公式）
     */
    public void demonstrateNewFormulaCalculation() {
        log.info("=== 新公式个税计算示例 ===");
        
        String idCard = "320123199001011234";
        Long supplierCorporationId = 1001L;
        
        // 1月份第一笔
        log.info("--- 1月份第一笔 ---");
        TaxCalculationRequest request1 = createRequest(idCard, supplierCorporationId, "2024-01", 
            new BigDecimal("10000"), new BigDecimal("500"), new BigDecimal("200"), BigDecimal.ZERO, BigDecimal.ZERO);
        
        TaxCalculationResult result1 = salaryTaxCalculationService.previewTaxCalculation(request1);
        printCalculationResult("1月份第一笔", request1, result1);
        
        // 1月份第二笔（同月第二次发薪）
        log.info("--- 1月份第二笔 ---");
        TaxCalculationRequest request2 = createRequest(idCard, supplierCorporationId, "2024-01", 
            new BigDecimal("8000"), new BigDecimal("300"), new BigDecimal("100"), new BigDecimal("50"), result1.getCurrentWithholdingTax());
        
        TaxCalculationResult result2 = salaryTaxCalculationService.previewTaxCalculation(request2);
        printCalculationResult("1月份第二笔", request2, result2);
        
        // 2月份第一笔
        log.info("--- 2月份第一笔 ---");
        TaxCalculationRequest request3 = createRequest(idCard, supplierCorporationId, "2024-02", 
            new BigDecimal("12000"), new BigDecimal("200"), new BigDecimal("150"), BigDecimal.ZERO, BigDecimal.ZERO);
        
        TaxCalculationResult result3 = salaryTaxCalculationService.previewTaxCalculation(request3);
        printCalculationResult("2月份第一笔", request3, result3);
        
        // 3月份第一笔（高收入，跨税率级别）
        log.info("--- 3月份第一笔 ---");
        TaxCalculationRequest request4 = createRequest(idCard, supplierCorporationId, "2024-03", 
            new BigDecimal("25000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        
        TaxCalculationResult result4 = salaryTaxCalculationService.previewTaxCalculation(request4);
        printCalculationResult("3月份第一笔", request4, result4);
    }
    
    /**
     * 创建计算请求
     */
    private TaxCalculationRequest createRequest(String idCard, Long supplierCorporationId, String taxPeriod,
                                              BigDecimal payableAmount, BigDecimal taxFreeIncome, 
                                              BigDecimal otherDeductions, BigDecimal taxReliefAmount,
                                              BigDecimal monthlyPrepaidTax) {
        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard(idCard);
        request.setSupplierCorporationId(supplierCorporationId);
        request.setTaxPeriod(taxPeriod);
        request.setPayableAmount(payableAmount);
        request.setTaxFreeIncome(taxFreeIncome);
        request.setOtherDeductions(otherDeductions);
        request.setTaxReliefAmount(taxReliefAmount);
        return request;
    }
    
    /**
     * 打印计算结果
     */
    private void printCalculationResult(String title, TaxCalculationRequest request, TaxCalculationResult result) {
        log.info("{}：", title);
        log.info("  输入数据：");
        log.info("    应发金额：{}", request.getPayableAmount());
        log.info("    免税收入：{}", request.getTaxFreeIncome());
        log.info("    其他扣除：{}", request.getOtherDeductions());
        log.info("    减免税额：{}", request.getTaxReliefAmount());
        
        log.info("  累计数据：");
        log.info("    累计收入：{}", result.getNewAccumulatedData().getAccumulatedIncome());
        log.info("    累计费用（20%）：{}", result.getNewAccumulatedData().getAccumulatedExpenses());
        log.info("    累计减除费用：{}", result.getNewAccumulatedData().getAccumulatedDeductionExpenses());
        log.info("    累计免税收入：{}", result.getNewAccumulatedData().getAccumulatedTaxFreeIncome());
        log.info("    累计其他扣除：{}", result.getNewAccumulatedData().getAccumulatedOtherDeductions());
        log.info("    累计减免税额：{}", result.getNewAccumulatedData().getAccumulatedTaxRelief());
        log.info("    累计应纳税所得额：{}", result.getNewAccumulatedData().getAccumulatedTaxableAmount());
        log.info("    累计应纳税额：{}", result.getNewAccumulatedData().getAccumulatedTaxAmount());
        log.info("    累计已预缴税额：{}", result.getNewAccumulatedData().getAccumulatedPrepaidTax());
        
        log.info("  计算结果：");
        log.info("    本期应纳税额：{}", result.getCurrentTaxAmount());
        log.info("    本次应预扣预缴税额（实际扣款）：{}", result.getCurrentWithholdingTax());
        log.info("    实发金额：{}", result.getNetPayment());
        
        // 计算税负率
        BigDecimal taxRate = result.getTaxRate(request.getPayableAmount());
        log.info("    税负率：{}%", taxRate.multiply(new BigDecimal("100")));
        log.info("");
    }
    
    /**
     * 手工计算验证示例
     */
    public void manualCalculationVerification() {
        log.info("=== 手工计算验证 ===");
        
        // 示例：1月份收入10000元，免税收入500元，其他扣除200元
        BigDecimal payableAmount = new BigDecimal("10000");
        BigDecimal taxFreeIncome = new BigDecimal("500");
        BigDecimal otherDeductions = new BigDecimal("200");
        
        log.info("手工计算过程：");
        log.info("1. 累计收入 = 0 + {} = {}", payableAmount, payableAmount);
        
        BigDecimal accumulatedExpenses = payableAmount.multiply(new BigDecimal("0.20"));
        log.info("2. 累计费用 = {} × 20% = {}", payableAmount, accumulatedExpenses);
        
        BigDecimal accumulatedDeductionExpenses = new BigDecimal("5000"); // 1月份
        log.info("3. 累计减除费用 = 1 × 5000 = {}", accumulatedDeductionExpenses);
        
        BigDecimal accumulatedTaxableAmount = payableAmount
            .subtract(accumulatedExpenses)
            .subtract(accumulatedDeductionExpenses)
            .subtract(taxFreeIncome)
            .subtract(otherDeductions);
        log.info("4. 累计应纳税所得额 = {} - {} - {} - {} - {} = {}", 
                payableAmount, accumulatedExpenses, accumulatedDeductionExpenses, 
                taxFreeIncome, otherDeductions, accumulatedTaxableAmount);
        
        BigDecimal accumulatedTaxAmount = accumulatedTaxableAmount.multiply(new BigDecimal("0.03"));
        log.info("5. 累计应纳税额 = {} × 3% = {}", accumulatedTaxableAmount, accumulatedTaxAmount);
        
        BigDecimal currentWithholdingTax = accumulatedTaxAmount.max(BigDecimal.ZERO);
        log.info("6. 本期应预扣预缴税额 = {} - 0 = {}", accumulatedTaxAmount, currentWithholdingTax);
        
        BigDecimal netPayment = payableAmount.subtract(currentWithholdingTax);
        log.info("7. 实发金额 = {} - {} = {}", payableAmount, currentWithholdingTax, netPayment);
        
        // 使用引擎计算验证
        TaxCalculationRequest request = createRequest("320123199001011234", 1001L, "2024-01", 
            payableAmount, taxFreeIncome, otherDeductions, BigDecimal.ZERO, BigDecimal.ZERO);
        
        TaxCalculationResult result = salaryTaxCalculationService.previewTaxCalculation(request);
        
        log.info("引擎计算结果验证：");
        log.info("  累计应纳税所得额：{} (手工：{})", result.getNewAccumulatedData().getAccumulatedTaxableAmount(), accumulatedTaxableAmount);
        log.info("  累计应纳税额：{} (手工：{})", result.getNewAccumulatedData().getAccumulatedTaxAmount(), accumulatedTaxAmount);
        log.info("  本次应预扣预缴税额：{} (手工：{})", result.getCurrentWithholdingTax(), currentWithholdingTax);
        log.info("  实发金额：{} (手工：{})", result.getNetPayment(), netPayment);
        
        boolean isCorrect = result.getNewAccumulatedData().getAccumulatedTaxableAmount().compareTo(accumulatedTaxableAmount) == 0 &&
                           result.getCurrentWithholdingTax().compareTo(currentWithholdingTax) == 0 &&
                           result.getNetPayment().compareTo(netPayment) == 0;
        
        log.info("计算结果验证：{}", isCorrect ? "✓ 正确" : "✗ 错误");
    }
    
    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        try {
            demonstrateNewFormulaCalculation();
            manualCalculationVerification();
        } catch (Exception e) {
            log.error("运行新公式计算示例时发生错误", e);
        }
    }
}