package com.olading.operate.labor.domain.corporation;

import cn.hutool.json.JSONUtil;
import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.util.ArrayList;

@Getter
@Setter
@Comment("作业主体配置")
@Entity
@Table(name = "t_corporation_config")
public class CorporationConfigEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;


    @Comment("最小年龄限制")
    @Column(name = "min_age_limit", nullable = false)
    private Integer minAgeLimit;


    @Comment("最大年龄限制")
    @Column(name = "max_age_limit", nullable = false)
    private Integer maxAgeLimit;

    @Comment("发票类目配置(json)")
    @Lob
    @Column(name = "invoice_category")
    private String invoiceCategory;

    @Comment("增值税起征点(万元)")
    @Column(name = "vat_start", precision = 19, scale = 2)
    private BigDecimal vatStart;

    @Comment("增值税税率(%)")
    @Column(name = "vat_rate", precision = 19, scale = 2)
    private BigDecimal vatRate;

    @Comment("附加税配置(json) 固定配置 SurtaxCodeEnum里的三项 ")
    @Lob
    @Column(name = "surtax_data")
    private String surtaxData;

    public CorporationConfigEntity(TenantInfo tenant,Long supplierId, Long supplierCorporationId) {
        setTenant(tenant);
        setSupplierId(supplierId);
        setSupplierCorporationId(supplierCorporationId);
        final ArrayList<SurtaxData> list = new ArrayList<>();
        list.add(SurtaxData.builder().surtaxCode(SurtaxCodeEnum.EDUCATION_SURCHARGE).name(SurtaxCodeEnum.EDUCATION_SURCHARGE.name()).rate(BigDecimal.ZERO).discount_rate(BigDecimal.ZERO).build());
        list.add(SurtaxData.builder().surtaxCode(SurtaxCodeEnum.URBAN_MAINTENANCE_TAX).name(SurtaxCodeEnum.URBAN_MAINTENANCE_TAX.name()).rate(BigDecimal.ZERO).discount_rate(BigDecimal.ZERO).build());
        list.add(SurtaxData.builder().surtaxCode(SurtaxCodeEnum.LOCAL_EDUCATION_SURCHARGE).name(SurtaxCodeEnum.LOCAL_EDUCATION_SURCHARGE.name()).rate(BigDecimal.ZERO).discount_rate(BigDecimal.ZERO).build());
        setSurtaxData(JSONUtil.toJsonStr(list));
    }

    protected CorporationConfigEntity() {
    }

}