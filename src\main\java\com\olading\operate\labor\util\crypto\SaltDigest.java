package com.olading.operate.labor.util.crypto;

import org.apache.commons.codec.digest.DigestUtils;

import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Arrays;

public class SaltDigest {


    /**
     * 加盐哈希算法
     *
     * @param plain 明文
     * @return hash密文
     */
    public static byte[] digest(byte[] plain, MessageDigest digest) {
        SecureRandom random = new SecureRandom();

        byte[] salt = new byte[32];
        random.nextBytes(salt);
        return digest(plain, salt, digest);
    }

    /**
     * SM3加盐哈希算法数据验证
     *
     * @param plain      明文
     * @param ciphertext 密文
     * @return 是否验证一致
     */
    public static boolean sm3Verify(byte[] plain, byte[] ciphertext, MessageDigest digest) {
        byte[] salt = Arrays.copyOf(ciphertext, 32);
        byte[] hash = Arrays.copyOfRange(ciphertext, 32, ciphertext.length);
        byte[] hash2 = digest(plain, ciphertext, digest);

        return Arrays.equals(hash, hash2);
    }

    public static byte[] digest(byte[] plain, byte[] salt) {
        byte[] hash = DigestUtils.getSha512Digest().digest(plain);
        byte[] ciphertext = Arrays.copyOf(salt, salt.length + hash.length);
        System.arraycopy(hash, 0, ciphertext, salt.length, hash.length);
        return ciphertext;
    }

    public static byte[] digest(byte[] plain, byte[] salt, MessageDigest digest) {
        byte[] hash = digest.digest(plain);
        byte[] ciphertext = Arrays.copyOf(salt, salt.length + hash.length);
        System.arraycopy(hash, 0, ciphertext, salt.length, hash.length);
        return ciphertext;
    }

}
