package com.olading.operate.labor.util.crypto;

import org.apache.commons.codec.digest.DigestUtils;

import java.security.MessageDigest;
import java.util.Arrays;

public class Digests {

    public static Digest sha512withSalt() {
        return withSalt(sha512(), 32);
    }

    public static Digest sha512() {
        return digest(DigestUtils.getSha512Digest());
    }

    public static Digest withSalt(Digest digest, int saltSize) {
        return new Salt2Digest(digest, saltSize);
    }

    public static Digest digest(MessageDigest digest) {
        return new MessageDigestDigest(digest);
    }

    public static void main(String[] args) {

        String s = "hello";
        Digest digest = sha512withSalt();
        byte[] b = digest.digest(s.getBytes());

        boolean r = digest.verify(s.getBytes(), b);
        System.out.println(r);
    }

    private static class MessageDigestDigest implements Digest {

        private final MessageDigest digest;

        public MessageDigestDigest(MessageDigest digest) {
            this.digest = digest;
        }

        @Override
        public byte[] digest(byte[] plaintext) {
            return digest.digest(plaintext);
        }

        @Override
        public boolean verify(byte[] plaintext, byte[] signature) {
            return Arrays.equals(digest.digest(plaintext), signature);
        }

    }
}
