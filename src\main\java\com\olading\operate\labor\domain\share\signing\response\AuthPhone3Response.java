package com.olading.operate.labor.domain.share.signing.response;


import com.olading.operate.labor.domain.share.signing.enums.AuthResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "运营商三要素认证响应参数")
public class AuthPhone3Response {

    @Schema(description = "认证结果", required = true)
    private AuthResult result;

}
