package com.olading.operate.labor;

import com.olading.operate.labor.domain.share.customer.CustomerManager;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

@SpringBootTest(classes = {OperateLaborApplication.class, TestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public abstract class BaseTest {

    private static AtomicLong SEQ = new AtomicLong(10000);

    @Autowired
    protected EntityManager em;

    @Autowired
    protected PlatformTransactionManager transactionManager;

    @Autowired
    protected AppProperties properties;

    @Autowired
    protected CustomerManager customerManager;


    protected long random() {
        return new Random().nextLong();
    }

    protected int random(int min, int max) {
        return new Random().nextInt(max - min + 1) + min;
    }

    protected long uniqueId() {
        return SEQ.getAndIncrement();
    }

    protected String uuid() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    protected String uid(int size) {
        return uuid().substring(0, size);
    }

    protected <T> T withTransaction(TransactionCallback<T> action) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        return transactionTemplate.execute(action);
    }

    @BeforeEach
    final protected void beforeEach2() {
        this.beforeEach();
    }

    protected void beforeEach() {

    }
}
