package com.olading.operate.labor.domain.share.signing.request;


import com.olading.operate.labor.domain.share.signing.enums.ImageFormat;
import com.olading.operate.labor.domain.share.signing.enums.SignImageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "生成签章图片请求参数")
public class BuildSignImgRequest extends BaseRequest<BuildSignImgRequest> {

    @Schema(description = "章类型", required = true)
    private SignImageType signType;
    @Schema(description = "签章图片内内容", required = true)
    private String content;
    @Schema(description = "图片格式：【JPG】", required = true)
    private ImageFormat format;

}
