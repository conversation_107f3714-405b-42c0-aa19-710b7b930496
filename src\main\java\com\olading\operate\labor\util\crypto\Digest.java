package com.olading.operate.labor.util.crypto;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public interface Digest {

    /**
     * 签名
     *
     * @param plaintext 原文
     * @return signature
     */
    byte[] digest(byte[] plaintext);

    /**
     * 验证签名
     *
     * @param plaintext 原文
     * @param signature 签名
     */
    boolean verify(byte[] plaintext, byte[] signature);

    default String digest(String plaintext) {
        return Base64.getEncoder().encodeToString(digest(plaintext.getBytes(StandardCharsets.UTF_8)));
    }

    default boolean verify(String plaintext, String signature) {
        return verify(plaintext.getBytes(StandardCharsets.UTF_8), Base64.getDecoder().decode(signature));
    }
}
