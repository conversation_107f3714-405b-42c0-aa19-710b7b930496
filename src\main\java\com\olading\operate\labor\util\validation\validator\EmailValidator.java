package com.olading.operate.labor.util.validation.validator;


import com.lanmaoly.util.lang.ValidateUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Email;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class EmailValidator extends BaseValidator<Email> {
    public EmailValidator() {}

    public EmailValidator(String name) {
        setName(name);
    }

    @Override
    public void initialize(Email constraintAnnotation) {
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        super.initialize(constraintAnnotation);
    }

    @Override
    protected boolean constraintCheck(Object o) {
        if (!ValidateUtils.isEmail(String.valueOf(o))) {
            throw new ValidationException(getName(), "输入不合规范");
        }

        return true;
    }
}
