package com.olading.operate.labor.domain.share.archive;

import com.olading.boot.util.Misc;
import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import org.hibernate.annotations.Comment;

/**
 * 存储小文件
 */
@Table(name = "t_archive")
@Entity
public class ArchiveEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Comment("文件名")
    @Column(name = "name", length = 50)
    private String name;

    @Comment("已压缩的原始数据,使用GZ压缩")
    @Lob
    @Column(name = "compressed_data", length = 17000000)
    private byte[] compressedData;

    @Comment("数据大小")
    @Column(name = "data_size")
    private Long dataSize;

    @Comment("sha256")
    @Column(name = "sha256", length = 64)
    private String sha256;


    /**
     * 解压缩后的数据
     */
    @Transient
    private transient byte[] plainData;

    public ArchiveEntity(String name, byte[] data, String sha256) {
        this.name = name;
        this.setData(data);
        this.sha256 = sha256;
    }

    protected ArchiveEntity() {
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public long getDataSize() {
        return dataSize;
    }

    public byte[] getData() {
        return Misc.gunzip(compressedData);
    }

    private void setData(byte[] data) {
        this.dataSize = (long) data.length;
        this.compressedData = Misc.gzip(data);
        this.plainData = null;
    }

    public String getSha256() {
        return sha256;
    }

}
