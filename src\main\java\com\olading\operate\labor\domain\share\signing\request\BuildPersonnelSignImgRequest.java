package com.olading.operate.labor.domain.share.signing.request;


import com.olading.operate.labor.domain.share.signing.enums.ImageFormat;
import com.olading.operate.labor.domain.share.signing.enums.SignStyleType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "生成签章图片(自定义签章图片)请求参数")
public class BuildPersonnelSignImgRequest extends BaseRequest<BuildPersonnelSignImgRequest> {

    @Schema(description = "章类型", required = true)
    private SignStyleType signStyleType;
    @Schema(description = "签章图片内内容", required = true)
    private String content;
    @Schema(description = "图片格式：【JPG】", required = true)
    private ImageFormat format;
    @Schema(description = "字体：【SimSun】宋体，【SimHei】黑体，【SimKai】楷体。默认为宋体")
    private String fontName;
    @Schema(description = "字体颜色:【RED】红色，【BLACK】黑色。默认为红色")
    private String fontColor;
    @Schema(description = "字号：20~96")
    private Integer fontSize;



}
