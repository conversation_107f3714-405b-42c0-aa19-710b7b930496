package com.olading.operate.labor.domain.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单其他费用明细视图对象
 */
@Data
@Schema(description = "账单其他费用明细信息")
public class BillOtherFeeDetailVO {

    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "账单主表ID")
    private Long billMasterId;

    @Schema(description = "账单分类ID")
    private Long billCategoryId;

    @Schema(description = "人员姓名")
    private String laborName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "费用名称")
    private String feeName;

    @Schema(description = "费用金额")
    private BigDecimal feeAmount;

    @Schema(description = "费用用途")
    private String feePurpose;

    @Schema(description = "账单月份")
    private LocalDate billMonth;

    @Schema(description = "费用类别")
    private String feeCategory;

    @Schema(description = "费用说明")
    private String feeDescription;

    @Schema(description = "导入批次号")
    private String importBatchNo;

    @Schema(description = "备注")
    private String remark;
}