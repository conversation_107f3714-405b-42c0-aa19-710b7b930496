package com.olading.operate.labor.util.excel;

import com.olading.operate.labor.util.textfilter.TextFilter;
import com.olading.operate.labor.util.validation.validator.Validator;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel列的定义
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Documented

public @interface ExcelColumn {

    /**
     * 导出Excel时所在的列(从1开始）
     * @return
     */
    int orderNum() default 0;

    /**
     * 分组名
     * @return
     */
    String group() default "";

    /**
     * Item显示的名字，必须唯一
     */
    String name() default "";

    /**
     * 默认导出的值
     */
    String nullWriteValue() default "";

    /**
     * 导出表格列的宽度
     * @return
     */
    int width() default -1;

    /**
     * 是否属于其他数据(存于ExcelRow.otherData内）
     * @return
     */
    boolean saveToOther() default false;


    /**
     * 是否必填
     */
    boolean required() default false;

    /**
     * 是否唯一，不可重复
     * @return
     */
    boolean unique() default false;

    /**
     * 取值枚举，options.length > 0时，取值使用枚举
     * @return
     */
    String[] options() default {};

    /**
     * 单元格数据校验的公式
     * @return
     */
    String validationFormula() default "";

    /**
     * 备注信息
     * @return
     */
    String comment() default "";

    /**
     * 自定义数据过滤器（校验前执行），覆盖自定义其它注解的默认过滤器
     */
    Class<? extends TextFilter>[] filters() default { };

    /**
     * 自定义数据格式化（校验后执行），覆盖自定义其它注解的默认格式化
     */
    Class<? extends TextFilter>[] formatters() default { };

    /**
     * 自定义数据校验器，覆盖自定义其它注解的默认校验器
     */
    Class<? extends Validator>[] validators() default { };
}