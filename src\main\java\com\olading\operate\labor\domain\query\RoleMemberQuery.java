package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.share.authority.QRoleEntity;
import com.olading.operate.labor.domain.share.authority.QRoleMemberEntity;
import com.olading.operate.labor.domain.share.authority.QSupplierMemberEntity;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.olading.operate.labor.domain.share.authority.RoleMemberEntity;
import com.olading.operate.labor.domain.share.authority.SupplierMemberEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Set;

public class RoleMemberQuery implements EntityQuery<QueryFilter<RoleMemberQuery.Filters>, RoleMemberQuery.Record> {

    private final QRoleMemberEntity t1 = QRoleMemberEntity.roleMemberEntity;
    private final QSupplierMemberEntity t2 = QSupplierMemberEntity.supplierMemberEntity;
    private final QRoleEntity t3 = QRoleEntity.roleEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t2.supplierId.eq(filters.getFilters().getSupplierId()));
        }
        if (filters.getFilters().getRoleId() != null) {
            criteria.and(t1.roleId.eq(filters.getFilters().getRoleId()));
        }
        if (filters.getFilters().getNameOrCellphone() != null) {
            criteria.and(
                    t2.name.like(JpaUtils.fullLike(filters.getFilters().getNameOrCellphone()))
                    .or(t2.cellphone.like(JpaUtils.fullLike(filters.getFilters().getNameOrCellphone())))
            );
        }
        if (CollectionUtils.isNotEmpty(filters.getFilters().getSubjectId())) {
            criteria.and(t1.subjectId.in(filters.getFilters().getSubjectId()));
        }

        query.select(t1, t2, t3)
                .distinct()
                .from(t1)
                .innerJoin(t2).on(t1.subjectId.eq(t2.id))
                .innerJoin(t3).on(t1.roleId.eq(t3.id));

        query.where(criteria);
    }

    @Override
    public Record transform(Object v) {
        Tuple tuple = (Tuple) v;

        SupplierMemberEntity supplierMember = tuple.get(t2);
        RoleMemberEntity roleMember = tuple.get(t1);
        RoleEntity role = tuple.get(t3);

        Record record = new Record();
        record.setSupplierMember(supplierMember);
        record.setRoleMember(roleMember);
        record.setRole(role);

        return record;
    }

    @Data
    public static class Filters {

        private Long supplierId;

        private Long roleId;

        private Set<Long> subjectId;

        private String nameOrCellphone;
    }

    @Data
    public static class Record {
        private RoleEntity role;
        private SupplierMemberEntity supplierMember;
        private RoleMemberEntity roleMember;
    }

}
