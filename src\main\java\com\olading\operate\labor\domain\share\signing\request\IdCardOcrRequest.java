package com.olading.operate.labor.domain.share.signing.request;


import com.olading.operate.labor.domain.share.signing.enums.ImageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "身份证OCR请求参数")
public class IdCardOcrRequest extends BaseRequest<IdCardOcrRequest> {

    @Schema(description = "流水号", required = true)
    private String flowNo;
    @Schema(description = "身份证图片，jpg格式，使用base64编码", required = true)
    private String image;
    @Schema(description = "身份证图片类型 头像面：【HEAD】国徽面：【EMBLEM】", required = true)
    private ImageType imageType;

}
