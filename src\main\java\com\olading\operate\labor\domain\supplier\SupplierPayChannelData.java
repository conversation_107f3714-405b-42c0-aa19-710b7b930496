package com.olading.operate.labor.domain.supplier;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/9 15:55
 */
@Data
public class SupplierPayChannelData {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "灵工平台id")
    private Long supplierId;

    @Schema(description = "通道编码")
    private String payChannel;

    @Schema(description = "通道配置id")
    private Long payChannelId;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "通道配置项(json)")
    private List<PayChannelConfig> payChannelConfig;

    public SupplierPayChannelData(SupplierPayChannelEntity supplierPayChannelEntity, PayChannelConfigEntity payChannelEntity){
        if(supplierPayChannelEntity != null){
            this.id = supplierPayChannelEntity.getId();
            this.payChannel = supplierPayChannelEntity.getPayChannel();
        }
        if(payChannelEntity != null){
            this.payChannelId = payChannelEntity.getId();
            this.channelName = payChannelEntity.getChannelName();
            this.payChannelConfig = JSONUtil.toList(payChannelEntity.getPayChannelConfig(), PayChannelConfig.class);
        }

    }

    public SupplierPayChannelData(){}

    @Data
    public static class  PayChannelConfig{
        @Schema(description = "参数名")
        private String paramName;
        @Schema(description = "参数值")
        private String paramValue;
        @Schema(description = "是否必填")
        private boolean required;
        @Schema(description = "参数描述")
        private String paramDesc;
    }


    public static void main(String[] args) {


        /**
         * 招商云直连
         * sm4PrivateKey SM4私钥
         * smPrivateKey 国密私钥
         * encryType=SM4  加密方式
         * branchName 支行名称
         * bankBranchNo 支行号
         * accountName 开户名称
         * accountNo 银行账户
         * uid 招商银行用户ID
         * rsaPublicKey 银行公钥
         * whiteChannelCode 网银BUSMOD配置
         *
         * 平安银行
         * accountName 开户名称
         * accountNo 银行账户
         * uid 商户ID
         */



        final PayChannelConfig payChannelConfig1 = new PayChannelConfig();
        payChannelConfig1.setParamDesc("主账户号");
        payChannelConfig1.setParamName("accountNo");
        payChannelConfig1.setRequired(true);

        final PayChannelConfig payChannelConfig2 = new PayChannelConfig();
        payChannelConfig2.setParamDesc("主账户名称");
        payChannelConfig2.setParamName("accountName");
        payChannelConfig2.setRequired(true);

        final PayChannelConfig payChannelConfig3 = new PayChannelConfig();
        payChannelConfig3.setParamDesc("商户ID");
        payChannelConfig3.setParamName("uid");
        payChannelConfig3.setRequired(true);

        final PayChannelConfig payChannelConfig4 = new PayChannelConfig();
        payChannelConfig4.setParamDesc("SM4私钥");
        payChannelConfig4.setParamName("sm4PrivateKey");
        payChannelConfig4.setRequired(true);

        final PayChannelConfig payChannelConfig5 = new PayChannelConfig();
        payChannelConfig5.setParamDesc("国密私钥");
        payChannelConfig5.setParamName("smPrivateKey");
        payChannelConfig5.setRequired(true);

        final PayChannelConfig payChannelConfig6 = new PayChannelConfig();
        payChannelConfig6.setParamDesc("支行名称");
        payChannelConfig6.setParamName("branchName");
        payChannelConfig6.setRequired(true);

        final PayChannelConfig payChannelConfig7 = new PayChannelConfig();
        payChannelConfig7.setParamDesc("支行号");
        payChannelConfig7.setParamName("bankBranchNo");
        payChannelConfig7.setRequired(true);

        final PayChannelConfig payChannelConfig8 = new PayChannelConfig();
        payChannelConfig8.setParamDesc("银行公钥");
        payChannelConfig8.setParamName("rsaPublicKey");
        payChannelConfig8.setRequired(true);

        final PayChannelConfig payChannelConfig9 = new PayChannelConfig();
        payChannelConfig9.setParamDesc("网银BUSMOD配置");
        payChannelConfig9.setParamName("whiteChannelCode");
        payChannelConfig9.setRequired(true);

        final List<PayChannelConfig> list = List.of(payChannelConfig1, payChannelConfig2,payChannelConfig3,payChannelConfig4,payChannelConfig5,payChannelConfig6,payChannelConfig7,payChannelConfig8,payChannelConfig9);
        final String jsonStr = JSONUtil.toJsonStr(list);
        System.out.println(jsonStr);
    }

}
