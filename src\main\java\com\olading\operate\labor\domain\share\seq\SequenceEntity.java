package com.olading.operate.labor.domain.share.seq;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 限额计数
 */
@Table(name = "t_sequence", indexes = {

})
@Entity
public class SequenceEntity extends BaseEntity {

    @Id
    @Column(name = "id", length = 400)
    private String name;

    @Column(name = "next_value", nullable = false)
    private Long nextValue;

    public SequenceEntity(String name, long start) {
        this.name = name;
        this.nextValue = start;
    }

    protected SequenceEntity() {
    }

    public String getName() {
        return name;
    }

    public long getNextValue() {
        return nextValue;
    }

    public void increase(long delta) {
        this.nextValue += delta;
    }
}
