package com.olading.operate.labor.domain.share.info;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.Comment;

/**
 * 人
 */
@Table(name = "t_person_info", indexes = {
        @Index(name = "i_person_info_1", columnList = "owner_id, owner_type", unique = true)
})
@Entity
public class PersonInfoEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Size(max = 20)
    @Comment("人员姓名")
    @Column(name = "name", length = 20)
    private String name;

    @Size(max = 11)
    @Comment("手机号")
    @Column(name = "cellphone", length = 11)
    private String cellphone;

    @Embedded
    private OwnedByFragment ownedBy;

    @Size(max = 20)
    @Comment("身份证号")
    @Column(name = "id_card", length = 20)
    private String idCard;

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public PersonInfoEntity(OwnerType ownerType, Long ownerId) {
        this.ownedBy = new OwnedByFragment(ownerType, ownerId);
    }

    protected PersonInfoEntity() {
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    public String getCellphone() {
        return cellphone;
    }

    void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    public OwnedByFragment getOwnedBy() {
        return ownedBy;
    }
}
