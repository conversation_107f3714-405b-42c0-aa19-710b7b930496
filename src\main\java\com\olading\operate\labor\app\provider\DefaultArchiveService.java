package com.olading.operate.labor.app.provider;

import com.lanmaoly.cloud.archive.ArchiveFingerprintType;
import com.lanmaoly.cloud.archive.ArchiveInfo;
import com.lanmaoly.cloud.archive.ArchiveMetadata;
import com.lanmaoly.cloud.archive.PersistParameter;
import com.lanmaoly.cloud.archive.Utils;
import com.lanmaoly.cloud.archive.exception.ArchiveException;
import com.lanmaoly.cloud.archive.rpc3.Archive3RpcService;
import com.lanmaoly.cloud.archive.rpc3.DeleteRequest;
import com.lanmaoly.cloud.archive.rpc3.DeleteResponse;
import com.lanmaoly.cloud.archive.rpc3.DescribeArchiveRequest;
import com.lanmaoly.cloud.archive.rpc3.DescribeArchiveResponse;
import com.lanmaoly.cloud.archive.rpc3.PrepareUploadRequest;
import com.lanmaoly.cloud.archive.rpc3.PrepareUploadResponse;
import com.lanmaoly.cloud.archive.rpc3.UpdateArchiveInfoRequest;
import com.lanmaoly.cloud.archive.rpc3.UpdateArchiveInfoResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestOperations;

import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;

@RequiredArgsConstructor
@Component
public class DefaultArchiveService {

    private static String ADDRESS = "lanmaoly-cloud-archive-service";

    private final String address = ADDRESS;

    private final RestOperations restOperations;

    private final Archive3RpcService archiveRpcService;

    public String persist(PersistParameter parameter) {
        try {
            PrepareUploadRequest request = new PrepareUploadRequest();
            request.setName(parameter.getInfo().getName());
            request.setRemark(parameter.getInfo().getRemark());
            request.setSize(parameter.getSize());
            request.setExpired(parameter.getExpired());
            request.setMime(parameter.getMime());
            request.setProperties(parameter.getProperties());

            PrepareUploadResponse response = archiveRpcService.prepareUpload(request);
            Utils.checkError(response.getHeader());

            String uploadUrl = response.getUploadUrl();

            restOperations.execute(new URI(uploadUrl), HttpMethod.POST, request1 -> {
                IOUtils.copy(parameter.getInputStream(), request1.getBody());
            }, response1 -> {
                if (response1.getStatusCode() != HttpStatus.OK) {
                    throw new ArchiveException("上传文件出错");
                }
                return 1;
            });

            return response.getId();
        } catch (URISyntaxException e) {
            throw new ArchiveException(e.getMessage(), e);
        }
    }


    public void delete(String id) {
        DeleteRequest request = new DeleteRequest();
        request.setId(id);
        DeleteResponse response = archiveRpcService.deleteArchive(request);
        Utils.checkError(response.getHeader());
    }


    public void update(String id, ArchiveInfo info) {
        UpdateArchiveInfoRequest request = new UpdateArchiveInfoRequest();
        request.setId(id);
        request.setRemark(info.getRemark());
        UpdateArchiveInfoResponse response = archiveRpcService.updateArchiveInfo(request);
        Utils.checkError(response.getHeader());
    }



    public ArchiveMetadata describe(String id) {
        DescribeArchiveRequest request = new DescribeArchiveRequest();
        request.setId(id);
        DescribeArchiveResponse response = archiveRpcService.describeArchive(request);
        Utils.checkError(response.getHeader());

        ArchiveInfo info = new ArchiveInfo(response.getName(), response.getRemark());
        return new ArchiveMetadata(
                response.getId(), info, response.getSize(), response.getWidth(),
                response.getHeight(), ArchiveFingerprintType.valueOf(response.getFingerprintType()),
                response.getFingerprint(), response.getCreateTime(), response.getExpired(),
                response.getProperties(), response.getUrl());
    }

    public void open(String id, OutputStream output) {
        try {
            String url = "http://" + address + "/api/v3/open/" + id;

            restOperations.execute(new URI(url), HttpMethod.POST, request -> {
            }, response -> {
                IOUtils.copy(response.getBody(), output);
                return 1;
            });
        } catch (URISyntaxException e) {
            throw new ArchiveException(e.getMessage(), e);
        }
    }

}
