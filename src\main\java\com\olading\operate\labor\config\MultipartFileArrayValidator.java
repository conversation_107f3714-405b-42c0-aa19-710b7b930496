package com.olading.operate.labor.config;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.web.multipart.MultipartFile;

import java.util.Set;

public class MultipartFileArrayValidator implements ConstraintValidator<ValidMultipartFileArray, MultipartFile[]> {

    private int maxCount;
    private long maxSize;
    private Set<String> allowedTypes;

    @Override
    public void initialize(ValidMultipartFileArray constraintAnnotation) {
        this.maxCount = constraintAnnotation.maxCount();
        this.maxSize = constraintAnnotation.maxSizeInBytes();
        this.allowedTypes = Set.of(constraintAnnotation.allowedTypes());
    }

    @Override
    public boolean isValid(MultipartFile[] files, ConstraintValidatorContext context) {
        if (files == null || files.length == 0) {
            return true;
        }

        if (files.length > maxCount) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("文件数量不能超过" + maxCount)
                    .addConstraintViolation();
            return false;
        }

        long totalSize = 0;
        for (MultipartFile file : files) {
            if (file == null || file.isEmpty()) {
                continue;
            }

            // 校验文件大小
            if (file.getSize() > maxSize) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("文件大小不能超过" + (maxSize / (1024 * 1024)) + "MB")
                        .addConstraintViolation();
                return false;
            }
            totalSize += file.getSize();

            // 校验文件类型
            String contentType = file.getContentType();
            if (contentType == null || !allowedTypes.contains(contentType)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("不允许的文件类型: " + contentType)
                        .addConstraintViolation();
                return false;
            }
        }

        if (totalSize > maxSize) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("文件总大小不能超过" + (maxSize / (1024 * 1024)) + "MB")
                    .addConstraintViolation();
            return false;
        }

        return true;
    }
}