package com.olading.operate.labor.domain.share.info;

import lombok.Data;


@Data
public class PersonInfoData {

    private Long id;

    private String ownerId;

    private String name;

    private String idCard;

    private String cellphone;

    public PersonInfoData(PersonInfoEntity entity) {
        id = entity.getId();
        ownerId = entity.getOwnedBy().getOwnerId().toString();
        name = entity.getName();
        cellphone = entity.getCellphone();
        idCard = entity.getIdCard();
    }
    public PersonInfoData() {
    }
}
