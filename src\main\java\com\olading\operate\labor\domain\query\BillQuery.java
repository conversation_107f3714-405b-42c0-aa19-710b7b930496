package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.bill.BillMasterEntity;
import com.olading.operate.labor.domain.bill.BillMasterStatus;
import com.olading.operate.labor.domain.bill.QBillMasterEntity;
import com.olading.operate.labor.domain.bill.vo.BillMasterVO;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * 账单查询 - V2版本
 * 完全按照ProxyOrderQuery的规范实现
 */
public class BillQuery implements EntityQuery<QueryFilter<BillQuery.Filters>, BillMasterVO> {

    private final QBillMasterEntity t1 = QBillMasterEntity.billMasterEntity;
    private final QCustomerEntity t2 = QCustomerEntity.customerEntity;
    private final QBusinessContractEntity t3 = QBusinessContractEntity.businessContractEntity;
    private final QSupplierCorporationEntity t4 = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.eq(filters.getFilters().getId()));
        }
        
        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t1.supplierId.eq(filters.getFilters().getSupplierId()));
        }
        
        if (filters.getFilters().getCustomerId() != null) {
            criteria.and(t1.customerId.eq(filters.getFilters().getCustomerId()));
        }
        
        if (filters.getFilters().getSupplierCorporationId() != null) {
            criteria.and(t1.supplierCorporationId.eq(filters.getFilters().getSupplierCorporationId()));
        }
        
        if (filters.getFilters().getContractId() != null) {
            criteria.and(t1.contractId.eq(filters.getFilters().getContractId()));
        }
        
        if (StringUtils.isNotBlank(filters.getFilters().getBillNo())) {
            criteria.and(t1.billNo.like(JpaUtils.fullLike(filters.getFilters().getBillNo())));
        }
        
        if (filters.getFilters().getBillMonthStart() != null) {
            criteria.and(t1.billMonth.goe(filters.getFilters().getBillMonthStart()));
        }
        
        if (filters.getFilters().getBillMonthEnd() != null) {
            criteria.and(t1.billMonth.loe(filters.getFilters().getBillMonthEnd()));
        }
        
        if (filters.getFilters().getBillStatus() != null) {
            criteria.and(t1.billStatus.eq(filters.getFilters().getBillStatus()));
        }
        
        if (StringUtils.isNotBlank(filters.getFilters().getCustomerName())) {
            criteria.and(t2.name.like(JpaUtils.fullLike(filters.getFilters().getCustomerName())));
        }
        
        if (StringUtils.isNotBlank(filters.getFilters().getContractName())) {
            criteria.and(t3.name.like(JpaUtils.fullLike(filters.getFilters().getContractName())));
        }
        
        if (filters.getFilters().getCustomerIds() != null) {
            criteria.and(t1.customerId.in(filters.getFilters().getCustomerIds()));
        }
        
        if (filters.getFilters().getContractIds() != null) {
            criteria.and(t1.contractId.in(filters.getFilters().getContractIds()));
        }

        // 默认只查询未删除的记录
        criteria.and(t1.deleted.eq(false));

        query.select(t1, t2, t3, t4)
                .from(t1)
                .leftJoin(t2).on(t1.customerId.eq(t2.id))
                .leftJoin(t3).on(t1.contractId.eq(t3.id))
                .leftJoin(t4).on(t1.supplierCorporationId.eq(t4.id));
        
        query.where(criteria);
    }

    @Override
    public BillMasterVO transform(Object v) {
        Tuple tuple = (Tuple) v;
        BillMasterEntity billMaster = tuple.get(t1);
        CustomerEntity customer = tuple.get(t2);
        BusinessContractEntity contract = tuple.get(t3);
        SupplierCorporationEntity corporation = tuple.get(t4);

        BillMasterVO vo = new BillMasterVO();
        BeanUtils.copyProperties(billMaster, vo);
        vo.setBillStatusDesc(billMaster.getBillStatus().getDescription());

        if (customer != null) {
            vo.setCustomerName(customer.getName());
        }

        if (contract != null) {
            vo.setContractName(contract.getName());
        }

        if (corporation != null) {
            vo.setSupplierCorporationName(corporation.getName());
        }

        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        if ("createTime".equals(column)) {
            return t1.createTime;
        }
        if ("billMonth".equals(column)) {
            return t1.billMonth;
        }
        return null;
    }

    @Data
    @NoArgsConstructor
    public static class Filters {
        private Long id;
        private Long supplierId;
        private Long customerId;
        private Long supplierCorporationId;
        private Long contractId;
        private String billNo;
        private LocalDate billMonthStart;
        private LocalDate billMonthEnd;
        private BillMasterStatus billStatus;
        private String customerName;
        private String contractName;
        private List<Long> customerIds;
        private List<Long> contractIds;
    }
}