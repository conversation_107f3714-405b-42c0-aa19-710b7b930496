package com.olading.operate.labor.domain.share.submission;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.util.ThreadPoolUtil;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborVo;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;

@Transactional
@Component
@RequiredArgsConstructor
@Slf4j
public class InfoSubmissionLaborManager {

    private final EntityManager em;
    private final FileManager fileManager;
    private final PlatformTransactionManager transactionManager;

    /**
     * 更新人员信息报送记录
     */
    public InfoSubmissionLaborEntity updateInfoSubmissionLabor(InfoSubmissionLaborVo vo) {
        InfoSubmissionLaborEntity entity = getInfoSubmissionLaborById(vo.getId());
        copyToEntity(vo, entity);
        return em.merge(entity);
    }

    /**
     * 查询人员信息报送记录详情
     */
    public InfoSubmissionLaborVo queryInfoSubmissionLabor(Long id) {
        InfoSubmissionLaborEntity entity = getInfoSubmissionLaborById(id);
        InfoSubmissionLaborVo vo = new InfoSubmissionLaborVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 删除人员信息报送记录
     */
    public void deleteInfoSubmissionLabor(Long id) {
        InfoSubmissionLaborEntity entity = getInfoSubmissionLaborById(id);
        em.remove(entity);
    }

    /**
     * 根据ID获取人员信息报送记录
     */
    public InfoSubmissionLaborEntity getInfoSubmissionLaborById(Long id) {
        InfoSubmissionLaborEntity entity = em.find(InfoSubmissionLaborEntity.class, id);
        if (entity == null) {
            throw new BusinessException("人员信息报送记录不存在");
        }
        return entity;
    }
    /**
     * 复制VO到Entity
     */
    private void copyToEntity(InfoSubmissionLaborVo vo, InfoSubmissionLaborEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
        if (vo.getReportStatus() != null) {
            entity.setReportStatus(vo.getReportStatus());
        }
        if (vo.getRegistrationLicenseObtained() != null) {
            entity.setRegistrationLicenseObtained(vo.getRegistrationLicenseObtained());
        }
        if (vo.getName() != null) {
            entity.setName(vo.getName());
        }
        if (vo.getUnifiedSocialCreditCode() != null) {
            entity.setUnifiedSocialCreditCode(vo.getUnifiedSocialCreditCode());
        }
        if (vo.getProfessionalServiceAgencyFlag() != null) {
            entity.setProfessionalServiceAgencyFlag(vo.getProfessionalServiceAgencyFlag());
        }
        if (vo.getLaborName() != null) {
            entity.setLaborName(vo.getLaborName());
        }
        if (vo.getCertificateType() != null) {
            entity.setCertificateType(vo.getCertificateType());
        }
        if (vo.getIdCard() != null) {
            entity.setIdCard(vo.getIdCard());
        }
        if (vo.getHouseholdCity() != null) {
            entity.setHouseholdCity(vo.getHouseholdCity());
        }
        if (vo.getIncomeReportingExemptionFlag() != null) {
            entity.setIncomeReportingExemptionFlag(vo.getIncomeReportingExemptionFlag());
        }
        if (vo.getExemptionType() != null) {
            entity.setExemptionType(vo.getExemptionType());
        }
        if (vo.getHouseholdAddress() != null) {
            entity.setHouseholdAddress(vo.getHouseholdAddress());
        }
        if (vo.getStoreName() != null) {
            entity.setStoreName(vo.getStoreName());
        }
        if (vo.getStoreUniqueCode() != null) {
            entity.setStoreUniqueCode(vo.getStoreUniqueCode());
        }
        if (vo.getWebsiteUrl() != null) {
            entity.setWebsiteUrl(vo.getWebsiteUrl());
        }
        if (vo.getCardBank() != null) {
            entity.setCardBank(vo.getCardBank());
        }
        if (vo.getAccountName() != null) {
            entity.setAccountName(vo.getAccountName());
        }
        if (vo.getBankCard() != null) {
            entity.setBankCard(vo.getBankCard());
        }
        if (vo.getContactName() != null) {
            entity.setContactName(vo.getContactName());
        }
        if (vo.getContactPhone() != null) {
            entity.setContactPhone(vo.getContactPhone());
        }
        if (vo.getStartDate() != null) {
            entity.setStartDate(vo.getStartDate());
        }
        if (vo.getEndDate() != null) {
            entity.setEndDate(vo.getEndDate());
        }
        if (vo.getInfoStatusFlag() != null) {
            entity.setInfoStatusFlag(vo.getInfoStatusFlag());
        }
    }
}
