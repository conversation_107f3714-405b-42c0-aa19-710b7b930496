package com.olading.operate.labor.domain.share.info;

import com.olading.operate.labor.domain.share.customer.vo.CustomerVo;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Transactional
@Component
public class InfoManager {

    private final EntityManager em;

    public EnterpriseInfoEntity save(OwnerType ownerType, Long ownerId, CustomerVo info) {

        EnterpriseInfoEntity entity = queryEnterpriseInfo(t -> t.ownedBy.ownerType.eq(ownerType).and(t.ownedBy.ownerId.eq(ownerId))).fetchOne();
        if (entity == null) {
            entity = new EnterpriseInfoEntity(ownerType, ownerId);
        }

        if (info.getName() != null) {
            entity.setName(info.getName());
        }
        if (info.getBusinessLicenseImage() != null) {
            entity.setBusinessLicenseImage(info.getBusinessLicenseImage());
        }
        if (info.getSocialCreditCode() != null) {
            entity.setSocialCreditCode(info.getSocialCreditCode());
        }
        if (info.getRegisterAddress() != null) {
            entity.setRegisteredAddress(info.getRegisterAddress());
        }
        if (info.getCertificateFrontImage() != null) {
            entity.setCertificateFrontImage(info.getCertificateFrontImage());
        }
        if (info.getCertificateBackImage() != null) {
            entity.setCertificateBackImage(info.getCertificateBackImage());
        }
        if (info.getRepresentativeName() != null) {
            entity.setRepresentativeName(info.getRepresentativeName());
        }
        if (info.getCertificateType() != null) {
            entity.setCertificateType(info.getCertificateType());
        }
        if (info.getCertificateNo() != null) {
            entity.setCertificateNo(info.getCertificateNo());
        }
        if (info.getContactName() != null) {
            entity.setContacts(info.getContactName());
        }
        if (info.getContactMobile() != null) {
            entity.setContactPhone(info.getContactMobile());
        }
        return em.merge(entity);
    }

    public PersonInfoEntity save(OwnerType ownerType, Long ownerId, PersonInfoData info) {
        PersonInfoEntity entity = queryPersonInfo(t -> t.ownedBy.ownerType.eq(ownerType).and(t.ownedBy.ownerId.eq(ownerId))).fetchOne();
        if (entity == null) {
            entity = new PersonInfoEntity(ownerType, ownerId);
        }

        if (StringUtils.isNotBlank(info.getName())) {
            entity.setName(info.getName());
        }
        if (StringUtils.isNotBlank(info.getCellphone())) {
            entity.setCellphone(info.getCellphone());
        }
        if (StringUtils.isNotBlank(info.getIdCard())) {
            entity.setIdCard(info.getIdCard());
        }
        return em.merge(entity);
    }

    public EnterpriseInfoEntity save(OwnedByFragment ownedBy, EnterpriseInfoData info){

        EnterpriseInfoEntity entity = queryEnterpriseInfo(t -> t.ownedBy.eq(ownedBy)).fetchOne();
        if (entity == null) {
            entity = new EnterpriseInfoEntity(ownedBy.getOwnerType(), ownedBy.getOwnerId());
        }

        if (info.getName() != null) {
            entity.setName(info.getName());
        }
        if (info.getContacts() != null) {
            entity.setContacts(info.getContacts());
        }
        if (info.getContactPhone() != null) {
            entity.setContactPhone(info.getContactPhone());
        }
        if (info.getRemark() != null) {
            entity.setRemark(info.getRemark());
        }
        if (info.getAttachments() != null) {
            entity.setAttachments(info.getAttachments());
        }
        if (info.getSocialCreditCode() != null){
            entity.setSocialCreditCode(info.getSocialCreditCode());
        }
        if (info.getBusinessLicenseImage() != null){
            entity.setBusinessLicenseImage(info.getBusinessLicenseImage());
        }
        if (info.getRegisterAddress() != null){
            entity.setRegisteredAddress(info.getRegisterAddress());
        }
        if (info.getCertificateFrontImage() != null){
            entity.setCertificateFrontImage(info.getCertificateFrontImage());
        }
        if (info.getCertificateBackImage() != null){
            entity.setCertificateBackImage(info.getCertificateBackImage());
        }
        if (info.getCertificateType() != null){
            entity.setCertificateType(info.getCertificateType());
        }
        if (info.getCertificateNo() != null){
            entity.setCertificateNo(info.getCertificateNo());
        }
        if(info.getRepresentativeName() != null){
            entity.setRepresentativeName(info.getRepresentativeName());
        }
        return em.merge(entity);
    }

    public PersonInfoData getPersonInfo(OwnerType ownerType, Long ownerId) {
        PersonInfoEntity entity = queryPersonInfo(t -> t.ownedBy.ownerId.eq(ownerId).and(t.ownedBy.ownerType.eq(ownerType))).fetchOne();
        if (entity == null) {
            return null;
        }
        return new PersonInfoData(entity);
    }

    public List<PersonInfoData> getPersonInfo(OwnerType ownerType, List<Long> ownerId) {
        return queryPersonInfo(t -> t.ownedBy.ownerId.in(ownerId).and(t.ownedBy.ownerType.eq(ownerType))).fetch()
                .stream().map(PersonInfoData::new).collect(Collectors.toList());
    }

    public EnterpriseInfoEntity getEnterpriseInfo(OwnerType ownerType, Long ownerId) {
        return queryEnterpriseInfo(t -> t.ownedBy.ownerId.eq(ownerId).and(t.ownedBy.ownerType.eq(ownerType))).fetchOne();
    }

    public EnterpriseInfoEntity getEnterpriseInfo(Long id) {
        return queryEnterpriseInfo(t -> t.id.eq( id)).fetchOne();
    }

    private JPAQuery<EnterpriseInfoEntity> queryEnterpriseInfo(Function<QEnterpriseInfoEntity, Predicate> condition) {
        QEnterpriseInfoEntity t = QEnterpriseInfoEntity.enterpriseInfoEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<PersonInfoEntity> queryPersonInfo(Function<QPersonInfoEntity, Predicate> condition) {
        QPersonInfoEntity t = QPersonInfoEntity.personInfoEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }
}
