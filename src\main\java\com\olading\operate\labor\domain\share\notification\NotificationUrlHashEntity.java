package com.olading.operate.labor.domain.share.notification;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.Comment;

/**
 * 推送URL哈希表
 */
@Table(name = "t_notification_url_hash", indexes = {
        @Index(name = "i_notification_url_hash_1", columnList = "url_hash")
})
@Entity
public class NotificationUrlHashEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Comment("通知接收人")
    @Column(name = "url", length = 1000, nullable = false)
    private String url;

    @NotNull
    @Comment("通知url的hash")
    @Column(name = "url_hash", length = 50, nullable = false)
    private String urlHash;

    public NotificationUrlHashEntity(String url, String urlHash) {
        this.url = url;
        this.urlHash = urlHash;
    }

    protected NotificationUrlHashEntity() {
    }

    public String getUrl() {
        return url;
    }

    public String getUrlHash() {
        return urlHash;
    }

}
