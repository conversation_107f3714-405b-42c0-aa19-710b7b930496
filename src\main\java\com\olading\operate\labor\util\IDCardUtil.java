package com.olading.operate.labor.util;


import com.olading.operate.labor.domain.ApiException;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class IDCardUtil {

    public static String validateArr[] = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};

    public static boolean checkIdNumber(String ID) {

        boolean flag = false;
        if (ID.length() != 18) {
            return false;
        }
        //验证码
        String validateCode = ID.substring(17, 18);
        //前17位称为本体码
        String selfCode = ID.substring(0, 17);
        //加权因子公式：2的n-1次幂除以11取余数，n就是那个i，从右向左排列。
        int sum = 0;   //用于加权数求和
        int codeLength = selfCode.length();
        for (int i = 0; i < codeLength; i++) {
            //计算该位加权因子
            int yi = weightFactor(i + 1) % 11;
            int count = Integer.parseInt(selfCode.charAt(codeLength - i - 1) + "");
            //加权求和
            sum += (count * yi);
        }
        String checkCode = validateArr[sum % 11];
        if (checkCode.equalsIgnoreCase(validateCode)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 计算身份证数位数字加权因子
     * digit表示数位
     */
    private static int weightFactor(int digit) {
        return 1 << digit;
    }


    /**
     * 根据身份证解析出 性别、出生日期、年龄
     * sex：性别
     * birthday：生日
     * age：年龄
     *
     * @param certificateNo
     * @return
     */
    public static Map<String, String> parseCertificateNo(String certificateNo) {

        Map<String, String> resultDTO = new HashMap<>();
        String myRegExpIDCardNo = "^\\d{6}(((19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\\d{3}([0-9]|x|X))|(\\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\\d{3}))$";
        boolean valid = Pattern.matches(myRegExpIDCardNo, certificateNo) || (certificateNo.length() == 17 && Pattern.matches(myRegExpIDCardNo, certificateNo.substring(0, 15)));
        if (!valid) {
            throw new ApiException("证件号码不规范!", ApiException.API_PARAM_ERROR);
        }
        int idxSexStart = 16;
        int birthYearSpan = 4;
        //如果是15位的证件号码
        if (certificateNo.length() == 15) {
            idxSexStart = 14;
            birthYearSpan = 2;
        }

        //性别
        String idxSexStr = certificateNo.substring(idxSexStart, idxSexStart + 1);
        int idxSex = Integer.parseInt(idxSexStr) % 2;
        String sex = (idxSex == 1) ? "男" : "女";
        resultDTO.put("sex", sex);

        //出生日期
        String year = (birthYearSpan == 2 ? "19" : "") + certificateNo.substring(6, 6 + birthYearSpan);
        String month = certificateNo.substring(6 + birthYearSpan, 6 + birthYearSpan + 2);
        String day = certificateNo.substring(8 + birthYearSpan, 8 + birthYearSpan + 2);
        String birthday = year + '-' + month + '-' + day;
        resultDTO.put("birthday", birthday);


        //年龄
        Calendar certificateCal = Calendar.getInstance();
        Calendar currentTimeCal = Calendar.getInstance();
        certificateCal.set(Integer.parseInt(year), Integer.parseInt(month) - 1, Integer.parseInt(day));
        int yearAge = (currentTimeCal.get(currentTimeCal.YEAR)) - (certificateCal.get(certificateCal.YEAR));
        certificateCal.set(currentTimeCal.get(Calendar.YEAR), Integer.parseInt(month) - 1, Integer.parseInt(day));
        int monthFloor = (currentTimeCal.before(certificateCal) ? 1 : 0);
        resultDTO.put("age", String.valueOf(yearAge - monthFloor));
        return resultDTO;
    }
}
