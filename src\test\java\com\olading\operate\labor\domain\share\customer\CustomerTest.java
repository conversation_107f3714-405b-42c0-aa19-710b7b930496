package com.olading.operate.labor.domain.share.customer;

import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.domain.query.CustomerQuery;
import com.olading.operate.labor.domain.service.CustomerService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.customer.vo.CustomerVo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import com.olading.operate.labor.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

@Slf4j
public class CustomerTest extends BaseTest {

    @Autowired
    private CustomerService customerService;


    @Autowired
    private QueryService queryService;

    /**
     * 分页查询客户列表
     */
    @Test
    public void queryCustomerPage() {
        CustomerQuery.Filters filters = new CustomerQuery.Filters();
        filters.setName("70");
        filters.setSocialCreditCode(null);
        filters.setCreateTimeStart(null);
        filters.setCreateTimeEnd(null);

        // 2. 构建分页过滤器
        QueryFilter<CustomerQuery.Filters> queryFilter = new QueryFilter<>();
        queryFilter.setFilters(filters);
        queryFilter.setLimit(10L);
        queryFilter.setOffset(0L);
        queryFilter.sort("id", Direction.DESCENDING);

        // 3. 调用查询方法
        DataSet<CustomerWithInfo> result = queryService.queryCustomer(queryFilter);

        // 4. 打印结果
        log.info("分页查询客户成功，数据={}",JSONUtils.json(result));
    }




    @Test
    public void addCustmoer(){
        // 1. 拼装测试参数
        CustomerVo customerVo = new CustomerVo();
        // 基础信息
        customerVo.setName("测试科技有限公司"+System.currentTimeMillis() % 10000); // 公司名称（必填）
        customerVo.setSocialCreditCode("91310101MA1G8XXXX" + System.currentTimeMillis() % 10000); // 统一社会信用代码（模拟唯一）
        customerVo.setRegisterAddress("上海市浦东新区XX路XX号"); // 注册地址
        customerVo.setBusinessLicenseImage("营业执照id"+ System.currentTimeMillis() % 10000); //营业执照图片id
        // 法定代表人信息
        customerVo.setRepresentativeName("张三"); // 法定代表人姓名
        customerVo.setCertificateType(CertificateTypeEnum.ID_CARD); // 证件类型：
        customerVo.setCertificateNo("31010119900101" + (1000 + (int)(Math.random() * 9000))); // 模拟身份证号
        customerVo.setCertificateFrontImage("front_img_" + System.currentTimeMillis()); // 正面照ID
        customerVo.setCertificateBackImage("back_img_" + System.currentTimeMillis()); // 背面照ID
        // 联系人信息
        customerVo.setContactName("李四"); // 联系人姓名
        customerVo.setContactMobile("13800138" + (1000 + (int)(Math.random() * 9000))); // 模拟手机号
        customerVo.setShortName("简称wklfslfsl");
        // 2. 调用控制器方法
        CustomerEntity customerEntity = customerService.addCustomer(null, customerVo);

        // 3. 简单打印结果
        log.info("新增客户成功！参数:{}",JSONUtils.json(customerEntity));
    }

    @Test
    public void updateCustomerInfo(){
        // 1. 拼装测试参数
        CustomerVo customerVo = new CustomerVo();
        // 基础信息
        customerVo.setName("1测试科技有限公司22uuuuu" + System.currentTimeMillis() % 10000); // 公司名称（必填）
        customerVo.setSocialCreditCode("91310101MA1G8uuuuu" + System.currentTimeMillis() % 10000); // 统一社会信用代码（模拟唯一）
        customerVo.setRegisterAddress("1上海市浦东新区XX路XX号uuuuu"); // 注册地址
        customerVo.setBusinessLicenseImage("营业执照iduuuuu"+ System.currentTimeMillis() % 10000); //营业执照图片id
        // 法定代表人信息
        customerVo.setRepresentativeName("1张三"); // 法定代表人姓名
        customerVo.setCertificateType(CertificateTypeEnum.ID_CARD); // 证件类型：1-身份证
        customerVo.setCertificateNo("131010119900101uuuuu" + (1000 + (int)(Math.random() * 9000))); // 模拟身份证号
        customerVo.setCertificateFrontImage("1front_imguuuuu_" + System.currentTimeMillis()); // 正面照ID
        customerVo.setCertificateBackImage("1back_img_uuuuu" + System.currentTimeMillis()); // 背面照ID
        // 联系人信息
        customerVo.setContactName("1李四uuuuu"); // 联系人姓名
        customerVo.setContactMobile("13800138uuuuu" + (1000 + (int)(Math.random() * 9000))); // 模拟手机号

        // 2. 调用控制器方法
        CustomerEntity customerEntity = customerService.updateCustomerInfo(null, 7l,customerVo);

        // 3. 简单打印结果
        log.info("修改客户成功！参数:{}",JSONUtils.json(customerEntity));

    }

    @Test
    public void queryCustomer(){
        CustomerWithInfo customerWithInfo = customerService.queryCustomer(3l);
        log.info("查询客户返回_queryCustomer:{}", JSONUtils.json(customerWithInfo));
    }

    @Test
    public void updateCustomerStatus(){
        // 测试更新客户状态
        long customerId = 7L; // 使用已存在的客户ID
        String newStatus = "ONGOING"; // 合作中

        try {
            customerService.updateCustomerStatus(null, customerId, newStatus);
            log.info("更新客户状态成功！客户ID: {}, 新状态: {}", customerId, newStatus);

            // 查询验证状态是否更新成功
            CustomerWithInfo customerWithInfo = customerService.queryCustomer(customerId);
            log.info("更新后的客户状态: {}", customerWithInfo.getStatus());
        } catch (Exception e) {
            log.error("更新客户状态失败", e);
        }
    }

    @Test
    public void updateCustomerStatusWithInvalidStatus(){
        // 测试使用无效状态值
        long customerId = 7L;
        String invalidStatus = "INVALID_STATUS";

        try {
            customerService.updateCustomerStatus(null, customerId, invalidStatus);
            log.error("应该抛出异常，但没有抛出");
        } catch (Exception e) {
            log.info("正确捕获到异常: {}", e.getMessage());
        }
    }

    @Test
    public void queryCustomerBySupplier(){
        List<CustomerWithInfo> customerWithInfos = customerService.queryCustomerBySupplier(23424l);
        log.info("查询客户返回_queryCustomerBySupplier:{}", JSONUtils.json(customerWithInfos));
    }

    @Test
    public void testQueryCustomerWithRoles(){
        // 1. 先创建一个客户
        CustomerVo customerVo = new CustomerVo();
        customerVo.setName("测试角色客户" + System.currentTimeMillis() % 10000);
        customerVo.setSocialCreditCode("91310101MA1G8ROLE" + System.currentTimeMillis() % 10000);
        customerVo.setRegisterAddress("上海市浦东新区角色测试路XX号");
        customerVo.setBusinessLicenseImage("营业执照role_id" + System.currentTimeMillis() % 10000);
        customerVo.setRepresentativeName("角色测试");
        customerVo.setCertificateType(CertificateTypeEnum.ID_CARD);
        customerVo.setCertificateNo("31010119900101" + (1000 + (int)(Math.random() * 9000)));
        customerVo.setCertificateFrontImage("front_role_" + System.currentTimeMillis());
        customerVo.setCertificateBackImage("back_role_" + System.currentTimeMillis());
        customerVo.setContactName("角色联系人");
        customerVo.setContactMobile("13800138" + (1000 + (int)(Math.random() * 9000)));
        customerVo.setShortName("角色简称");

        // 设置角色ID（这里使用模拟的角色ID，实际测试时需要先创建角色）
        customerVo.setRoleIds(Arrays.asList(1L, 2L, 3L));

        // 2. 创建客户
        CustomerEntity customerEntity = customerService.addCustomer(null, customerVo);
        log.info("创建客户成功，ID: {}", customerEntity.getId());

        // 3. 查询客户信息，验证是否包含角色信息
        CustomerWithInfo customerWithInfo = customerService.queryCustomer(customerEntity.getId());

        // 4. 验证结果
        log.info("查询客户详情成功: {}", JSONUtils.json(customerWithInfo));
        log.info("客户关联的角色ID列表: {}", customerWithInfo.getRoleIds());

        // 验证角色ID是否正确返回
        if (customerWithInfo.getRoleIds() != null && !customerWithInfo.getRoleIds().isEmpty()) {
            log.info("✓ 成功返回角色ID列表，数量: {}", customerWithInfo.getRoleIds().size());
        } else {
            log.warn("⚠ 未返回角色ID列表或列表为空");
        }
    }
}
