package com.olading.operate.labor.domain.share.labor;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicUpdate;

import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(name = "t_labor_info")
@AttributeOverrides({
        @AttributeOverride(name = "version", column = @Column(name = "version"))
})
@DynamicUpdate
public class LaborInfoEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Comment("劳务人员id")
    @Column(name = "labor_id")
    private Long laborId;

    @Comment("平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("客户id")
    @Column(name = "customer_id")
    private Long customerId;

    @Comment("服务合同id")
    @Column(name = "contract_id")
    private Long contractId;

    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id")
    private Long supplierCorporationId;

    @Size(max = 30)
    @Comment("最高学历")
    @Column(name = "education", length = 30)
    private String education;

    @Size(max = 120)
    @Comment("籍贯")
    @Column(name = "native", length = 120)
    private String nativeField;

    @Size(max = 10)
    @Comment("户口性质")
    @Column(name = "household_registration_type", length = 10)
    private String householdRegistrationType;

    @Size(max = 50)
    @Comment("户口所在城市")
    @Column(name = "household_city", length = 50)
    private String householdCity;

    @Size(max = 256)
    @Comment("户口所在地址")
    @Column(name = "household_address", length = 256)
    private String householdAddress;

    @Size(max = 10)
    @Comment("婚姻状况")
    @Column(name = "marital_status", length = 10)
    private String maritalStatus;

    @Size(max = 20)
    @Comment("子女状况")
    @Column(name = "children", length = 20)
    private String children;

    @Size(max = 60)
    @Comment("民族")
    @Column(name = "nation", length = 60)
    private String nation;

    @Size(max = 20)
    @Comment("政治面貌")
    @Column(name = "political", length = 20)
    private String political;

    @Comment("参加工作日期")
    @Column(name = "in_work_day")
    private LocalDate inWorkDay;

    @Comment("所在部门id")
    @Column(name = "dept_id")
    private Long deptId;

    @Comment("雇佣人员")
    @Column(name = "employed")
    private Long employed;

    @Comment("加入日期")
    @Column(name = "join_date")
    private LocalDate joinDate;

    @Size(max = 128)
    @Comment("岗位")
    @Column(name = "post", length = 128)
    private String post;

    @Size(max = 20)
    @Comment("员工状态")
    @Column(name = "emp_status", length = 20)
    private String empStatus;

    @Size(max = 128)
    @Comment("工作邮箱")
    @Column(name = "work_email", length = 128)
    private String workEmail;

    @Size(max = 128)
    @Comment("企业微信")
    @Column(name = "wechat", length = 128)
    private String wechat;

    @Size(max = 128)
    @Comment("个人微信")
    @Column(name = "personal_wechat", length = 128)
    private String personalWechat;

    @Size(max = 20)
    @Comment("工作电话")
    @Column(name = "work_mobile", length = 20)
    private String workMobile;

    @Size(max = 20)
    @Comment("分机号")
    @Column(name = "mobile_number", length = 20)
    private String mobileNumber;

    @Size(max = 128)
    @Comment("个人邮箱")
    @Column(name = "personal_email", length = 128)
    private String personalEmail;

    @Size(max = 128)
    @Comment("居住地")
    @Column(name = "address", length = 128)
    private String address;

    @Size(max = 60)
    @Comment("工资卡号")
    @Column(name = "bank_card", length = 60)
    private String bankCard;

    @Size(max = 64)
    @Comment("开户城市")
    @Column(name = "open_card_city", length = 64)
    private String openCardCity;

    @Size(max = 64)
    @Comment("开户行")
    @Column(name = "card_bank", length = 64)
    private String cardBank;

    @Size(max = 64)
    @Comment("开户支行")
    @Column(name = "bank_branch", length = 64)
    private String bankBranch;

}