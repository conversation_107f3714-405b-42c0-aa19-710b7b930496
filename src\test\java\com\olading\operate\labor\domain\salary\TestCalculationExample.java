package com.olading.operate.labor.domain.salary;

import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationRequest;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationResult;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 新公式计算示例 - 包含增值税和附加税
 *
 * 演示完整的薪酬计算流程：个税 + 增值税 + 附加税
 */
@Slf4j
public class TestCalculationExample {

    /**
     * 演示完整的薪酬计算示例
     */
    public static void demonstrateFullCalculation() {
        log.info("=== 薪酬计算示例（包含增值税和附加税增量计算）===");

        // 示例1：低于增值税起征点
        demonstrateCase("示例1：应发金额低于增值税起征点",
                new BigDecimal("80000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);

        // 示例2：正好达到增值税起征点
        demonstrateCase("示例2：应发金额正好达到增值税起征点",
                new BigDecimal("100000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);

        // 示例3：超过增值税起征点
        demonstrateCase("示例3：应发金额超过增值税起征点",
                new BigDecimal("150000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);

        // 示例4：高收入情况
        demonstrateCase("示例4：高收入情况",
                new BigDecimal("300000"), new BigDecimal("5000"), new BigDecimal("2000"), new BigDecimal("1000"), BigDecimal.ZERO);

        // 示例5：增量计算场景
        demonstrateIncrementalCalculation();
    }

    /**
     * 演示增量计算场景
     */
    public static void demonstrateIncrementalCalculation() {
        log.info("\n=== 增量计算场景演示 ===");

        // 模拟当月多笔薪酬发放的增量计算
        log.info("场景：某作业主体当月分3次发放薪酬");

        // 第一笔：80,000元
        log.info("\n第一笔薪酬：");
        BigDecimal firstAmount = new BigDecimal("80000");
        BigDecimal existingAmount1 = BigDecimal.ZERO;
        demonstrateIncrementalCase(firstAmount, existingAmount1, 1);

        // 第二笔：30,000元（累计110,000元）
        log.info("\n第二笔薪酬：");
        BigDecimal secondAmount = new BigDecimal("30000");
        BigDecimal existingAmount2 = new BigDecimal("80000");
        demonstrateIncrementalCase(secondAmount, existingAmount2, 2);

        // 第三笔：40,000元（累计150,000元）
        log.info("\n第三笔薪酬：");
        BigDecimal thirdAmount = new BigDecimal("40000");
        BigDecimal existingAmount3 = new BigDecimal("110000");
        demonstrateIncrementalCase(thirdAmount, existingAmount3, 3);
    }

    private static void demonstrateIncrementalCase(BigDecimal currentAmount, BigDecimal existingAmount, int sequence) {
        BigDecimal newTotal = existingAmount.add(currentAmount);
        BigDecimal newTotalVat = calculateVAT(newTotal);
        BigDecimal existingTotalVat = calculateVAT(existingAmount);
        BigDecimal incrementalVat = newTotalVat.subtract(existingTotalVat);
        BigDecimal incrementalSurtax = incrementalVat.multiply(new BigDecimal("0.12")); // 假设12%附加税率

        log.info("  当前应发金额: {}", currentAmount);
        log.info("  已确认金额: {}", existingAmount);
        log.info("  当月累计金额: {}", newTotal);
        log.info("  新总额增值税: {}", newTotalVat);
        log.info("  原总额增值税: {}", existingTotalVat);
        log.info("  增量增值税: {}", incrementalVat);
        log.info("  增量附加税: {}", incrementalSurtax);
        log.info("  总增量税费: {}", incrementalVat.add(incrementalSurtax));
    }

    private static void demonstrateCase(String caseName, BigDecimal payableAmount,
                                        BigDecimal taxFreeIncome, BigDecimal otherDeductions,
                                        BigDecimal taxReliefAmount, BigDecimal existingAmount) {

        log.info("\n--- {} ---", caseName);
        log.info("应发金额: {}", payableAmount);
        log.info("免税收入: {}", taxFreeIncome);
        log.info("其他扣除: {}", otherDeductions);
        log.info("减免税额: {}", taxReliefAmount);

        // 模拟计算结果（实际使用时会调用PersonalIncomeTaxCalculationEngine）
        TaxCalculationResult result = simulateCalculation(payableAmount, taxFreeIncome,
                otherDeductions, taxReliefAmount);

        log.info("计算结果:");
        log.info("  个人所得税:");
        log.info("    累计应纳税所得额: {}", result.getNewAccumulatedData().getAccumulatedTaxableAmount());
        log.info("    本期应预扣预缴税额: {}", result.getCurrentWithholdingTax());

        log.info("  增值税:");
        log.info("    增值税额: {}", result.getVatAmount());

        log.info("  附加税:");
        log.info("    城市维护建设税: {}", result.getUrbanConstructionTax());
        log.info("    教育费附加: {}", result.getEducationSurcharge());
        log.info("    地方教育附加: {}", result.getLocalEducationSurcharge());
        log.info("    附加税总额: {}", result.getAdditionalTaxAmount());

        log.info("  汇总:");
        BigDecimal totalTax = result.getCurrentWithholdingTax()
                .add(result.getVatAmount())
                .add(result.getAdditionalTaxAmount());
        log.info("    总税费: {}", totalTax);
        log.info("    实发金额: {}", result.getNetPayment());
        log.info("    税负率: {}%", totalTax.divide(payableAmount, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100")));
    }

    /**
     * 模拟计算过程（简化版本，用于演示）
     */
    private static TaxCalculationResult simulateCalculation(BigDecimal payableAmount,
                                                            BigDecimal taxFreeIncome,
                                                            BigDecimal otherDeductions,
                                                            BigDecimal taxReliefAmount) {

        TaxCalculationResult result = new TaxCalculationResult();

        // 1. 模拟个税计算
        BigDecimal taxableIncome = payableAmount.subtract(taxFreeIncome).subtract(otherDeductions);
        BigDecimal personalIncomeTax = calculatePersonalIncomeTax(taxableIncome).subtract(taxReliefAmount).max(BigDecimal.ZERO);
        result.setCurrentWithholdingTax(personalIncomeTax);

        // 2. 计算增值税
        BigDecimal vatAmount = calculateVAT(payableAmount);
        result.setVatAmount(vatAmount);

        // 3. 计算附加税（使用标准税率）
        BigDecimal urbanTax = vatAmount.multiply(new BigDecimal("0.07")); // 7%
        BigDecimal educationSurcharge = vatAmount.multiply(new BigDecimal("0.03")); // 3%
        BigDecimal localEducationSurcharge = vatAmount.multiply(new BigDecimal("0.02")); // 2%

        result.setUrbanConstructionTax(urbanTax);
        result.setEducationSurcharge(educationSurcharge);
        result.setLocalEducationSurcharge(localEducationSurcharge);
        result.setAdditionalTaxAmount(urbanTax.add(educationSurcharge).add(localEducationSurcharge));

        // 4. 计算实发金额
        BigDecimal netPayment = payableAmount
                .subtract(personalIncomeTax)
                .subtract(vatAmount)
                .subtract(result.getAdditionalTaxAmount());
        result.setNetPayment(netPayment);

        return result;
    }

    /**
     * 简化的个税计算（仅用于演示）
     */
    private static BigDecimal calculatePersonalIncomeTax(BigDecimal taxableIncome) {
        // 简化计算，实际应使用完整的累计扣缴算法
        if (taxableIncome.compareTo(new BigDecimal("36000")) <= 0) {
            return taxableIncome.multiply(new BigDecimal("0.03"));
        } else if (taxableIncome.compareTo(new BigDecimal("144000")) <= 0) {
            return taxableIncome.multiply(new BigDecimal("0.10")).subtract(new BigDecimal("2520"));
        } else {
            return taxableIncome.multiply(new BigDecimal("0.20")).subtract(new BigDecimal("16920"));
        }
    }

    /**
     * 增值税计算
     */
    private static BigDecimal calculateVAT(BigDecimal payableAmount) {
        BigDecimal threshold = new BigDecimal("100000");
        if (payableAmount.compareTo(threshold) <= 0) {
            return BigDecimal.ZERO;
        }
        return payableAmount.subtract(threshold).multiply(new BigDecimal("0.01"));
    }

    /**
     * 计算规则说明
     */
    public static void explainCalculationRules() {
        log.info("\n=== 计算规则说明 ===");
        log.info("1. 个人所得税:");
        log.info("   - 采用累计预扣法");
        log.info("   - 累计应纳税所得额 = 累计收入 - 累计费用(20%) - 累计减除费用(月数×5000) - 累计免税收入 - 累计其他扣除");
        log.info("   - 按照个税税率表计算累计应纳税额");
        log.info("   - 本期应预扣预缴税额 = 累计应纳税额 - 累计已预缴税额");

        log.info("\n2. 增值税:");
        log.info("   - 起征点: 100,000元");
        log.info("   - 计算公式: 累计应发金额 ≤ 100,000时，增值税 = 0");
        log.info("   - 计算公式: 累计应发金额 > 100,000时，增值税 = (累计应发金额 - 100,000) × 1%");

        log.info("\n3. 附加税:");
        log.info("   - 基于增值税额计算");
        log.info("   - 城市维护建设税 = 增值税额 × 城建税税率 × (1 - 优惠比例)");
        log.info("   - 教育费附加 = 增值税额 × 教育费附加税率 × (1 - 优惠比例)");
        log.info("   - 地方教育附加 = 增值税额 × 地方教育附加税率 × (1 - 优惠比例)");

        log.info("\n4. 实发金额:");
        log.info("   - 实发金额 = 应发金额 - 个人所得税 - 增值税 - 附加税总额");
    }

    public static void main(String[] args) {
        explainCalculationRules();
        demonstrateFullCalculation();
    }
}
