package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.share.task.QTaskEntity;
import com.olading.operate.labor.domain.share.task.TaskEntity;
import com.olading.operate.labor.domain.share.task.TaskType;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;

public class OrderDetailTaskQuery implements EntityQuery<QueryFilter<OrderDetailTaskQuery.Filters>, TaskEntity> {
    private final QTaskEntity t1 = QTaskEntity.taskEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getOwnerId() != null) {
            criteria.and(t1.ownedBy.ownerId.eq(filters.getFilters().getOwnerId()));
        }
        if (filters.getFilters().getTaskType() != null) {
            criteria.and(t1.taskType.eq(filters.getFilters().getTaskType()));
        }
        criteria.and(t1.deleted.eq(Boolean.FALSE));
        query.select(t1).from(t1);
        query.where(criteria);
        query.orderBy(t1.createTime.desc());
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        return EntityQuery.super.columnMapping(column);
    }

    @Override
    public TaskEntity transform(Object v) {
        return EntityQuery.super.transform(v);
    }

    @Data
    public static class Filters {

        private Long ownerId;

        private TaskType taskType;

    }
}
