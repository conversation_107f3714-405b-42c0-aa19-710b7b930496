package com.olading.operate.labor.util.excel;

import com.lanmaoly.util.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/3/21 14:29
 */
public class ExcelCellError {
    /**
     * 校验错误的提示信息
     */
    private String error;

    /**
     * 校验错误的详细信息
     */
    private String detail;

    public ExcelCellError() {

    }

    public ExcelCellError(String error) {
        this.error = error;
    }

    public ExcelCellError(String error, String detail) {
        this(error);
        this.detail = detail;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    @Override
    public String toString() {
        return StringUtils.toNoNullString(error);
    }
}
