package com.olading.operate.labor.domain.bill.repository;

import com.olading.operate.labor.domain.bill.BillSalaryDetailEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 账单薪酬明细仓储接口
 */
@Repository
public interface BillSalaryDetailRepository extends JpaRepository<BillSalaryDetailEntity, Long> {

    /**
     * 根据账单主表ID分页查询薪酬明细
     */
    Page<BillSalaryDetailEntity> findByBillMasterIdAndDeletedFalseOrderByCreateTimeDesc(Long billMasterId, Pageable pageable);

    /**
     * 根据账单分类ID查询薪酬明细
     */
    List<BillSalaryDetailEntity> findByBillCategoryIdAndDeletedFalse(Long billCategoryId);

    /**
     * 根据薪酬批次ID查询账单明细
     */
    List<BillSalaryDetailEntity> findBySalaryBatchIdAndDeletedFalse(Long salaryBatchId);


    /**
     * 统计账单主表的薪酬明细数量
     */
    long countByBillMasterId(@Param("billMasterId") Long billMasterId);

    /**
     * 删除账单主表的所有薪酬明细
     */
    void deleteByBillMasterId(@Param("billMasterId") Long billMasterId);
}