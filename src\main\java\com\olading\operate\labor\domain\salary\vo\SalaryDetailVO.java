package com.olading.operate.labor.domain.salary.vo;

import com.olading.boot.util.beans.Beans;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SalaryDetailVO {

    @Schema(description = "员工工资明细ID")
    private Long id;

    @Schema(description = "工资表ID")
    private Long salaryStatementId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "手机号")
    private String phoneNumber;

    @Schema(description = "银行卡号")
    private String bankCardNumber;

    @Schema(description = "开户行")
    private String bankName;

    @Schema(description = "应发金额")
    private BigDecimal payableAmount;

    @Schema(description = "累计收入")
    private BigDecimal accumulatedIncome;

    @Schema(description = "累计应纳税额")
    private BigDecimal accumulatedTaxableAmount;

    @Schema(description = "累计已预缴税额")
    private BigDecimal accumulatedPrepaidTax;

    @Schema(description = "本期应预扣预缴税额")
    private BigDecimal currentPeriodTaxWithholding;

    @Schema(description = "本次应预扣预缴税额")
    private BigDecimal currentTaxWithholding;

    @Schema(description = "本月已预扣预缴税额")
    private BigDecimal monthlyPrepaidTax;

    @Schema(description = "增值税额")
    private BigDecimal vatAmount;

    @Schema(description = "增值附加税额")
    private BigDecimal additionalTaxAmount;

    @Schema(description = "城市维护建设附加税")
    private BigDecimal urbanConstructionTax;

    @Schema(description = "教育费附加税")
    private BigDecimal educationSurcharge;

    @Schema(description = "地方教育附加税")
    private BigDecimal localEducationSurcharge;

    @Schema(description = "实发金额")
    private BigDecimal netPayment;

    public static SalaryDetailVO toVo(SalaryDetailEntity entity) {
        return Beans.copyBean(entity, SalaryDetailVO.class);
    }

}
