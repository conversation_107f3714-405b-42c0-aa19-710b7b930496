
package com.olading.operate.labor.app.web.biz;

import cn.hutool.json.JSONUtil;
import com.olading.operate.labor.domain.share.user.UserEntity;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

@Aspect
@Component
@Slf4j
public class LogAspect {

    @Pointcut("execution(public * com.olading.operate.labor.app.web..*Controller.*(..))")
    public void controllerLog() {
    }

    @Around("controllerLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        LocalDateTime startTime = LocalDateTime.now();
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();

        String clientIP = getClientIP(request);
        String userAgent = request.getHeader("User-Agent");
        String url = request.getRequestURL().toString();

        String userInfo = "匿名用户";
        Long supplierId = null;

        // 获取用户信息（安全方式）
        try {
            if (joinPoint.getTarget() instanceof BusinessController controller) {
                try {
                    UserEntity user = controller.currentUser();
                    if (user != null) {
                        userInfo = String.format("%s:%s:%s", user.getName(), user.getCellphone(), user.getAccountNo());
                    }
                } catch (Exception ignored) {
                }

                try {
                    supplierId = controller.getSupplierId(request);
                } catch (Exception ignored) {
                }
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败", e);
        }

        // 构建日志上下文
        LogContext context = new LogContext()
                .setStartTime(startTime)
                .setIp(clientIP)
                .setUserAgent(userAgent)
                .setUserInfo(userInfo)
                .setSupplierId(supplierId)
                .setMethodName(joinPoint.getSignature().getName()).setUrl(url);;

        try {
            // 打印请求参数
            Object[] args = joinPoint.getArgs();
            StringBuilder paramsBuilder = new StringBuilder("[");
            for (Object arg : args) {
                if (arg instanceof HttpServletRequest || arg instanceof HttpServletResponse || arg instanceof MultipartFile || arg == null) {
                    continue;
                }
                try {
                    paramsBuilder.append(JSONUtil.toJsonStr(arg)).append(",");
                } catch (Exception e) {
                    paramsBuilder.append("<<JSON序列化失败>>").append(",");
                }
            }
            if (paramsBuilder.length() > 1) {
                paramsBuilder.setLength(paramsBuilder.length() - 1);
            }
            paramsBuilder.append("]");
            context.setRequestParams(paramsBuilder.toString());

        } catch (Exception e) {
            log.warn("记录请求参数失败", e);
        }

        // 执行业务逻辑
        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Exception ex) {
            context.setException(ex.getClass().getSimpleName() + ": " + ex.getMessage());
            throw ex;
        } finally {
            // 记录耗时
            context.setDuration(Duration.between(startTime, LocalDateTime.now()).toMillis());
            // 打印返回结果
            try {
                String resultJson = result != null ? JSONUtil.toJsonStr(result) : "";
                context.setResult(StringUtils.abbreviate(resultJson, 2000));
            } catch (Exception e) {
                context.setResult("<<无法序列化>>");
            }
            log.info(context.buildLog());
        }
        return result;
    }

    private String getClientIP(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private static class LogContext {
        private LocalDateTime startTime;
        private String ip;
        private String userAgent;
        private String userInfo;
        private Long supplierId;
        private String methodName;
        private String requestParams = "[]";
        private String result = "";
        private long duration;
        private String exception = "";
        private String url;

        public LogContext setUrl(String url) {
            this.url = url;
            return this;
        }
        public LogContext setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }

        public LogContext setIp(String ip) {
            this.ip = ip;
            return this;
        }

        public LogContext setUserAgent(String userAgent) {
            this.userAgent = userAgent;
            return this;
        }

        public LogContext setUserInfo(String userInfo) {
            this.userInfo = userInfo;
            return this;
        }

        public LogContext setSupplierId(Long supplierId) {
            this.supplierId = supplierId;
            return this;
        }

        public LogContext setMethodName(String methodName) {
            this.methodName = methodName;
            return this;
        }

        public LogContext setRequestParams(String requestParams) {
            this.requestParams = requestParams;
            return this;
        }

        public LogContext setResult(String result) {
            this.result = result;
            return this;
        }

        public LogContext setDuration(long duration) {
            this.duration = duration;
            return this;
        }

        public LogContext setException(String exception) {
            this.exception = exception;
            return this;
        }

        public String buildLog() {
            StringBuilder sb = new StringBuilder();
            sb.append("【请求日志】 ");
            sb.append("访问地址: ").append(url).append(" | ");
            sb.append("IP: ").append(ip).append(" | ");
            sb.append("用户: ").append(userInfo).append(" | ");
            sb.append("供应商ID: ").append(supplierId != null ? supplierId : "-").append(" | ");
            sb.append("方法: ").append(methodName).append(" | ");
            sb.append("参数: ").append(requestParams).append(" | ");
            sb.append("耗时: ").append(duration).append("ms | ");
            if (!exception.isEmpty()) {
                sb.append("异常: ").append(exception).append(" | ");
            }
            sb.append("结果: ").append(result);
            return sb.toString();
        }
    }
}