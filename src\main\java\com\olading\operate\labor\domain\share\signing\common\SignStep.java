package com.olading.operate.labor.domain.share.signing.common;

import com.olading.operate.labor.domain.share.signing.enums.IdType;
import com.olading.operate.labor.domain.share.signing.enums.SignStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description= "签署步骤签名信息")
@Data
public class SignStep {

    @Schema(description = "签名步骤", required = true)
    private String name;
    @Schema(description = "签名人名称", required = true)
    private String signer;
    @Schema(description = "签署人唯一标识符", required = true)
    private String idNo;
    @Schema(description = "证件类型", required = true)
    private IdType idType;
    @Schema(description = "签名状态", required = true)
    private SignStatus status;
}
