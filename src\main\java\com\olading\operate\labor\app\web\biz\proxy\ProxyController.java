package com.olading.operate.labor.app.web.biz.proxy;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.component.captcha.CaptchaGenerator;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.PublicController;
import com.olading.operate.labor.config.NullIfEmptyInSetDeserializer;
import com.olading.operate.labor.domain.proxy.ProxyOrderBatchDetailData;
import com.olading.operate.labor.domain.proxy.ProxyOrderData;
import com.olading.operate.labor.domain.proxy.ProxyOrderManager;
import com.olading.operate.labor.domain.proxy.order.ProxyBatchStatusEnum;
import com.olading.operate.labor.domain.proxy.order.ProxyOrderStatusEnum;
import com.olading.operate.labor.domain.query.ProxyBatchQuery;
import com.olading.operate.labor.domain.query.ProxyOrderQuery;
import com.olading.operate.labor.domain.query.SalaryQuery;
import com.olading.operate.labor.domain.salary.vo.SalaryVO;
import com.olading.operate.labor.domain.service.ProxyOrderService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.otp.Otp;
import com.olading.operate.labor.domain.share.otp.OtpManager;
import com.olading.operate.labor.domain.supplier.SmsBusinessType;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierManager;
import com.olading.operate.labor.domain.supplier.SupplierSmsTemplateEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@Tag(name = "代发接口")
@RestController
@RequestMapping("/api/supplier/proxy")
@RequiredArgsConstructor
@Slf4j
public class ProxyController extends BusinessController {

    private final ProxyOrderManager proxyOrderManager;
    private final QueryService queryService;
    private final ProxyOrderService proxyOrderService;
    private final SupplierManager supplierManager;
    private final OtpManager otpManager;
    private final CaptchaGenerator captchaGenerator;

    @Operation(summary = "查询代发批次")
    @PostMapping(value = "listProxyBatch")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<ProxyOrderBatchDetailData> listProxyBatch(@RequestBody QueryFilter<ListProxyBatchFilters> request) {
        QueryFilter<ProxyBatchQuery.Filters> filter = request.convert(ListProxyBatchFilters::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        filter.sort("id", Direction.DESCENDING);
        DataSet<ProxyOrderBatchDetailData> ds = queryService.queryProxyOrderBatch(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "查询代发列表")
    @PostMapping(value = "listProxy")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_ORDER)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<ProxyOrderData> listProxy(@RequestBody QueryFilter<ListProxyFilters> request) {
        QueryFilter<ProxyOrderQuery.Filters> filter = request.convert(ListProxyFilters::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        filter.sort("id", Direction.DESCENDING);
        DataSet<ProxyOrderData> ds = queryService.queryProxyOrder(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "查询批次汇总信息")
    @PostMapping("batchSummary")
    @AuthorityDataScopGuard(return_value = {
            @AuthorityDataScopGuard.ReturnMapping(type = OwnerType.CONTRACT, spel = "data.contractId")
    })
    public WebApiResponse<ProxyOrderBatchDetailData> batchSummary(@Valid @RequestBody IdRequest request) {

        ProxyOrderBatchDetailData orderBatchDetailData = proxyOrderManager.getOrderBatchDetailData(request.getId());
        if(orderBatchDetailData.getSupplierId() != currentSupplierId()){
            throw new SecurityException("无权限操作");
        }
        return WebApiResponse.success(orderBatchDetailData);
    }

    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "删除批次")
    @PostMapping("deleteBatch")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.contractIds")
    })
    public WebApiResponse<Void> deleteBatch(@Valid @RequestBody IdRequest request) {
        QueryFilter<ProxyBatchQuery.Filters> queryFilter = new QueryFilter<>();
        queryFilter.setFilters(ProxyBatchQuery.Filters.builder()
                .id(request.getId())
                .contractIds(request.getContractIds()).supplierId(currentSupplierId())
                .build());
        DataSet<ProxyOrderBatchDetailData> dataSet = queryService.queryProxyOrderBatch(queryFilter);
        if(CollectionUtil.isEmpty(dataSet.getData())){
            throw new SecurityException("无权限操作");
        }
        proxyOrderService.removeBatch(request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "发送确认支付验证码")
    @RequestMapping(value = "sendOtp", method = RequestMethod.POST)
    public WebApiResponse<PublicController.CreateOtpResponse> sendOtp(@Valid @RequestBody PorxyCreateOtpRequest request) {
        final SupplierEntity supplierEntity = currentSupplier();
        final SupplierSmsTemplateEntity supplierSmsTemplateEntity = supplierManager.requireSupplierSmsTemplate(currentSupplierId(), SmsBusinessType.CHANNEL_PAYMENT);
        captchaGenerator.verify(request.getCaptchaAnswer(), request.getCaptchaToken());
        Otp otp = otpManager.send(currentUser().getCellphone(),supplierSmsTemplateEntity.getTemplateCode(),supplierEntity.getSignatureCode());
        PublicController.CreateOtpResponse response = new PublicController.CreateOtpResponse();
        response.setToken(otp.getToken());
        return WebApiResponse.success(response);
    }


    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "确认支付")
    @PostMapping("confirmPay")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.contractIds")
    })
    public WebApiResponse<Void> confirmPay(@Valid @RequestBody ConfirmPayRequest request) {
        QueryFilter<ProxyBatchQuery.Filters> queryFilter = new QueryFilter<>();
        queryFilter.setFilters(ProxyBatchQuery.Filters.builder()
                .id(request.getId())
                .contractIds(request.getContractIds())
                .supplierId(currentSupplierId())
                .build());
        DataSet<ProxyOrderBatchDetailData> dataSet = queryService.queryProxyOrderBatch(queryFilter);
        if(CollectionUtil.isEmpty(dataSet.getData())){
            throw new SecurityException("无权限操作");
        }
        proxyOrderService.confirmBatch(currentSupplierId(), request.getId(), request.getOtpToken(), request.getCode());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "生成代付批次")
    @PostMapping("createBatch")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.contractIds")
    })
    public WebApiResponse<Long> createBatch(@Valid @RequestBody CreateBatchRequest request) {
        final SalaryQuery.Filters filters = new SalaryQuery.Filters();
        filters.setId(request.getId());
        filters.setContractIds(request.getContractIds());
        filters.setSupplierId(currentSupplierId());
        QueryFilter<SalaryQuery.Filters> filtersQueryFilter = new QueryFilter<>(filters);
        final DataSet<SalaryVO> dataSet = queryService.querySalary(filtersQueryFilter);
        if(CollectionUtil.isEmpty(dataSet.getData())){
            throw new SecurityException("无权限操作");
        }
        final Long batch = proxyOrderService.createBatch(currentTenant(), request.getId(), request.getRemark());
        return WebApiResponse.success(batch);
    }

    @Data
    @Schema(description = "创建确认支付验证码请求")
    public static class PorxyCreateOtpRequest {

        @NotNull
        @Schema(description = "手机号")
        private String cellphone;

        @NotNull
        @Schema(description = "图形验证码token")
        private String captchaToken;

        @NotNull
        @Schema(description = "图形验证码")
        private String captchaAnswer;
    }




    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    @Schema(description = "代发批次查询条件")
    public static class ListProxyBatchFilters {

        @Schema(description = "代发批次ID")
        private Long id;

        @Schema(description = "作业主体名称")
        private String corporation;

        @Schema(description = "合同名称")
        private String businessContract;

        @Schema(description = "客户名称")
        private String customer;

        @Schema(description = "客户id")
        @JsonDeserialize(using = NullIfEmptyInSetDeserializer.class)
        private Set<Long> customerId;

        @Schema(description = "合同id")
        private Long contractId;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        @Schema(description = "作业主体id")
        private Long supplierCorporationId;

        @Schema(description = "批次状态")
        private ProxyBatchStatusEnum batchStatus;

        public ProxyBatchQuery.Filters convert() {
            return ProxyBatchQuery.Filters.builder()
                    .id(this.id)
                    .corporation(this.corporation)
                    .businessContract(this.businessContract)
                    .customer(this.customer)
                    .customerId(this.customerId)
                    .contractId(this.contractId)
                    .contractIds(this.contractIds)
                    .supplierCorporationId(this.supplierCorporationId)
                    .batchStatus(this.batchStatus)
                    .build();
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    @Schema(description = "代发订单查询条件")
    public static class ListProxyFilters {

        @Schema(description = "代发订单Id")
        private Long id;

        @Schema(description = "作业主体名称")
        private String corporation;

        @Schema(description = "合同名称")
        private String businessContract;

        @Schema(description = "代发批次Id")
        private Long batchId;

        @Schema(description = "工资明细Id")
        private Long salaryDetailId;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        @Schema(description = "代发状态")
        private ProxyOrderStatusEnum status;

        public ProxyOrderQuery.Filters convert() {
            return ProxyOrderQuery.Filters.builder()
                    .id(this.id)
                    .batchId(this.batchId)
                    .salaryDetailId(this.salaryDetailId)
                    .corporation(this.corporation)
                    .contractIds(this.contractIds)
                    .status(this.status)
                    .businessContract(this.businessContract)
                    .batchId(this.batchId)
                    .build();
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class ConfirmPayRequest{

        @NotNull(message = "批次ID不能为空")
        @Schema(description = "批次ID")
        private Long id;

        @Schema(description = "短信验证码")
        @NotNull(message = "短信验证码不能为空")
        private String code;

        @NotNull(message = "验证码token不能为空")
        @Schema(description = "验证码token")
        private String otpToken;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class IdRequest{

        @NotNull(message = "批次ID不能为空")
        @Schema(description = "批次ID")
        private Long id;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class CreateBatchRequest{
        @NotNull(message = "工资表id不能为空")
        @Schema(description = "工资表id")
        private Long id;

        private String remark;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

    }



}
