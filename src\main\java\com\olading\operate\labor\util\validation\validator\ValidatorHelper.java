package com.olading.operate.labor.util.validation.validator;


import com.lanmaoly.util.lang.StringUtils;
import com.lanmaoly.util.lang.exception.ValidationException;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/2/25 13:16
 */
public class ValidatorHelper {
    public static boolean invokeValidator(Validator validator, Object value) {
        if (value instanceof List) {
            Set<String> errors = new HashSet<>();
            ((List<?>) value).forEach(i -> {
                try {
                    validator.validate(i);
                } catch (ValidationException e) {
                    errors.add(e.getMessage());
                }
            });

            if (!errors.isEmpty()) {
                throw new ValidationException(StringUtils.join(errors, BaseValidator.ERRORS_SPLIT));
            }
        } else {
            try {
                validator.validate(value);
            } catch (ValidationException e) {
                throw e;
            }
        }

        return true;
    }
}
