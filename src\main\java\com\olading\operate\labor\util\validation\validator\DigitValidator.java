package com.olading.operate.labor.util.validation.validator;

import com.lanmaoly.util.lang.ValidateUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Digit;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class DigitValidator extends BaseValidator<Digit> {
    public static final int MAX_LENGTH = 999;
    public static final int MIN_LENGTH = 1;

    private int maxLength = MAX_LENGTH;

    private int minLength = MIN_LENGTH;

    public DigitValidator() {}

    public DigitValidator(String name) {
        setName(name);
    }

    public void setMinLength(int minLength) {
        this.minLength = minLength;
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    @Override
    public void initialize(Digit constraintAnnotation) {
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        maxLength = constraintAnnotation.maxLength();
        minLength = constraintAnnotation.minLength();
        super.initialize(constraintAnnotation);
    }

    @Override
    protected boolean constraintCheck(Object o) {
        try {
            return ValidateUtils.checkDigit(String.valueOf(o), minLength, maxLength);
        } catch (ValidationException e) {
            e.setName(getName());
            throw e;
        }
    }
}
