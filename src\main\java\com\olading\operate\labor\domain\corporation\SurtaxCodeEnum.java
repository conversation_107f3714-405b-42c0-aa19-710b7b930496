package com.olading.operate.labor.domain.corporation;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @time: 2025/7/18 18:54
 */
public enum SurtaxCodeEnum {
    /**
     * 城建税
     */
    URBAN_MAINTENANCE_TAX("城建税"),
    /**
     * 教育费附加
     */
    EDUCATION_SURCHARGE("教育费附加"),
    /**
     * 地方教育附加
     */
    LOCAL_EDUCATION_SURCHARGE("地方教育附加");

    private final String name;
    SurtaxCodeEnum(String name) {
        this.name = name;
    }

    public static List<SurtaxData> getInitSurtaxData(){
        return List.of(
                SurtaxData.builder()
                        .name("城建税")
                        .surtaxCode(SurtaxCodeEnum.URBAN_MAINTENANCE_TAX)
                        .rate(BigDecimal.ZERO)
                        .discount_rate(BigDecimal.ZERO)
                        .build(),
                SurtaxData.builder()
                        .name("教育费附加")
                        .surtaxCode(SurtaxCodeEnum.EDUCATION_SURCHARGE)
                        .rate(BigDecimal.ZERO)
                        .discount_rate(BigDecimal.ZERO)
                        .build(),
                SurtaxData.builder()
                        .name("地方教育附加")
                        .surtaxCode(SurtaxCodeEnum.LOCAL_EDUCATION_SURCHARGE)
                        .rate(BigDecimal.ZERO)
                        .discount_rate(BigDecimal.ZERO)
                        .build()
        );
    }


}
