package com.olading.operate.labor.domain.proxy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.CorporationPayChannelEntity;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.proxy.channel.ChannelRemitOrderEntity;
import com.olading.operate.labor.domain.proxy.channel.ChannelRemitOrderRepo;
import com.olading.operate.labor.domain.proxy.channel.QChannelRemitOrderEntity;
import com.olading.operate.labor.domain.proxy.channel.RemitStatusEnum;
import com.olading.operate.labor.domain.proxy.order.*;
import com.olading.operate.labor.domain.salary.QSalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.info.InfoManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/10 19:52
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class ProxyOrderManager {
    private final EntityManager em;
    private final ChannelRemitOrderRepo channelRemitOrderRepo;
    private final CorporationManager corporationManager;
    private final InfoManager infoManager;

    QProxyOrderEntity qProxyOrder = QProxyOrderEntity.proxyOrderEntity;
    QProxyBatchEntity qProxyBatch = QProxyBatchEntity.proxyBatchEntity;
    QSalaryDetailEntity  qSalaryDetail = QSalaryDetailEntity.salaryDetailEntity;
    QChannelRemitOrderEntity qChannelRemitOrder = QChannelRemitOrderEntity.channelRemitOrderEntity;
    QCustomerEntity qCustomer = QCustomerEntity.customerEntity;
    QBusinessContractEntity qBusinessContract = QBusinessContractEntity.businessContractEntity;

    public void processRemitAcceptFail(ChannelRemitOrderEntity order,String errorCode, String errorMessage) {

        channelRemitOrderRepo.processRemitAcceptFail(order,errorCode, errorMessage);
        final ProxyOrderEntity proxyOrderEntity = em.find(ProxyOrderEntity.class, order.getProxyOrderId());
        proxyOrderEntity.setStatus(ProxyOrderStatusEnum.FAIL);
        proxyOrderEntity.setErrorCode(errorCode);
        proxyOrderEntity.setLastErrorInfo(errorMessage);
        em.merge(proxyOrderEntity);
    }

    public ProxyOrderEntity findProxyOrder(Long id, LockModeType lock ) {
        return em.find(ProxyOrderEntity.class, id, lock);
    }

    public List<ProxyOrderEntity> findProxyOrderByBatchId(Long id) {
        return queryProxyOrder(t -> t.proxyBatchId.eq(id)).fetch();
    }


    private JPAQuery<ProxyOrderEntity> queryProxyOrder(Function<QProxyOrderEntity, Predicate> condition) {
        QProxyOrderEntity t = QProxyOrderEntity.proxyOrderEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<ProxyBatchEntity> queryProxyBatchOrder(Function<QProxyBatchEntity, Predicate> condition) {
        return new JPAQueryFactory(em)
                .select(qProxyBatch)
                .from(qProxyBatch)
                .where(condition.apply(qProxyBatch));
    }


    private Long countProxyOrder(Function<QProxyOrderEntity, Predicate> condition) {
        return new JPAQueryFactory(em)
                .select(qProxyOrder.count())
                .from(qProxyOrder)
                .where(condition.apply(qProxyOrder))
                .fetchOne();
    }

    private BigDecimal sumProxyOrder(Function<QProxyOrderEntity, Predicate> condition) {
        return new JPAQueryFactory(em)
                .select(qProxyOrder.amount.sum())
                .from(qProxyOrder)
                .where(condition.apply(qProxyOrder))
                .fetchOne();
    }

    public void processRemitSuccess(ChannelRemitOrderEntity order, String errorCode, String errorMessage) {
        order.setStatus(RemitStatusEnum.REMIT);
        order.setFinishTime(LocalDateTime.now());
        em.merge(order);
        final ProxyOrderEntity proxyOrderEntity = em.find(ProxyOrderEntity.class, order.getProxyOrderId());
        proxyOrderEntity.setStatus(ProxyOrderStatusEnum.REMIT);
        proxyOrderEntity.setCompleteTime(LocalDateTime.now());
        em.merge(proxyOrderEntity);
    }

    public void processBatchStatus(Long batchId){
        if(batchId == null){
            return;
        }
        ProxyBatchEntity proxyBatchEntity = em.find(ProxyBatchEntity.class, batchId);
        switch (proxyBatchEntity.getBatchStatus()){
            case CHECK:
                processProxBatch(proxyBatchEntity);
                break;
            case PROCESSING:
                completeProxBatch(proxyBatchEntity);
                break;
            default:
                break;
        }

    }


    private void processProxBatch(ProxyBatchEntity proxyBatchEntity){
        if (proxyBatchEntity.getBatchStatus() != ProxyBatchStatusEnum.CHECK){
            log.info("批次状态非核验中不能处理,{},{}", proxyBatchEntity.getBatchStatus(), proxyBatchEntity.getId());
            return;
        }
        if (!canProcessBatch(proxyBatchEntity.getId(), proxyBatchEntity)) {
            return;
        }
        final Long total = countProxyOrder(t -> t.proxyBatchId.eq(proxyBatchEntity.getId()).and(t.status.in(ProxyOrderStatusEnum.PROCESSING)));
        if(total > 0){
            proxyBatchEntity.setBatchStatus(ProxyBatchStatusEnum.PROCESSING);
            em.merge(proxyBatchEntity);
        }
    }

    private void completeProxBatch(ProxyBatchEntity proxyBatchEntity){
        if (proxyBatchEntity.getBatchStatus() != ProxyBatchStatusEnum.PROCESSING){
            log.info("批次状态不是处理中,不能处理,{},{}", proxyBatchEntity.getBatchStatus(), proxyBatchEntity.getId());
            return;
        }
        final List<ProxyOrderEntity> fetch = queryProxyOrder(t -> t.proxyBatchId.eq(proxyBatchEntity.getId())).fetch();
        if(CollectionUtil.isEmpty(fetch)){
            proxyBatchEntity.setBatchStatus(ProxyBatchStatusEnum.COMPLETE);
            em.merge(proxyBatchEntity);
            return;
        }

        final Long total = countProxyOrder(t -> t.proxyBatchId.eq(proxyBatchEntity.getId()).and(t.status.in(ProxyOrderStatusEnum.REMIT, ProxyOrderStatusEnum.REFUND, ProxyOrderStatusEnum.FAIL)));
        if(Objects.equals(total, proxyBatchEntity.getCount())){
            proxyBatchEntity.setBatchStatus(ProxyBatchStatusEnum.COMPLETE);
            em.merge(proxyBatchEntity);
        }
    }

    private boolean canProcessBatch(long batchId, ProxyBatchEntity proxyBatchEntity) {
        final List<ProxyOrderEntity> fetch = queryProxyOrder(t -> t.proxyBatchId.eq(batchId)).fetch();
        if(CollectionUtil.isEmpty(fetch) && proxyBatchEntity.getCount() > 0){
            log.info("批次数据异常不处理");
            return false;
        }
        return true;
    }




    public void processRefund(ChannelRemitOrderEntity order, String errorCode, String errorMessage) {
        order.setStatus(RemitStatusEnum.REFUND);
        order.setFinishTime(LocalDateTime.now());
        order.setErrorCode(errorCode);
        order.setErrorReason(errorMessage);
        em.merge(order);
        final ProxyOrderEntity proxyOrderEntity = em.find(ProxyOrderEntity.class, order.getProxyOrderId());
        proxyOrderEntity.setStatus(ProxyOrderStatusEnum.REFUND);
        proxyOrderEntity.setCompleteTime(LocalDateTime.now());
        proxyOrderEntity.setErrorCode(errorCode);
        proxyOrderEntity.setLastErrorInfo(errorMessage);
        em.merge(proxyOrderEntity);
    }

    public ProxyBatchEntity queryUnCompleteBatchByStatementId(Long statementId) {
        return queryProxyBatchOrder(t -> t.salaryStatementId.eq(statementId).and(t.batchStatus.notIn(ProxyBatchStatusEnum.COMPLETE))).fetchOne();
    }

    public List<SalaryDetailEntity> queryCanProxySalaryDetail(Long statementId) {
        QSalaryDetailEntity detailEntity = QSalaryDetailEntity.salaryDetailEntity;
        return new JPAQueryFactory(em)
                .select(detailEntity).distinct()
                .from(detailEntity)
                .leftJoin(qProxyOrder)
                .on(qProxyOrder.salaryDetailId.eq(detailEntity.id))
                .where(detailEntity.salaryStatementId.eq(statementId).and(detailEntity.deleted.eq(false))
                        .and(qProxyOrder.status.isNull().or(qProxyOrder.status.in(ProxyOrderStatusEnum.CHECK_FAIL, ProxyOrderStatusEnum.FAIL, ProxyOrderStatusEnum.REFUND))))
                .fetch();
    }

    public void saveBatch(ProxyBatchEntity proxyBatchEntity) {
        em.persist(proxyBatchEntity);

    }

    public void saveOrders(List<ProxyOrderEntity> orderEntities) {
        orderEntities.forEach(em::persist);
    }

    public Pair<ProxyBatchEntity, List<ProxyOrderEntity>> createBatch(TenantInfo tenantInfo, SalaryStatementEntity statementEntity, List<SalaryDetailEntity> salaryDetails, CorporationPayChannelEntity payChannel, String remark) {
        final ProxyBatchEntity proxyBatchEntity = ofStatement(tenantInfo, statementEntity, salaryDetails, remark);
        proxyBatchEntity.setPayChannel(payChannel.getPayChannel());
        this.saveBatch(proxyBatchEntity);
        final List<ProxyOrderEntity> orderEntities = salaryDetails.stream().map(detail -> ofDetail(proxyBatchEntity, detail)).collect(Collectors.toList());
        this.saveOrders(orderEntities);
        return Pair.of(proxyBatchEntity, orderEntities);
    }

    private ProxyOrderEntity ofDetail(ProxyBatchEntity proxyBatchEntity, SalaryDetailEntity salaryDetail) {
        final ProxyOrderEntity proxyOrderEntity = new ProxyOrderEntity(proxyBatchEntity.getTenant());
        proxyOrderEntity.setName(salaryDetail.getName());
        proxyOrderEntity.setIdCard(salaryDetail.getIdCard());
        proxyOrderEntity.setSalaryDetailId(salaryDetail.getId());
        proxyOrderEntity.setProxyBatchId(proxyBatchEntity.getId());
        proxyOrderEntity.setAmount(salaryDetail.getNetPayment());
        proxyOrderEntity.setStatus(ProxyOrderStatusEnum.CREATE);
        proxyOrderEntity.setCustomerId(proxyBatchEntity.getCustomerId());
        proxyOrderEntity.setSupplierId(proxyBatchEntity.getSupplierId());
        proxyOrderEntity.setSupplierCorporationId(proxyBatchEntity.getSupplierCorporationId());
        proxyOrderEntity.setContractId(proxyBatchEntity.getContractId());
        proxyOrderEntity.setPayChannel(proxyBatchEntity.getPayChannel());
        proxyOrderEntity.setRemark(proxyBatchEntity.getRemark());
        return proxyOrderEntity;

    }


    private ProxyBatchEntity ofStatement(TenantInfo tenantInfo, SalaryStatementEntity statementEntity, List<SalaryDetailEntity> salaryDetail, String remark) {
        final ProxyBatchEntity proxyBatchEntity = new ProxyBatchEntity(tenantInfo);
        proxyBatchEntity.setBatchStatus(ProxyBatchStatusEnum.CHECK);
        proxyBatchEntity.setSalaryStatementId(statementEntity.getId());
        proxyBatchEntity.setSupplierId(statementEntity.getSupplierId());
        proxyBatchEntity.setCustomerId(statementEntity.getCustomerId());
        proxyBatchEntity.setSupplierCorporationId(statementEntity.getSupplierCorporationId());
        proxyBatchEntity.setContractId(statementEntity.getContractId());
        proxyBatchEntity.setCount((long) salaryDetail.size());
        proxyBatchEntity.setTotalAmount(salaryDetail.stream().map(SalaryDetailEntity::getNetPayment).reduce(BigDecimal.ZERO, BigDecimal::add));
        proxyBatchEntity.setRemark(remark);
        return proxyBatchEntity;
    }

    public void saveOrder(ProxyOrderEntity proxyOrderEntity) {
        em.merge(proxyOrderEntity);
    }

    public ProxyBatchEntity requiredProxyBatch(Long batchId) {
        final ProxyBatchEntity proxyBatchEntity = em.find(ProxyBatchEntity.class, batchId, LockModeType.PESSIMISTIC_WRITE);
        if (proxyBatchEntity == null) {
            throw new IllegalStateException("批次不存在");
        }
        return proxyBatchEntity;
    }

    /**
     * 删除整个批次
     */
    public void remove(Long batchId) {
        final ProxyBatchEntity proxyBatchEntity = em.find(ProxyBatchEntity.class,batchId, LockModeType.PESSIMISTIC_WRITE);
        em.remove(proxyBatchEntity);
        final List<ProxyOrderEntity> orderEntities = queryProxyOrder(t -> t.proxyBatchId.eq(batchId)).fetch();
        if (orderEntities != null) {
            orderEntities.forEach(em::remove);
        }
    }

    /**
     * 变更批次为出款中
     */
    public void processBatch(ProxyBatchEntity proxyBatch, List<ProxyOrderEntity> orderEntities) {
        proxyBatch.setBatchStatus(ProxyBatchStatusEnum.PROCESSING);
        proxyBatch.setConfirmTime(LocalDateTime.now());
        em.merge(proxyBatch);
        orderEntities.forEach(order->{
            switch (order.getStatus()){
                case CHECK_SUCC:
                    order.setStatus(ProxyOrderStatusEnum.PROCESSING);
                    break;
                case CHECK_FAIL:
                    order.setStatus(ProxyOrderStatusEnum.FAIL);
                    break;
                case CREATE:
                    order.setStatus(ProxyOrderStatusEnum.FAIL);
                    break;
                default:
                    break;
            }
            em.merge(order);
        });
    }

    public ProxyOrderBatchDetailData getOrderBatchDetailData(Long batchId){
        ProxyBatchEntity proxyBatchEntity = em.find(ProxyBatchEntity.class, batchId);
        if(proxyBatchEntity == null){
            return null;
        }

        ProxyOrderBatchDetailData proxyOrderBatchDetailData = ProxyOrderBatchDetailData.of(proxyBatchEntity);
        SupplierCorporationEntity corporationEntity = em.find(SupplierCorporationEntity.class, proxyBatchEntity.getSupplierCorporationId());
        proxyOrderBatchDetailData.setCorporation(corporationEntity.getName());

        CustomerEntity customerEntity = em.find(CustomerEntity.class, proxyBatchEntity.getCustomerId());
        proxyOrderBatchDetailData.setCustomer(customerEntity.getName());

        BusinessContractEntity businessContractEntity = em.find(BusinessContractEntity.class, proxyBatchEntity.getContractId());
        proxyOrderBatchDetailData.setBusinessContract(businessContractEntity.getName());

        final EnterpriseInfoEntity enterpriseInfo = infoManager.getEnterpriseInfo(OwnerType.CORPORATION, proxyBatchEntity.getSupplierCorporationId());
        proxyOrderBatchDetailData.setCorporationContactPhone(enterpriseInfo.getContactPhone());

        proxyOrderBatchDetailData.setCheckSuccCount( countProxyOrder(t -> t.proxyBatchId.eq(batchId).and(t.status.in(ProxyOrderStatusEnum.CHECK_SUCC))));
        proxyOrderBatchDetailData.setCheckFailCount(countProxyOrder(t -> t.proxyBatchId.eq(batchId).and(t.status.in(ProxyOrderStatusEnum.CHECK_FAIL))));
        proxyOrderBatchDetailData.setCreateCount(countProxyOrder(t -> t.proxyBatchId.eq(batchId).and(t.status.in(ProxyOrderStatusEnum.CREATE))));
        proxyOrderBatchDetailData.setCheckSuccAmount(sumProxyOrder(t -> t.proxyBatchId.eq(batchId).and(t.status.in(ProxyOrderStatusEnum.CHECK_SUCC))));
        proxyOrderBatchDetailData.setCheckFailAmount(sumProxyOrder(t -> t.proxyBatchId.eq(batchId).and(t.status.in(ProxyOrderStatusEnum.CHECK_FAIL))));
        proxyOrderBatchDetailData.setCreateAmount(sumProxyOrder(t -> t.proxyBatchId.eq(batchId).and(t.status.in(ProxyOrderStatusEnum.CREATE))));
        return proxyOrderBatchDetailData;
    }

    public List<ProxyOrderData> getOrderDatas(Long batchID) {

        List<Tuple> fetch = new JPAQueryFactory(em)
                .select(qProxyOrder, qSalaryDetail)
                .from(qProxyOrder)
                .leftJoin(qSalaryDetail).on(qProxyOrder.salaryDetailId.eq(qSalaryDetail.id))
                .where(qProxyOrder.proxyBatchId.eq(batchID)).fetch();
        if(fetch == null) return List.of();
        return fetch.stream().map(tuple -> ProxyOrderData.of(tuple.get(qProxyOrder), tuple.get(qSalaryDetail))).collect(Collectors.toList());
    }


    public List<ChannelRemitOrderEntity> queryProcessing(LocalDateTime start,LocalDateTime end) {
        final JPAQuery<ChannelRemitOrderEntity> query = new JPAQueryFactory(em).selectFrom(qChannelRemitOrder);
        query.where(qChannelRemitOrder.status.in(RemitStatusEnum.REMITTING).and(qChannelRemitOrder.createTime.between(start, end)));
        return query.fetch();
    }

    public List<ChannelRemitOrderEntity> queryNoVoucher(LocalDateTime start,LocalDateTime end) {
        final JPAQuery<ChannelRemitOrderEntity> query = new JPAQueryFactory(em).selectFrom(qChannelRemitOrder);
        query.where(qChannelRemitOrder.status.in(RemitStatusEnum.REMIT).and(qChannelRemitOrder.createTime.between(start, end)).and(qChannelRemitOrder.voucher.isNull()));
        return query.fetch();
    }

    private JPAQuery<ChannelRemitOrderEntity> queryChannelRemitOrder(Function<QChannelRemitOrderEntity, Predicate> condition) {
        return new JPAQueryFactory(em).selectFrom(qChannelRemitOrder).where(condition.apply(qChannelRemitOrder));
    }

    public void processRemit(ChannelRemitOrderEntity order) {
        order.setStatus(RemitStatusEnum.REMITTING);
        em.merge(order);
    }

    public List<ProxyBatchEntity> queryUnCompleteBatch(LocalDateTime start, LocalDateTime end) {
        return queryProxyBatchOrder(t -> t.batchStatus.ne(ProxyBatchStatusEnum.COMPLETE).and(t.createTime.between(start, end))).fetch();
    }
}
