package com.olading.operate.labor.domain.supplier;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("支付通道配置项")
@Entity
@Table(name = "t_pay_channel_config")
public class PayChannelConfigEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Size(max = 64)
    @NotNull
    @Comment("通道编码")
    @Column(name = "pay_channel", nullable = false, length = 64)
    private String payChannel;

    @Size(max = 64)
    @NotNull
    @Comment("通道名称")
    @Column(name = "channel_name", nullable = false, length = 64)
    private String channelName;

    @NotNull
    @Comment("通道配置项(json)")
    @Lob
    @Column(name = "pay_channel_config", nullable = false)
    private String payChannelConfig;

}