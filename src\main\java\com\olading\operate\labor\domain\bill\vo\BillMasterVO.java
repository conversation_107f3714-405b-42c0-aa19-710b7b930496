package com.olading.operate.labor.domain.bill.vo;

import com.olading.operate.labor.domain.bill.BillMasterStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账单主表视图对象
 */
@Data
@Schema(description = "账单主表信息")
public class BillMasterVO {

    @Schema(description = "账单ID")
    private Long id;

    @Schema(description = "账单编号")
    private String billNo;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "客户合同ID")
    private Long contractId;

    @Schema(description = "合同名称")
    private String contractName;

    @Schema(description = "账单月份")
    private LocalDate billMonth;

    @Schema(description = "账单应收总费用")
    private BigDecimal totalReceivableAmount;

    @Schema(description = "薪酬费用总额")
    private BigDecimal salaryAmount;

    @Schema(description = "管理费总额")
    private BigDecimal managementFeeAmount;

    @Schema(description = "其他费用总额")
    private BigDecimal otherFeeAmount;

    @Schema(description = "开票总金额")
    private BigDecimal totalInvoiceAmount;

    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;

    @Schema(description = "已收款金额")
    private BigDecimal receivedAmount;

    @Schema(description = "账单状态")
    private BillMasterStatus billStatus;

    @Schema(description = "账单状态描述")
    private String billStatusDesc;

    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

    @Schema(description = "确认人ID")
    private Long confirmUserId;

    @Schema(description = "确认人姓名")
    private String confirmUserName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "账单分类统计")
    private List<BillCategoryVO> categories;
}