package com.olading.operate.labor.domain.share.signing.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "上传模板文件请求参数")
public class UploadTemplateFileRequest extends BaseRequest<UploadTemplateFileRequest> {

    @Schema(description = "上传文件URL", required = true)
    private String uploadUrl;
    @Schema(description = "token(授权接口返回token)", required = true)
    private String token;

}
