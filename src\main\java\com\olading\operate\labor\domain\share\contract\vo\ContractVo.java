package com.olading.operate.labor.domain.share.contract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ContractVo {
    @Schema(description = "合同ID")
    private Long id;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "合同名称")
    private String name;

    @Schema(description = "合同编号")
    private String sn;

    @Schema(description = "合同期限是否固定")
    private Boolean timeFixed;

    @Schema(description = "开始日期")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    private LocalDate endDate;

    @Schema(description = "是否提前中止")
    private Boolean stopped;

    @Schema(description = "提前中止时间")
    private Instant stopTime;

    @Schema(description = "提前中止原因")
    private String stopReason;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "附件IDs")
    private String fileIds;

    @Schema(description = "发票抬头")
    private String invoiceTitle;

    @Schema(description = "税号")
    private String invoiceTaxNo;

    @Schema(description = "开户行")
    private String invoiceBankName;

    @Schema(description = "银行账号")
    private String invoiceBankAccount;

    @Schema(description = "注册地址")
    private String invoiceRegisterAddress;

    @Schema(description = "企业电话")
    private String invoiceCompanyTel;

    @Schema(description = "发票备注")
    private String invoiceRemark;

    @Schema(description = "计算规则")
    private String manageCalculationRule;

    @Schema(description = "金额")
    private BigDecimal manageAmount;

    @Schema(description = "费率")
    private BigDecimal manageRate;

    @Schema(description = "操作者id")
    private Long creatorId;

    @Schema(description = "修改者id")
    private Long updaterId;

    @Schema(description = "作业主体ID")
    private Long supplierId;

    @Schema(description = "作业主体名称")
    private String supplierName;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "角色Id")
    private List<Long> roleIds;

    @Schema(description = "合同状态")
    private String status;

    @Comment("创建时间")
    protected LocalDateTime createTime;

}
