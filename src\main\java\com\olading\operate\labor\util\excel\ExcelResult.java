package com.olading.operate.labor.util.excel;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/24 15:21
 */
public class ExcelResult<T extends ExcelRow>{


    /**
     * 表头数据
     */
    private ExcelHeader excelHeader;

    /**
     * 数据列表
     */
    private List<T> results = new ArrayList<>();

    public ExcelHeader getExcelHeader() {
        return excelHeader;
    }

    public void setExcelHeader(ExcelHeader excelHeader) {
        this.excelHeader = excelHeader;
    }

    public List<T> getResults() {
        return results;
    }

    public void setResults(List<T> results) {
        this.results = results;
    }

    /**
     * 创建空的实例（主要用于导出文件用）
     * @param pojoClass
     * @return
     */
    public static ExcelResult newInstance(Class<? extends ExcelRow> pojoClass) {
        return newInstance(pojoClass, null);
    }

    /**
     * 创建空的实例（主要用于导出文件用）
     * @param pojoClass
     * @param mapping PojoClass的name与Excel的title的映射
     * @return
     */
    public static ExcelResult newInstance(Class<? extends ExcelRow> pojoClass, Map<String, String> mapping) {
        ExcelHeader excelHeader = new ExcelHeader();
        ExcelResult excelResult = new ExcelResult();
        excelResult.setExcelHeader(excelHeader);

        Map<String, ExcelColumnEntity> pojoColumnMap = ExcelHelper.getColumnMap(pojoClass);
        pojoColumnMap.values().stream().forEach(i -> {
            if (null != mapping && mapping.containsKey(i.getName())) {
                i.setExcelTitle(mapping.get(i.getName()));
            } else {
                i.setExcelTitle(i.getName());
            }

            excelHeader.put(i);
        });

        return excelResult;
    }

    public List<String> getHeaderTitles()
    {
        if (null != excelHeader) {
            return excelHeader.getTitleListByColumnIndex();
        }

        return new ArrayList<>();
    }

    public List<List<Object>> getObjectListResults(boolean appendError)
    {
        List<List<Object>> lists = new ArrayList<>();
        results.forEach(i -> {
            lists.add(i.getDataListByColumnIndex(appendError));
        });

        return lists;
    }

    public List<T> getSuccessResults() {
        return results.stream().filter(i -> !i.isVerifyFail()).collect(Collectors.toList());
    }

    public int getSuccessCount() {
        return getSuccessResults().size();
    }

    public List<T> getFailResults() {
        return results.stream().filter(i -> i.isVerifyFail()).collect(Collectors.toList());
    }

    public int getFailCount() {
        return getFailResults().size();
    }

    public boolean hasVerifyFailed() {
        return !getFailResults().isEmpty();
    }

    public List<T> getWriteList(ExcelWriterMode mode) {
        List<T> list = new ArrayList<>();
        switch (mode) {
            case ONLY_WRITE_SUCCESS:{
                list.addAll(getSuccessResults());
                break;
            }
            case ONLY_WRITE_FAILED:{
                list.addAll(getFailResults());
                break;
            }
            case WRITE_SUCCESS_FIRST:{
                list.addAll(getSuccessResults());
                list.addAll(getFailResults());
                break;
            }
            case WRITE_FAILED_FIRST: {
                list.addAll(getFailResults());
                list.addAll(getSuccessResults());
                break;
            }
            default: {
                list = results;
            }
        }

        return list;
    }

    public void addRow(T row) {
        results.add(row);
    }

    public boolean isEmpty() {
        if (null != excelHeader && !excelHeader.isEmpty()) {
            return false;
        }

        return results.isEmpty();
    }

    public boolean isResultEmpty() {
        return results.isEmpty();
    }

    public int getResultSize() {
        return results.size();
    }

}
