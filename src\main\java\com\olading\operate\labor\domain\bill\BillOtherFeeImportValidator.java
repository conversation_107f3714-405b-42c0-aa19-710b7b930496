package com.olading.operate.labor.domain.bill;

import com.lanmaoly.util.lang.StringUtils;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportRow;
import com.olading.operate.labor.domain.bill.dto.ImportValidationError;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 其他费用导入数据验证器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BillOtherFeeImportValidator {
    
    /**
     * 验证导入数据
     */
    public List<ImportValidationError> validateImportData(BillMasterEntity bill, 
                                                         List<BillOtherFeeImportRow> rows) {
        List<ImportValidationError> errors = new ArrayList<>();

        if(rows.isEmpty()){
            throw ApiException.apiError("导入数据不能为空!");
        }
        
        for (int i = 0; i < rows.size(); i++) {
            BillOtherFeeImportRow row = rows.get(i);

            // Excel行号从第2行开始（第1行是表头）
            int rowNum = i + 2;
            
            // 1. 必填字段验证
            validateRequiredFields(row, rowNum, errors);
            
            // 2. 数据格式验证
            validateDataFormat(row, rowNum, errors);
            
            // 3. 业务规则验证
            validateBusinessRules(bill, row, rowNum, errors);
        }
        
        return errors;
    }
    
    private void validateRequiredFields(BillOtherFeeImportRow row, int rowNum, 
                                      List<ImportValidationError> errors) {
        if (StringUtils.isBlank(row.getFeeName())) {
            errors.add(new ImportValidationError(rowNum, "费用名称", "费用名称不能为空"));
        }
        
        if (row.getFeeAmount() == null) {
            errors.add(new ImportValidationError(rowNum, "费用金额", "费用金额不能为空"));
        }
        
        if (row.getOccurDate() == null) {
            errors.add(new ImportValidationError(rowNum, "产生时间", "产生时间不能为空"));
        }
        
        if (StringUtils.isBlank(row.getLaborName())) {
            errors.add(new ImportValidationError(rowNum, "产生人", "产生人不能为空"));
        }
    }
    
    private void validateDataFormat(BillOtherFeeImportRow row, int rowNum, 
                                  List<ImportValidationError> errors) {
        // 费用金额验证
        if (row.getFeeAmount() != null) {
            if (row.getFeeAmount().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add(new ImportValidationError(rowNum, "费用金额", "费用金额必须大于0"));
            }
            // 验证小数位数
            if (row.getFeeAmount().scale() > 2) {
                errors.add(new ImportValidationError(rowNum, "费用金额", "费用金额最多保留2位小数"));
            }
        }
        
        // 身份证号验证
        if (StringUtils.isNotBlank(row.getIdCard())) {
            if (!isValidIdCard(row.getIdCard())) {
                errors.add(new ImportValidationError(rowNum, "身份证号", "身份证号格式不正确"));
            }
        }
        
        // 字符长度验证
        if (StringUtils.isNotBlank(row.getFeeName()) && row.getFeeName().length() > 100) {
            errors.add(new ImportValidationError(rowNum, "费用名称", "费用名称长度不能超过100字符"));
        }
        
        if (StringUtils.isNotBlank(row.getLaborName()) && row.getLaborName().length() > 50) {
            errors.add(new ImportValidationError(rowNum, "产生人", "产生人姓名长度不能超过50字符"));
        }
        
        if (StringUtils.isNotBlank(row.getFeePurpose()) && row.getFeePurpose().length() > 200) {
            errors.add(new ImportValidationError(rowNum, "费用用途", "费用用途长度不能超过200字符"));
        }
    }
    
    private void validateBusinessRules(BillMasterEntity bill, BillOtherFeeImportRow row, 
                                     int rowNum, List<ImportValidationError> errors) {
        // 验证账单状态
        if (bill.getBillStatus() != BillMasterStatus.GENERATED) {
            errors.add(new ImportValidationError(rowNum, "", "当前账单状态不允许导入其他费用"));
        }
    }
    
    /**
     * 身份证号验证
     */
    private boolean isValidIdCard(String idCard) {
        if (StringUtils.isBlank(idCard) || idCard.length() != 18) {
            return false;
        }
        
        // 简单的身份证号格式验证
        String regex = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        return idCard.matches(regex);
    }
}