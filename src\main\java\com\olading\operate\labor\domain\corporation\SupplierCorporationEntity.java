package com.olading.operate.labor.domain.corporation;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("作业主体信息")
@Entity
@Table(name = "t_supplier_corporation")
public class SupplierCorporationEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @Comment("企业信息ID")
    @Column(name = "enterprise_info_id")
    private Long enterpriseInfoId;

    @Size(max = 64)
    @NotNull
    @Comment("公司名称")
    @Column(name = "name", nullable = false, length = 64)
    private String name;

    @Size(max = 64)
    @Comment("开户行")
    @Column(name = "bank_name", nullable = false, length = 64)
    private String bankName;

    @Size(max = 64)
    @Comment("开户账号")
    @Column(name = "bank_account", nullable = false, length = 64)
    private String bankAccount;

    @Size(max = 64)
    @Comment("企业电话")
    @Column(name = "company_tel", nullable = false, length = 64)
    private String companyTel;

    @Comment("是否禁用")
    @Column(name = "disabled")
    private Boolean disabled = false;

    @Size(max = 64)
    @Comment("统一社会信用代码")
    @NotNull
    @Column(name = "social_credit_code", length = 64)
    private String socialCreditCode;

    @Size(max = 64)
    @Comment("税局给的平台UUID")
    @Column(name = "tax_uuid", length = 64)
    private String taxUuid;

    public SupplierCorporationEntity(TenantInfo tenantInfo, Long supplierId, String name, String socialCreditCode) {
        setTenant(tenantInfo);
        setSupplierId(supplierId);
        setName(name);
        setSocialCreditCode(socialCreditCode);
    }
    protected SupplierCorporationEntity() {
    }

}