package com.olading.operate.labor.app.web.biz.protocol.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ProtocolCreateDto {

    @Schema(description = "模板id")
    private Long templateId;
    @Schema(description = "协议名")
    private String protocolName;
    @Schema(description = "作业主体id")
    private Long corporationId;
    @Schema(description = "客户id")
    private Long customerId;
    @Schema(description = "服务合同id")
    private Long contractId;
    @Schema(description = "合同开始日期")
    private LocalDate startDate;
    @Schema(description = "合同结束日期")
    private LocalDate endDate;
    @Schema(description = "劳务人员信息列表id")
    private List<Long> laborInfoIdList;

}
