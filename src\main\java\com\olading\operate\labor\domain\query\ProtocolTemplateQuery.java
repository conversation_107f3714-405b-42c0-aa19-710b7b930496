package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.enums.ProtocolTempEnum;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolTemplateEntity;

import com.olading.operate.labor.domain.share.protocol.QCorporationProtocolTempRangeEntity;
import com.olading.operate.labor.domain.share.protocol.QCorporationProtocolTemplateEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;


public class ProtocolTemplateQuery implements EntityQuery<QueryFilter<ProtocolTemplateQuery.Filters>, CorporationProtocolTemplateEntity> {

    private final QCorporationProtocolTemplateEntity t = QCorporationProtocolTemplateEntity.corporationProtocolTemplateEntity;

    private final QCorporationProtocolTempRangeEntity t1 = QCorporationProtocolTempRangeEntity.corporationProtocolTempRangeEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (StringUtils.isNotBlank(filters.getFilters().getTempName())) {
            criteria.and(t.tempName.like(JpaUtils.fullLike(filters.getFilters().getTempName())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getTempNameWord())) {
            criteria.and(t.tempName.like(JpaUtils.fullLike(filters.getFilters().getTempNameWord())));
        }
        if (filters.getFilters().getTempStatus() != null) {
            criteria.and(t.status.eq(filters.getFilters().getTempStatus()));
        }

        if (filters.getFilters().getSupplier() != null) {
            criteria.and(t.supplierId.eq(filters.getFilters().getSupplier()));
        }
        if (filters.getFilters().getCorporationIds() != null) {
            criteria.and(t1.supplierCorporationId.in(filters.getFilters().getCorporationIds()));
        }
        criteria.and(t.deleted.eq(false));

        query.select(t)
             .distinct()
             .from(t)
             .where(criteria).groupBy(t.id).orderBy(t.id.desc());

        if (filters.getFilters().getCorporationId() != null) {
            criteria.and(t1.supplierCorporationId.eq(filters.getFilters().getCorporationId()));
            query.leftJoin(t1).on(t.id.eq(t1.templateId));
        }
        if (CollectionUtils.isNotEmpty(filters.getFilters().getCorporationIds())) {

            query.leftJoin(t1).on(t.id.eq(t1.templateId));


        }

    }

    @Override
    public CorporationProtocolTemplateEntity transform(Object v) {
        return (CorporationProtocolTemplateEntity) v;
    }


    @Data
    public static class Filters {

        private String tempName;
        private String tempNameWord;
        private String tempStatus;
        private Long supplier;
        private Long corporationId;
        private Set<Long> corporationIds;

    }

}
