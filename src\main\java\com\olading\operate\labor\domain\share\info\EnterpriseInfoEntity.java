package com.olading.operate.labor.domain.share.info;

import com.olading.boot.util.Json;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;

import java.util.Collections;
import java.util.List;

/**
 * 企业信息
 */
@Table(name = "t_enterprise_info", indexes = {
        @Index(name = "i_enterprise_info_1", columnList = "owner_id, owner_type", unique = true)
})
@Entity
public class EnterpriseInfoEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Size(max = 100)
    @Comment("名称")
    @Column(name = "name", length = 100)
    private String name;

    @Size(max = 100)
    @Comment("联系人")
    @Column(name = "contacts", length = 100)
    private String contacts;

    @Size(max = 100)
    @Comment("联系电话")
    @Column(name = "contact_phone", length = 100)
    private String contactPhone;

    @Size(max = 1024)
    @Comment("备注")
    @Column(name = "remark", length = 1024)
    private String remark;

    @Lob
    @Comment("附件文件ID列表")
    @Column(name = "attachments", length = 17000000)
    private String attachments;

    @Embedded
    private OwnedByFragment ownedBy;

    @Size(max = 64)
    @Comment("营业执照图片id")
    @Column(name = "business_license_image", length = 64)
    private String businessLicenseImage;

    @Size(max = 64)
    @Comment("统一社会信用代码")
    @Column(name = "social_credit_code", length = 64)
    private String socialCreditCode;

    @Size(max = 64)
    @Comment("法定代表人姓名")
    @Column(name = "representative_name", length = 64)
    private String representativeName;

    @Comment("法定代表人证件类型")
    @Column(name = "certificate_type")
    private CertificateTypeEnum certificateType;

    @Size(max = 64)
    @Comment("法定代表人证件号")
    @Column(name = "certificate_no", length = 64)
    private String certificateNo;

    @Size(max = 64)
    @Comment("法定代表人证件正面照")
    @Column(name = "certificate_front_image", length = 64)
    private String certificateFrontImage;

    @Size(max = 64)
    @Comment("法定代表人证件背面照")
    @Column(name = "certificate_back_image", length = 64)
    private String certificateBackImage;

    @Size(max = 64)
    @Comment("公司注册地址")
    @Column(name = "registered_address", length = 64)
    private String registeredAddress;

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getCertificateBackImage() {
        return certificateBackImage;
    }

    public void setCertificateBackImage(String certificateBackImage) {
        this.certificateBackImage = certificateBackImage;
    }

    public String getCertificateFrontImage() {
        return certificateFrontImage;
    }

    public void setCertificateFrontImage(String certificateFrontImage) {
        this.certificateFrontImage = certificateFrontImage;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public CertificateTypeEnum getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(CertificateTypeEnum certificateType) {
        this.certificateType = certificateType;
    }

    public String getRepresentativeName() {
        return representativeName;
    }

    public void setRepresentativeName(String representativeName) {
        this.representativeName = representativeName;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getBusinessLicenseImage() {
        return businessLicenseImage;
    }

    public void setBusinessLicenseImage(String businessLicenseImage) {
        this.businessLicenseImage = businessLicenseImage;
    }

    public EnterpriseInfoEntity(OwnerType ownerType, Long ownerId) {
        this.ownedBy = new OwnedByFragment(ownerType, ownerId);
    }

    protected EnterpriseInfoEntity() {
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    public String getContacts() {
        return contacts;
    }

    void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getRemark() {
        return remark;
    }

    void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getAttachments() {
        if (StringUtils.isBlank(attachments)) {
            return Collections.emptyList();
        } else {
            return Json.toList(attachments);
        }
    }

    void setAttachments(List<String> archiveId) {
        if (CollectionUtils.isNotEmpty(archiveId)) {
            this.attachments = Json.toJson(archiveId);
        } else {
            this.attachments = null;
        }
    }

    public OwnedByFragment getOwnedBy() {
        return ownedBy;
    }
}
