package com.olading.operate.labor.domain.share.signing.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "签名查询请求参数")
public class QuerySignRequest extends BaseRequest<QuerySignRequest>{

    @Schema(description = "签约流水号", required = true)
    private String flowNo;

}
