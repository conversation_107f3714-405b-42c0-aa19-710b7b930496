package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.tax.ValueAddedTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.ValueAddedTaxDeclareManager;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDeclareVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class ValueAddedTaxDeclareService {

    private final ValueAddedTaxDeclareManager valueAddedTaxDeclareManager;
    private final AuthorityManager authorityManager;

    /**
     * 新增增值税申报记录
     */
    public ValueAddedTaxDeclareEntity addValueAddedTaxDeclare(TenantInfo tenantInfo, ValueAddedTaxDeclareVo vo) {

        // 校验必填字段
        validateValueAddedTaxDeclareVo(vo);

        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareManager.addValueAddedTaxDeclare(tenantInfo, vo);

        return entity;
    }

    /**
     * 更新增值税申报记录
     */
    public ValueAddedTaxDeclareEntity updateValueAddedTaxDeclare(TenantInfo tenantInfo, ValueAddedTaxDeclareVo vo) {

        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareManager.updateValueAddedTaxDeclare(vo);

        return entity;
    }

    /**
     * 查询增值税申报记录详情
     */
    public ValueAddedTaxDeclareVo queryValueAddedTaxDeclare(Long id) {
        ValueAddedTaxDeclareVo vo = valueAddedTaxDeclareManager.queryValueAddedTaxDeclare(id);
        
        return vo;
    }
    /**
     * 删除增值税申报记录
     */
    public void deleteValueAddedTaxDeclare(TenantInfo tenantInfo, Long id) {
        // 删除记录及其明细
        valueAddedTaxDeclareManager.deleteValueAddedTaxDeclareWithDetails(id);
    }

    /**
     * 更新申报状态为已申报
     */
    public void updateTaxStatusToDeclared(Long id) {
        valueAddedTaxDeclareManager.updateTaxStatusToDeclared(id);
    }

    /**
     * 校验增值税申报记录VO
     */
    private void validateValueAddedTaxDeclareVo(ValueAddedTaxDeclareVo vo) {
        if (vo.getSupplierCorporationId() == null) {
            throw new BusinessException("作业主体ID不能为空");
        }
    }
}
