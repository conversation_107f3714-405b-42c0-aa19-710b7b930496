package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.AppProperties;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.domain.supplier.SupplierData;
import com.olading.operate.labor.util.crypto.HashPassword;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class BossService {

    public static final TenantInfo DEFAULT_TENANT = TenantInfo.nobody(TenantInfo.TenantType.BOSS);

    private final UserManager userManager;
    private final AppProperties properties;
    private final SupplierService supplierService;

    public UserEntity login(String cellphone, String password, boolean isCheck) {
        UserEntity user = userManager.getUserByCellphone(DEFAULT_TENANT, cellphone);
        if (user == null) {
            throw new BusinessException("登录失败");
        }
        if (isCheck  && !HashPassword.checkPassword(password, user.getPassword())) {
            throw new BusinessException("密码错误或用户不存在");
        }
        return user;
    }

    public void addUser(String cellphone, String name) {

        if (userManager.getUserByCellphone(DEFAULT_TENANT, cellphone) != null) {
            throw new BusinessException("该手机的用户已存在");
        }

        UserEntity user = userManager.addUser(DEFAULT_TENANT, cellphone);

        PersonInfoData info = new PersonInfoData();
        info.setName(name);
        info.setCellphone(cellphone);
        userManager.setUserInfo(user.getId(), info);
    }

    public void editUser(long userId, String name) {

        UserEntity user = userManager.getUser(DEFAULT_TENANT, userId);
        if (user == null) {
            throw new BusinessException("用户不存在: " + userId);
        }

        PersonInfoData info = new PersonInfoData();
        info.setName(name);
        userManager.setUserInfo(userId, info);
    }

    public void deleteUser(long userId) {
        UserEntity user = userManager.getUser(DEFAULT_TENANT, userId);
        if (user != null && properties.getAdminCellphone().equals(user.getCellphone())) {
            throw new BusinessException("不可删除此用户");
        }
        userManager.deleteUser(DEFAULT_TENANT, userId);
    }

}
