package com.olading.operate.labor.util;

import cn.hutool.crypto.digest.MD5;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.*;

/**
 * 全局线程池管理
 */

public class ThreadPoolUtil {
    private static Logger logger = LoggerFactory.getLogger(ThreadPoolUtil.class);
    //老业务使用的线程池
    private static ExecutorService executorService = new ThreadPoolExecutor(10, 10,
            120L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    public static ExecutorService getExecutorService() {
        return executorService;
    }

    private ThreadPoolUtil() {
    }


    /**
     * 通用线程池
     */
    private static ExecutorService commonThreadPool = new ThreadPoolExecutor(5, 5,
            120L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());


    /**
     * 捕获异常不处理，保证之前事物正常提交
     * 注意需要其他逻辑进行补偿
     */
    public static void executeCommonThreadPool(Runnable runnable) {
        try {
            ThreadPoolUtil.commonThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.info("ThreadPoolUtil-线程池：{},队列超界", "commonThreadPool", e);
        }
    }


    /**
     * 通用线程池
     */
    private static ExecutorService pubThreadPool = new ThreadPoolExecutor(5, 15,
            120L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());


    /**
     * 捕获异常不处理，保证之前事物正常提交
     * 注意需要其他逻辑进行补偿
     */
    public static void executePubThreadPool(Runnable runnable) {
        try {
            ThreadPoolUtil.pubThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.info("pubThreadPool-线程池：{},队列超界", "pubThreadPool", e);
        }
    }



    /**
     * 交易线程池 用于处理出款
     */
    private static ExecutorService transactionThreadPool = new ThreadPoolExecutor(5, 10,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(5000));


    /**
     * 捕获异常不处理，保证之前事物正常提交
     * 注意需要其他逻辑进行补偿
     */
    public static void executeTransactionThreadPool(Runnable runnable) {

        try {
            ThreadPoolUtil.transactionThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.info("ThreadPoolUtil-线程池：{},队列超界", "transactionThreadPool", e);
        }
    }


    /**
     * 补单线程池
     */
    private static ExecutorService reTryThreadPool = new ThreadPoolExecutor(5, 5,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(5000));


    /**
     * 捕获异常不处理，保证之前事物正常提交
     * 注意需要其他逻辑进行补偿
     */
    public static void executereTryThreadPool(Runnable runnable) {

        try {
            ThreadPoolUtil.reTryThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.warn("ThreadPoolUtil-线程池：{},队列超界", "reTryThreadPool", e);
        }
    }



    //--------------------------------------------------------------------------------

    /**
     * 用于异步通知发送
     */
    private static ExecutorService sendAsynMsgThreadPool = new ThreadPoolExecutor(5, 5,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000));


    public static void execute2SendMsgThreadPool(Runnable runnable) {
        try {
            ThreadPoolUtil.sendAsynMsgThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.warn("ThreadPoolUtil-线程池：{},队列超界", "sendAsynMsgThreadPool", e);
        }
    }


    //--------------------------------------------------------------------------------

    /**
     * 签约线程池
     */
    private static ExecutorService sginThreadPool = new ThreadPoolExecutor(5, 10,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(2000));


    public static void execute2sginThreadPool(Runnable runnable) {
        try {
            ThreadPoolUtil.sginThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.warn("ThreadPoolUtil-线程池：{},队列超界", "signThreadPool", e);
        }
    }


    //--------------------------------------------------------------------------------

    /**
     * 扣费
     */
    private static ExecutorService feeThreadPool = new ThreadPoolExecutor(5, 20,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(2000));


    public static void execute2feeThreadPool(Runnable runnable) {
        try {
            ThreadPoolUtil.feeThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.warn("ThreadPoolUtil-线程池：{},队列超界", "feeThreadPool", e);
        }
    }




    private static class MyRunnable implements Runnable {

        private Runnable runnable;

        private Map<String, String> mdc;

        private MyRunnable(Runnable runnable) {
            this.runnable = runnable;
            this.mdc = MDC.getCopyOfContextMap();
        }

        @Override
        public void run() {
            String spanId = MD5.create().digestHex(Thread.currentThread().getName()).substring(16);
            MDC.put("X-B3-SpanId", spanId);
            if(mdc!=null){
                MDC.put("X-B3-TraceId", mdc.get("X-B3-TraceId"));
            }
            runnable.run();
        }
    }


    /**
     * API-开票线程池
     */
    private static ExecutorService apiInvoiceThreadPool = new ThreadPoolExecutor(2, 5,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000));


    public static void executeApiInvoiceThreadPool(Runnable runnable) {
        try {
            ThreadPoolUtil.apiInvoiceThreadPool.execute(new MyRunnable(runnable));
        } catch (RejectedExecutionException e) {
            // 捕获异常不处理，保证之前事物正常提交
            // 注意需要其他逻辑进行补偿
            logger.warn("ThreadPoolUtil-线程池：{},队列超界", "apiInvoiceThreadPool", e);
        }
    }

}
