package com.olading.operate.labor.domain.bill;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 账单主表状态枚举
 */
@Getter
@RequiredArgsConstructor
public enum BillMasterStatus {
    
    /**
     * 生成中
     */
    GENERATING("生成中"),
    
    /**
     * 已生成
     */
    GENERATED("已生成"),
    
    /**
     * 待确认
     */
    PENDING_CONFIRM("待确认"),
    
    /**
     * 已确认
     */
    CONFIRMED("已确认");
    private final String description;
}