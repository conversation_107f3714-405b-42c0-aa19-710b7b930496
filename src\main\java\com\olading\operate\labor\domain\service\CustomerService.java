package com.olading.operate.labor.domain.service;


import cn.hutool.core.collection.CollectionUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.enums.CustomerCooperationStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.CustomerManager;
import com.olading.operate.labor.domain.share.customer.vo.CustomerVo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.util.crypto.HashPassword;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
public class CustomerService {

    private final UserManager userManager;
    private final CustomerManager customerManager;
    private final AuthorityManager authorityManager;

    public static final TenantInfo DEFAULT_TENANT = TenantInfo.nobody(TenantInfo.TenantType.CUSTOMER);

    public UserEntity login(String accountNo, String password) {
        UserEntity user = userManager.getUserByAccount(accountNo);
        if (user == null) {
            throw new BusinessException("登录失败");
        }
        if (!HashPassword.checkPassword(password, user.getPassword())) {
            throw new BusinessException("密码错误");
        }
        return user;
    }

    public String getCustomerId(long userId) {
        UserEntity user = userManager.requireUser(userId);
        if (user!=null){
            return user.getTenant().getId();
        }
        return null;
    }

    public CustomerEntity getCustomer(Long customerId) {
        return customerManager.getCustomer(customerId);
    }

    public void saveAccountBalanceSms(long userId, BigDecimal balanceThreshold, List<String> phoneNumbers) {
        CustomerEntity customer = getCustomer(Long.valueOf(getCustomerId(userId)));
        if (customer != null) {
            /*customer.setBalanceAlertThreshold(balanceThreshold);
            customer.setBalanceAlertPhone(phoneNumbers);*/
            customerManager.saveCustomer(customer);
        }

    }

    public CustomerEntity addCustomer(TenantInfo tenantInfo, CustomerVo vo) {
        if(!Objects.equals(tenantInfo, TenantInfo.ofSupplier(vo.getSupplierId()))){
            throw new BusinessException("操作的租户不匹配");
        }

        // 校验唯一性约束
        validateCustomerUniqueness(vo.getSupplierId(), vo.getName(), vo.getSocialCreditCode());

        CustomerEntity customerEntity = customerManager.addCustomer(tenantInfo,vo);

        if(CollectionUtil.isNotEmpty(vo.getRoleIds())){
            List<RoleData> list = vo.getRoleIds().stream().map(id -> {
                RoleData roleData = new RoleData();
                roleData.setId(id);
                return roleData;
            }).toList();
            list.forEach(o-> authorityManager.addRoleDataScopes(tenantInfo, o.getId(),new OwnedByFragment(OwnerType.CUSTOMER, customerEntity.getId())));
        }
        return customerEntity;
    }

    public CustomerEntity updateCustomerInfo(TenantInfo tenantInfo, long customerId, CustomerVo vo) {
        if(!Objects.equals(tenantInfo, TenantInfo.ofSupplier(vo.getSupplierId()))){
            throw new BusinessException("操作的租户不匹配");
        }
        CustomerEntity customerEntity = customerManager.updateCustomerInfo(customerId, vo);

        if(vo.getRoleIds() != null){
            List<RoleData> list = null;
            if(CollectionUtil.isNotEmpty(vo.getRoleIds())){
                list = vo.getRoleIds().stream().map(id -> {
                    RoleData roleData = new RoleData();
                    roleData.setId(id);
                    return roleData;
                }).toList();
            }
            authorityManager.editDataScope(tenantInfo, new OwnedByFragment(OwnerType.CUSTOMER, customerId), list);
        }
        return customerEntity;
    }

    public void updateCustomerStatus(TenantInfo tenantInfo, long customerId, String status) {
        // 校验状态值是否有效
        try {
            CustomerCooperationStatusEnum.valueOf(status);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的客户状态: " + status);
        }

        customerManager.updateCustomerStatus(customerId, status);
    }

    public CustomerWithInfo queryCustomer(long customerId) {
        CustomerWithInfo customerWithInfo = customerManager.queryCustomer(customerId);

        // 查询客户关联的角色信息
        List<RoleData> roleDataList = authorityManager.getRoleByDataScope(OwnerType.CUSTOMER, customerId);
        if (CollectionUtil.isNotEmpty(roleDataList)) {
            List<Long> roleIds = roleDataList.stream()
                    .map(RoleData::getId)
                    .collect(Collectors.toList());
            customerWithInfo.setRoleIds(roleIds);
        }

        return customerWithInfo;
    }

    public List<CustomerWithInfo> queryCustomerBySupplier(long customerId) {
        return customerManager.queryCustomerBySupplier(customerId);
    }

    /**
     * 校验客户唯一性约束
     * @param supplierId 供应商ID
     * @param name 客户名称
     * @param socialCreditCode 社会信用代码
     */
    private void validateCustomerUniqueness(Long supplierId, String name, String socialCreditCode) {
        // 校验 supplierId + name 的唯一性
        if (customerManager.existsBySupplierIdAndName(supplierId, name)) {
            throw new BusinessException("客户名称重复");
        }

        // 校验 supplierId + socialCreditCode 的唯一性
        if (socialCreditCode != null && customerManager.existsBySupplierIdAndSocialCreditCode(supplierId, socialCreditCode)) {
            throw new BusinessException("统一社会信用代码重复");
        }
    }
}
