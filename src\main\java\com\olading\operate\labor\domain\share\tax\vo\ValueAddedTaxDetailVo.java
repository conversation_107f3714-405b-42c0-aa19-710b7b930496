package com.olading.operate.labor.domain.share.tax.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ValueAddedTaxDetailVo {
    
    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "灵工平台ID")
    private Long supplierId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "增值税申报ID")
    private Long valueAddedTaxId;

    @Schema(description = "税款所属期")
    private String taxPeriod;

    @Schema(description = "申报月份")
    private String declareMonth;

    // 从业人员信息模块
    @Schema(description = "姓名")
    private String name;

    @Schema(description = "证件类型")
    private String certificateType;

    @Schema(description = "证件号码")
    private String idCard;

    @Schema(description = "国家或地区")
    private String countryRegion;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "用户唯一标识码")
    private String userUniqueCode;

    @Schema(description = "联系电话")
    private String cellPhone;

    // 增值税模块
    @Schema(description = "计税依据")
    private BigDecimal taxBasis;

    @Schema(description = "征收品目")
    private String taxItem;

    @Schema(description = "征收率")
    private String taxRate;

    @Schema(description = "本期应纳税额(增值税)")
    private BigDecimal vatAmount;

    @Schema(description = "减免性质代码(增值税)")
    private String vatExemptionCode;

    @Schema(description = "减免税额(增值税)")
    private BigDecimal vatExemptionAmount;

    @Schema(description = "应补(退)税额(增值税)")
    private BigDecimal vatPayable;

    // 城市维护建设税模块
    @Schema(description = "适用税率(城市维护建设税)")
    private String urbanTaxRate;

    @Schema(description = "本期应纳税额(城市维护建设税)")
    private BigDecimal urbanTaxAmount;

    @Schema(description = "减免性质代码(城市维护建设税)")
    private String urbanExemptionCode;

    @Schema(description = "减免税额(城市维护建设税)")
    private BigDecimal urbanExemptionAmount;

    @Schema(description = "应补(退)税额(城市维护建设税)")
    private BigDecimal urbanTaxPayable;

    // 教育附加模块
    @Schema(description = "适用税率(教育附加)")
    private String eduTaxRate;

    @Schema(description = "本期应纳税额(教育附加)")
    private BigDecimal eduTaxAmount;

    @Schema(description = "减免性质代码(教育附加)")
    private String eduExemptionCode;

    @Schema(description = "减免税额(教育附加)")
    private BigDecimal eduExemptionAmount;

    @Schema(description = "应补(退)税额(教育附加)")
    private BigDecimal eduTaxPayable;

    // 地方教育附加模块
    @Schema(description = "适用税率(地方教育附加)")
    private String localEduTaxRate;

    @Schema(description = "本期应纳税额(地方教育附加)")
    private BigDecimal localEduTaxAmount;

    @Schema(description = "减免性质代码(地方教育附加)")
    private String localEduExemptionCode;

    @Schema(description = "减免税额(地方教育附加)")
    private BigDecimal localEduExemptionAmount;

    @Schema(description = "应补(退)税额(地方教育附加)")
    private BigDecimal localEduTaxPayable;
}
