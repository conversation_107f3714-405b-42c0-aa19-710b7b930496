package com.olading.operate.labor.domain.share.signing.common;

import com.olading.operate.labor.domain.share.signing.enums.ControlType;
import com.olading.operate.labor.domain.share.signing.enums.SignWay;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description= "控件")
@Data
public class Control {

    /**
     * 控件名称
     */
    private String name;

    /**
     * 类型
     */
    private ControlType type;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 横坐标(百分比坐标)
     */
    private java.math.BigDecimal xAxis;

    /**
     * 纵坐标(百分比坐标)
     */
    private java.math.BigDecimal yAxis;

    /**
     * 宽度(相对页面百分比)
     */
    private java.math.BigDecimal width;

    /**
     * 高度(相对页面百分比 )
     */
    private java.math.BigDecimal height;

    /**
     * 字体
     */
    private String fontFamily;

    /**
     * 字体大小(相对页面百分比)
     */
    private java.math.BigDecimal fontSize;

    /**
     * 时间格式
     */
    private String dateFormat;

    /**
     * 签署方式
     */
    private SignWay signWay;

    /**
     * 签署步骤(关键字或者签署步骤)
     */
    private String stepId;

    /**
     * 默认值
     */
    private String value;

    public static Control buildControl(ApiControl apiControl){
        Control control = new Control();
        control.setName(apiControl.getName());
        control.setType(apiControl.getType());
        control.setPage(apiControl.getPage());
        control.setXAxis(apiControl.getXAxis());
        control.setYAxis(apiControl.getYAxis());
        control.setWidth(apiControl.getWidth());
        control.setHeight(apiControl.getHeight());
        control.setFontFamily(apiControl.getFontFamily());
        control.setFontSize(apiControl.getFontSize());
        control.setDateFormat(apiControl.getDateFormat());
        control.setSignWay(apiControl.getSignWay());
        control.setStepId(apiControl.getStepId());
        control.setValue(apiControl.getValue());
        return control;
    }
}
