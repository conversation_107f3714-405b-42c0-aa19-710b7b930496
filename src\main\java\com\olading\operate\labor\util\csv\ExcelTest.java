package com.olading.operate.labor.util.csv;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.Data;

import java.util.Collections;

public class ExcelTest {

    public static void main(String[] args) {

        try (ExcelWriter writer = EasyExcel.write("R:\\t.xlsx", Record.class)
                .build()) {
            WriteSheet sheet = EasyExcel.writerSheet("abc").build();
            for (int i = 0; i < 100000; i++) {
                Record record = new Record();
                record.setId((long) i);
                record.setName("name" + i);
                record.setRemark("remark" + i);
                record.setCard("******************");
                writer.write(Collections.singletonList(record), sheet);
            }
        }

    }

    @Data
    public static class Record {
        @ExcelProperty("编号")
        private Long id;
        @ExcelProperty("名字")
        private String name;
        @ExcelProperty("备注")
        private String remark;
        @ColumnWidth(100)
        @ExcelProperty("身份证")
        private String card;
    }
}
