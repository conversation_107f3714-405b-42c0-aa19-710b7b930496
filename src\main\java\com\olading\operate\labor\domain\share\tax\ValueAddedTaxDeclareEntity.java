package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("税务增值税申报表")
@Entity
@Table(name = "t_value_added_tax_declare", schema = "olading_labor")
public class ValueAddedTaxDeclareEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 20)
    @Comment("税款所属期")
    @Column(name = "tax_payment_period", length = 20)
    private String taxPaymentPeriod;

    @Size(max = 20)
    @Comment("个税申报月")
    @Column(name = "income_tax_month", length = 20)
    private String incomeTaxMonth;

    @Size(max = 20)
    @Comment("纳税人数")
    @Column(name = "taxpayers_count", length = 20)
    private String taxpayersCount;

    @Size(max = 64)
    @Comment("本期收入")
    @Column(name = "current_income", length = 64)
    private String currentIncome;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @Size(max = 20)
    @Comment("生成状态")
    @Column(name = "status", length = 20)
    private String status;

    @Size(max = 20)
    @Comment("申报状态")
    @Column(name = "tax_status", length = 20)
    private String taxStatus;

    public ValueAddedTaxDeclareEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }

    public ValueAddedTaxDeclareEntity() {
    }
}