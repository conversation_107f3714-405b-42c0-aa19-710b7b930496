package com.olading.operate.labor.app.web.biz.corporation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.query.WebApiQueryService;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.supplier.SupplierController;
import com.olading.operate.labor.domain.corporation.CorporationConfigData;
import com.olading.operate.labor.domain.corporation.CorporationData;
import com.olading.operate.labor.domain.corporation.CorporationPayChannelData;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.query.SupplierCorporationQuery;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.supplier.SupplierPayChannelData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

@Tag(name = "业务主体接口")
@RestController
@RequestMapping("/api/supplier")
@RequiredArgsConstructor
@Slf4j
public class CorporationController extends BusinessController {

    private final SupplierService supplierService;
    private final QueryService queryService;
    private final WebApiQueryService webApiQueryService;


    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @Operation(summary = "添加业务主体")
    @PostMapping(value = "addCorporation")
    public WebApiResponse<Long> addCorporation(@RequestBody @Valid SupplierCorporationDetailVo request) {
        CorporationData data = CorporationData.of(request);
        data.setSupplierId(currentSupplierId());
        data.setId(null);
        final SupplierCorporationEntity corporationEntity = supplierService.addCorporation(currentTenant(), data);
        return WebApiQueryResponse.success(corporationEntity.getId());
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @Operation(summary = "修改业务主体")
    @PostMapping(value = "editCorporation")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CORPORATION, spel = "#request.id")
    })
    public WebApiResponse<Void> editCorporation(@RequestBody @Valid SupplierCorporationDetailVo request) {
        CorporationData data = CorporationData.of(request);
        data.setSupplierId(currentSupplierId());
        supplierService.editCorporation(currentTenant(), data);
        return WebApiQueryResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @Operation(summary = "添加修改业务主体配置")
    @PostMapping(value = "editCorporationBusiness")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CORPORATION, spel = "#request.corporationId")
    })
    public WebApiResponse<Void> editCorporationBusiness(@RequestBody  @Valid  CorporationBusinessVo request) {
        CorporationData data = CorporationData.of(request);
        data.setSupplierId(currentSupplierId());
        supplierService.editCorporationBusiness(currentTenant(), data);
        return WebApiQueryResponse.success();
    }

    @Operation(summary = "查询业务主体列表")
    @PostMapping(value = "listCorporation")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.corporationIds")
    })
    public WebApiQueryResponse<SupplierCorporationEntity> listCorporation(@RequestBody QueryFilter<WebCorporationFilters> request) {
        QueryFilter<SupplierCorporationQuery.Filters> filter = request.convert(WebCorporationFilters::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        filter.sort("id", Direction.DESCENDING);
        //数据权限控制
        filter.getFilters().setCorporationIds(currentDataScope().get(OwnerType.CORPORATION));
        DataSet<SupplierCorporationEntity> ds = queryService.queryCorporation(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }


    @Operation(summary = "查询业务主体列表导出")
    @PostMapping(value = "exportCorporation")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.corporationIds")
    })
    public void exportCorporation(@RequestBody QueryFilter<WebCorporationFilters> request, HttpServletResponse response) {
        QueryFilter<SupplierCorporationQuery.Filters> filter = request.convert(WebCorporationFilters::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        filter.sort("id", Direction.DESCENDING);
        //数据权限控制
        filter.getFilters().setCorporationIds(currentDataScope().get(OwnerType.CORPORATION));
        downloadExcel(response,
                "业务主体列表.zip",
                webApiQueryService.exportCorporation(),
                filter);
    }



    @Operation(summary = "查询业务主体详情")
    @PostMapping(value = "corporationDetail")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @AuthorityDataScopGuard(value = {
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CORPORATION, spel = "#request.id")
    })
    public WebApiResponse<CorporationData> corporationDetail(@RequestBody @Valid IdRequest request) {
        CorporationData data = supplierService.getCorporation(currentTenant(), request.getId());
        return WebApiQueryResponse.success(data);
    }

    @Operation(summary = "查询业务主体可开通道列表")
    @PostMapping(value = "supplierPayChannelList")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    public WebApiResponse<List<SupplierPayChannelData>> supplierPayChannelList() {
        return WebApiResponse.success(supplierService.getPayChannelList(currentSupplierId()));
    }


    @Operation(summary = "查询业务主体配置信息")
    @PostMapping(value = "corporationConfigDetail")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @AuthorityDataScopGuard(value = {
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CORPORATION, spel = "#request.id")
    })
    public WebApiResponse<CorporationBusinessDetailVo> corporationConfigDetail(@RequestBody @Valid IdRequest request) {
        CorporationData data = supplierService.getCorporation(currentTenant(), request.getId());
        return WebApiQueryResponse.success( CorporationBusinessDetailVo.of(data));
    }


    @Operation(summary = "新增/修改支付通道")
    @PostMapping(value = "corporationConfigPayChannel")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_CORPORATION)
    @AuthorityDataScopGuard(value = {
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CORPORATION, spel = "#request.corporationId")
    })
    public WebApiResponse<Void> corporationConfigPayChannel(@RequestBody @Valid CorporationConfigPayChannel request) {
        supplierService.corporationConfigPayChannel(currentTenant(), currentSupplierId(), CorporationPayChannelData.of( request));
        return WebApiQueryResponse.success();
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class CorporationConfigPayChannel {

        @Schema(description = "业务主体ID")
        @NotNull(message = "请选择业务主体")
        private Long corporationId;

        @NotBlank(message = "通道编码不能为空")
        @Schema(description = "通道编码")
        private String payChannel;

        @Schema(description = "是否启用")
        private boolean isOpen = true;

        @Schema(description = "是否默认通道")
        private boolean isDefault =  false;

        @NotNull(message = "通道配置信息不能为空")
        @Schema(description = "通道配置信息")
        private HashMap<String,String> channelConfig;
    }


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class IdRequest {
        @Schema(description = "业务主体ID")
        @NotNull(message = "请选择业务主体")
        private Long id;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class WebCorporationFilters {

        @Schema(description = "作业主体名称")
        private String name;

        @Schema(description = "社会信用代码")
        private String socialCreditCode;

        @Schema(description = "作业主体ID")
        private Long id;

        @Schema(description = "创建时间开始")
        private LocalDateTime createTimeBegin;

        @Schema(description = "创建时间结束")
        private LocalDateTime createTimeEnd;

        @Schema(description = "作业主体ID列表", hidden = true)
        private Set<Long> corporationIds;

        public SupplierCorporationQuery.Filters convert() {
            return Beans.copyBean(this, SupplierCorporationQuery.Filters.class);
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class CorporationBusinessVo {

        @NotNull(message = "请选择业务主体")
        @Schema(description = "业务主体ID")
        private Long corporationId;

        @Schema(description = "业务配置")
        private CorporationConfigData configData;

        @Schema(description = "默认通道编码")
        private String defaultPayChannel;

        public static CorporationBusinessVo of(CorporationData data) {
            CorporationBusinessVo vo = new CorporationBusinessVo();
            vo.setCorporationId(data.getId());
            vo.setConfigData(data.getConfigData());
            vo.setDefaultPayChannel(data.getPayChannelDataList().stream().filter(CorporationPayChannelData::getIsDefault).findFirst().map(CorporationPayChannelData::getPayChannel).orElse(null));
            return vo;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class CorporationBusinessDetailVo {

        @Schema(description = "业务主体ID")
        private Long corporationId;

        @Schema(description = "业务配置")
        private CorporationConfigData configData;

        @Schema(description = "通道配置")
        private List<CorporationPayChannelData> payChannelDataList;

        public static CorporationBusinessDetailVo of(CorporationData data) {
            CorporationBusinessDetailVo vo = new CorporationBusinessDetailVo();
            vo.setCorporationId(data.getId());
            vo.setConfigData(data.getConfigData());
            vo.setPayChannelDataList(data.getPayChannelDataList());
            return vo;
        }
    }




    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class SupplierCorporationDetailVo {
        @Schema(description = "id")
        private Long id;
        @Schema(description = "灵工平台id")
        private Long supplierId;
        @NotBlank(message = "公司名称不能为空")
        @Schema(description = "公司名称")
        private String name;
        @Schema(description = "开户行")
        private String bankName;
        @Schema(description = "开户账号")
        private String bankAccount;
        @Schema(description = "企业电话")
        private String companyTel;
        @Schema(description = "税局平台UUID")
        private String taxUuid;
        @Schema(description = "营业执照图片id")
        @NotBlank(message = "营业执照图片不能为空")
        private String businessLicenseImage;
        @NotBlank(message = "统一社会信用代码不能为空")
        @Schema(description = "统一社会信用代码")
        private String socialCreditCode;
        @Schema(description = "注册地址")
        @NotBlank(message = "注册地址不能为空")
        @Size(max = 64, message = "注册地址不能超过64位")
        private String registerAddress;
        @Schema(description = "法定代表人证件正面照")
        @NotBlank(message = "法定代表人证件正面照不能为空")
        private String certificateFrontImage;
        @Schema(description = "法定代表人证件背面照")
        @NotBlank(message = "法定代表人证件背面照不能为空")
        private String certificateBackImage;
        @Schema(description = "法定代表人姓名")
        @NotBlank(message = "法定代表人姓名不能为空")
        private String representativeName;
        @Schema(description = "法定代表人证件号")
        @NotBlank(message = "法定代表人证件号不能为空")
        private String certificateNo;
        @NotBlank(message = "联系人姓名不能为空")
        @Schema(description = "联系人姓名")
        private String contactName;
        @NotBlank(message = "联系人电话不能为空")
        @Schema(description = "联系人电话")
        private String contactMobile;
        @Schema(description = "是否禁用")
        private Boolean disabled;
        @Schema(description = "角色Id")
        private List<Long> roleIds;
    }
}
