package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.salary.QSalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;

public class SalaryDetailQuery implements EntityQuery<QueryFilter<SalaryDetailQuery.Filters>, SalaryDetailEntity> {

    private final QSalaryDetailEntity t = QSalaryDetailEntity.salaryDetailEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<SalaryDetailQuery.Filters> filters) {
        BooleanBuilder builder = new BooleanBuilder();

        SalaryDetailQuery.Filters f = filters.getFilters();

        if (f.getId() != null) {
            builder.and(t.id.eq(f.getId()));
        }
        if (f.getSupplierCorporationId() != null) {
            builder.and(t.supplierCorporationId.eq(f.getSupplierCorporationId()));
        }

        query.select(t)
                .from(t)
                .where(builder);
    }

    @Override
    public SalaryDetailEntity transform(Object v) {
        return (SalaryDetailEntity) v;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t.id;
        }
        return null;
    }

    @Data
    public static class Filters {
        private Long id;
        private Long supplierCorporationId;
    }
}
