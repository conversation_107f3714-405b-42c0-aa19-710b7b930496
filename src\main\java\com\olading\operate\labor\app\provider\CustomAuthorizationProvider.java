package com.olading.operate.labor.app.provider;

import com.olading.boot.core.business.Tenant;
import com.olading.boot.core.business.UserInfo;
import com.olading.boot.core.spi.AuthorizationProvider;
import com.olading.operate.labor.domain.share.user.UserManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class CustomAuthorizationProvider implements AuthorizationProvider {

    private final UserManager userManager;

    @Override
    public UserInfo getUserInfo(String userId) {
        var user = userManager.getUser(Long.parseLong(userId));
        if (user == null) {
            return null;
        }
        return UserInfo.builder().id(userId).build();
    }

    @Override
    public boolean accessTenant(String userId, Tenant tenant) {
        return true;
    }
}
