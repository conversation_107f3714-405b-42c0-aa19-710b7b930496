package com.olading.operate.labor.app.web.biz.protocol.vo;

import com.olading.operate.labor.domain.share.signing.common.Step;
import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import com.olading.operate.labor.domain.share.signing.enums.SignType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Schema(description=("模板步骤"))
@Getter
@Setter
public class TemplateStep {

    @Schema(description = "步骤ID")
    private Long stepId;

    @Schema(description = "操作类型")
    private EnumOperateType operate;

    @Schema(description = "步骤名")
    private String stepName;

    @Schema(description = "步骤排序")
    private Integer sortBy;

    @Schema(description = "域列表")
    private List<TemplateFiled> filedList;

    public Step to() {
        Step step = new Step();
        step.setName(stepName);
        //记录公章签署人和手写签署人，抄送不计入
        if (EnumOperateType.SEAL.equals(operate)) {
            step.setSignType(SignType.ENTERPRISE);
        } else {
            step.setSignType(SignType.PERSONAL);
        }
        return step;
    }

//    public CorporationProtocolTempStepEntity toEntity(Long tempId) {
//        CorporationProtocolTempStepEntity entity = new CorporationProtocolTempStepEntity();
//        entity.setTemplateId(tempId);
//        entity.set
//    }

}
