package com.olading.operate.labor.util.csv;

import lombok.Data;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.io.IOUtils;
import org.supercsv.io.CsvListWriter;
import org.supercsv.prefs.CsvPreference;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class CsvWriter<T> implements AutoCloseable {


    private final LinkedHashMap<String, String> properties = new LinkedHashMap<>();

    private final OutputStream output;

    private final CsvListWriter writer;

    private boolean writeHeader = false;

    public CsvWriter(Class<T> beanClass, OutputStream output) {
        this.output = output;
        writer = new CsvListWriter(new OutputStreamWriter(output, StandardCharsets.UTF_8), CsvPreference.STANDARD_PREFERENCE);

        for (PropertyDescriptor descriptor : PropertyUtils.getPropertyDescriptors(beanClass)) {
            if (descriptor.getReadMethod() != null) {
                try {
                    CsvColumn column = beanClass.getDeclaredField(descriptor.getName()).getAnnotation(CsvColumn.class);
                    properties.put(descriptor.getName(), column.value());
                } catch (NoSuchFieldException e) {

                }
            }
        }
    }

    public static void main(String[] args) throws IOException {
        try (OutputStream out = Files.newOutputStream(Paths.get("R:\\t.csv"));
             CsvWriter<Record> w = new CsvWriter<>(Record.class, out)) {
            for (int i = 0; i < 1000000; i++) {
                Record record = new Record();
                record.setId((long) i);
                record.setName("name" + i);
                record.setRemark("remark" + i);
                record.setCard("'******************");
                w.write(record);
            }
        }
    }

    public void write(T bean) {

        try {
            if (!writeHeader) {
                writeHeader = true;
                output.write(0xef);
                output.write(0xbb);
                output.write(0xbf);
                writer.write(new ArrayList<>(properties.values()));
            }

            List<String> list = new ArrayList<>();
            for (Map.Entry<String, String> entry : properties.entrySet()) {
                Object value = PropertyUtils.getProperty(bean, entry.getKey());
                list.add(value == null ? "" : value.toString());
            }
            writer.write(list);
        } catch (IOException | InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void close() {
        IOUtils.closeQuietly(writer);
    }

    @Data
    public static class Record {
        @CsvColumn("编号")
        private Long id;
        @CsvColumn("名字")
        private String name;
        @CsvColumn("备注")
        private String remark;
        @CsvColumn("身份证")
        private String card;
    }
}
