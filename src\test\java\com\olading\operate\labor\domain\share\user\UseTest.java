package com.olading.operate.labor.domain.share.user;

import cn.hutool.json.JSONUtil;
import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Optional;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/15 14:42
 */
@Slf4j
public class UseTest extends BaseTest {

    @Autowired
    UserManager userManager;

    @Autowired
    UserRepository userRepository;

    @Test
    @Transactional
    public void testAdd(){

        //final UserEntity userEntity = userManager.addUser(TenantInfo.ofSupplier(1l), "18888888888", "张三");
        final Optional<UserEntity> byTenantIdAndCellphone = userRepository.findByTenantIdAndCellphone(TenantInfo.ofSupplier(9l).toTenantId(), "18259176534");
        log.info("byTenantIdAndCellphone:{}", JSONUtil.toJsonStr(byTenantIdAndCellphone));
        final Pageable page = Pageable.ofSize(10).withPage(1);
        final Page<UserEntity> activeUsers = userRepository.findActiveUsers(TenantInfo.ofSupplier(9l).toTenantId(), page);
        System.out.println(JSONUtil.toJsonPrettyStr(activeUsers));
    }

}
