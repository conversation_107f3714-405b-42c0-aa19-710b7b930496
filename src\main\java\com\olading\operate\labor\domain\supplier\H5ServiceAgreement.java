package com.olading.operate.labor.domain.supplier;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class H5ServiceAgreement{
    @Size(max = 200)
    @Schema(description = "服务协议文件名称")
   private String name;
    @Size(max = 255)
    @Schema(description = "服务协议文件id")
   private String fileId;
    public H5ServiceAgreement(){}
    public H5ServiceAgreement(String name, String fileId) {
        this.name = name;
        this.fileId = fileId;
    }
}