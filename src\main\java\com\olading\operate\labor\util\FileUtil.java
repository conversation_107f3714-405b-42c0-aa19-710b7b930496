package com.olading.operate.labor.util;

import com.olading.operate.labor.domain.ApiException;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 文件操作工具类
 */
public class FileUtil {
    /**
     * 字节数组，输出文件流 转file对象
     */
    public static File writeBytesToFile(byte[] b, String outputFile) throws IOException {
        File file;
        FileOutputStream os = null;
        try {
            file = new File(outputFile);
            if (!file.exists()) {
                file.createNewFile();
            }
            os = new FileOutputStream(file);
            os.write(b);
        } finally {
            if (os != null) {
                os.close();
            }
        }
        return file;
    }


    /**
     * 得到文件的SHA码,用于校验
     *
     * @param inputStream
     * @return
     */
    public static String getSHA(InputStream inputStream) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA");
            byte[] buffer = new byte[8192];
            int length = -1;
            while ((length = inputStream.read(buffer)) != -1) {
                md.update(buffer, 0, length);
            }
            BigInteger bigInt = new BigInteger(1, md.digest());//1代表绝对值
            return bigInt.toString();
        } catch (IOException ex) {
            return null;
        } catch (NoSuchAlgorithmException ex) {
            return null;
        } finally {
            try {
                inputStream.close();
            } catch (IOException ex) {
            }
        }
    }


    /**
     *
     */
    public static InputStream getInputStream(String base64) {
        if(StringUtils.isEmpty(base64)){
            throw new ApiException("文件不存在", ApiException.API_PARAM_ERROR);
        }
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] bytes = decoder.decode(base64);
        try {
             return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            throw new ApiException("Base64文件解析异常:", ApiException.API_PARAM_ERROR);
        }
    }

    public static ByteArrayInputStream base64ToInputStream(String file) throws IOException {
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] bytes = decoder.decode(file.trim());
        return new ByteArrayInputStream(bytes);
    }
}
