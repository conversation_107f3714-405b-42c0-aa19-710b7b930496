package com.olading.operate.labor.util.excel;

import com.olading.operate.labor.util.exception.ExcelIOException;
import com.lanmaoly.util.lang.StringUtils;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/27 15:55
 */
public class ExcelWriter extends BaseExcel {
    public static int USE_SXSSF_LIMIT = 100000;

    private CellStyle errorCellStyle;

    private double rowHeight = 17.5;

    /**
     * 保存的Excel类型
     */
    private ExcelType excelType = ExcelType.HSSF;

    private ExcelResult result;

    /**
     * 写原始excel导入的数据，从excel导入的数据会存到一份原始值到snapshot中
     */
    private boolean writeOriginData = true;

    public ExcelWriterMode getWriterMode() {
        return writerMode;
    }

    public void setWriterMode(ExcelWriterMode writerMode) {
        this.writerMode = writerMode;
    }

    public ExcelType getExcelType() {
        return excelType;
    }

    public void setExcelType(ExcelType excelType) {
        this.excelType = excelType;
    }

    public ExcelResult getResult() {
        return result;
    }

    public void setResult(ExcelResult result) {
        this.result = result;
    }

    public void setRowHeight(double rowHeight) {
        this.rowHeight = rowHeight;
    }

    public boolean isWriteOriginData() {
        return writeOriginData;
    }

    public void setWriteOriginData(boolean writeOriginData) {
        this.writeOriginData = writeOriginData;
    }

    /**
     * 导出数据方式
     */
    private ExcelWriterMode writerMode = ExcelWriterMode.DEFAULT;

    public ExcelWriter() {
        sheetName = "Sheet1";
        sheetIndex = 0;
    }

    public ExcelWriter(ExcelResult result) {
        this();
        this.result = result;
    }

    public static ExcelWriterBuilder builder(ExcelResult excelResult) {
        return new ExcelWriterBuilder(excelResult);
    }

    public String toRealFileName(String fileName) {
        return excelType.toRealFileName(fileName);
    }

    public void download(String fileName, HttpServletResponse response) throws IOException{
        String attachment = toRealFileName(fileName);
        Workbook workbook = writeToWorkbook();
        Excels.download(workbook, attachment, response);
    }

    public InputStream writeToInputStream() throws IOException{
        Workbook workbook = writeToWorkbook();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        return new ByteArrayInputStream(os.toByteArray());
    }

    public Workbook writeToWorkbook() {
        return writeToWorkbook(null);
    }

    public Workbook writeToWorkbook(Workbook wBook) {
        if (null == result) {
            throw new ExcelIOException("数据不能为空");
        }

        List<? extends ExcelRow> records = result.getWriteList(writerMode);
        int size = headerRow + records.size();
        boolean resetCellStyle = null == wBook;
        Workbook workbook = null != wBook ? wBook : getWorkbook(size);
        this.createErrorCellStyle(workbook);
        Sheet sheet = null;

        try {
            if (null != wBook) {
                // 打开已有的Workbook
                sheet = workbook.getSheetAt(null != sheetIndex ? sheetIndex : 0);
            } else {
                // 新创建
                sheet = workbook.createSheet(sheetName);
            }
        } catch (Exception e) {
            sheet = workbook.createSheet();
        }

        ExcelHeader excelHeader = result.getExcelHeader();
        if (null == excelHeader || excelHeader.isEmpty()) {
            throw new ExcelIOException("Excel表头信息不能空。");
        }

        excelHeader.resortColumnIndex();

        Map<String, Integer> excelTitleIndexMap = new HashMap<>();
        Row headRow = null;
        int writeRow = headerRow >= 1 ? headerRow - 1 : 0;
        headRow = sheet.createRow(writeRow++);

        int maxColIndex = 0;
        // Write Header
        int maxLineCount = 1;
        for (ExcelColumnEntity column: excelHeader.getColumns()) {
            Integer colIndex = column.getColIndex();
            if (null == colIndex) {
                continue;
            }

            createHeaderCell(workbook, sheet, headRow, column, resetCellStyle);
            maxLineCount = Integer.max(maxLineCount, column.getExcelTitle().split("\n").length);
            if (colIndex > maxColIndex) {
                maxColIndex = colIndex;
            }

            int cellWidth = 0;
            if (column.getWidth() > 0) {
                cellWidth = calcCellWidth(column.getWidth());
            } else if (null != column.getExcelTitle()){
                cellWidth = column.getExcelTitle().length() < 3 ? 3 * 700 : column.getExcelTitle().length() * 700;
            }

            if (resetCellStyle && cellWidth > 0) {
                sheet.setColumnWidth(colIndex, cellWidth);
            }

            excelTitleIndexMap.put(column.getExcelTitle(), colIndex);
        }

        headRow.setHeight(calcRowHeight(maxLineCount));

        if (result.hasVerifyFailed()) {
            Cell errTitleCell = headRow.createCell(maxColIndex + 1);
            errTitleCell.setCellStyle(this.errorCellStyle);
            errTitleCell.setCellValue("失败原因");
        }

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // Write Data
        if (!records.isEmpty()) {
            for (ExcelRow excelRow : records) {
                Row row = sheet.createRow(writeRow++);
                Map<String, Object> snapshot = excelRow.getSnapshot();
                maxLineCount = 1;
                for (ExcelColumnEntity column: excelHeader.getColumns()) {
                    Integer colIndex = column.getColIndex();
                    if (null == colIndex) {
                        continue;
                    }

                    Field field = column.getField();
                    String value = null;
                    if (null != snapshot && snapshot.size() > 0 && writeOriginData) {
                        // 从快照读原始数据
                        Object snapshotValue = snapshot.get(column.getExcelTitle());
                        if (null != snapshotValue) {
                            // 读快照
                            value = String.valueOf(snapshotValue);
                        }
                    }else if (null != field) {
                        // 从变量读
                        value = column.getNullWriteValue();
                        try {
                            field.setAccessible(true);
                            Object v = field.get(excelRow);
                            Class<?> fieldType = field.getType();
                            if (null != v) {
                                if (column.isDateField()) {
                                    if (null != column.getDateFormat()) {
                                        if (LocalDateTime.class.equals(fieldType)) {
                                            value = ((LocalDateTime)v).format(DateTimeFormatter.ofPattern(column.getDateFormat(), Locale.SIMPLIFIED_CHINESE));
                                        } else if (LocalDate.class.equals(fieldType)) {
                                            value = ((LocalDate)v).format(DateTimeFormatter.ofPattern(column.getDateFormat(), Locale.SIMPLIFIED_CHINESE));
                                        } else {
                                            value = String.valueOf(v).replace('T', ' ');
                                        }
                                    } else {
                                        value = String.valueOf(v).replace('T', ' ');
                                    }
                                } else if (v instanceof List) {
                                    int length = ((List) v).size();
                                    int i = 1;
                                    for (Object o : ((List) v)) {
                                        value += String.valueOf(o);
                                        if (i++ < length) {
                                            value += Excels.ENTER_BLANK_CHAR;
                                        }
                                    }
                                } else {
                                    value = String.valueOf(v);
                                }
                            }
                        } catch (Exception e) {
                            //
                        }
                    }

                    if (StringUtils.isNotBlank(value)) {
                        maxLineCount = Integer.max(maxLineCount, value.split(Excels.ENTER_BLANK_CHAR).length);

                        Cell cell = row.createCell(colIndex);
                        if (resetCellStyle) {
                            cell.setCellStyle(cellStyle);
                        }

                        cell.setCellValue(value);
                    }
                }

                if (excelRow.isVerifyFail()) {
                    Cell errCell = row.createCell(maxColIndex + 1);
                    errCell.setCellStyle(this.errorCellStyle);
                    errCell.setCellValue(excelRow.getRowErrorString());
                    maxLineCount = Integer.max(maxLineCount, excelRow.getErrorCount());
                }

                row.setHeight(calcRowHeight(maxLineCount));
            }
        }

        return workbook;
    }

    private short calcRowHeight(int lineCount) {
        return (short)(20.0D * this.rowHeight * lineCount);
    }

    private int calcCellWidth(int width) {
        return (int)(256.0D * width);
    }

    private Cell createHeaderCell(Workbook workbook, Sheet sheet, Row row,
                                  ExcelColumnEntity column, boolean resetCellStyle) {
        Cell cell = row.createCell(column.getColIndex());
        String title = column.getExcelTitle();

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        //headerFont.setFontName("微软雅黑");
        headerFont.setFontHeightInPoints((short) 12);
        if (column.isRequired()) {
            // 必选项加*标红
            title = "*" + column.getExcelTitle();
            headerFont.setColor(IndexedColors.RED.getIndex());
        }

        cellStyle.setFont(headerFont);

        if (resetCellStyle) {
            // 是否设置样式，从Excel模板读取的Workbook无需设置样式，使用模板设置的样式
            cell.setCellStyle(cellStyle);
        }

        cell.setCellValue(title);

        // 备注
        if (!StringUtils.isBlank(column.getComment())) {
            Drawing drawing = sheet.createDrawingPatriarch();
            ClientAnchor anchor = null;
            RichTextString commentText = null;
            if (ExcelType.HSSF.equals(excelType)) {
                anchor = new HSSFClientAnchor();
                commentText = new HSSFRichTextString(column.getComment());
            } else {
                anchor = new XSSFClientAnchor();
                commentText = new XSSFRichTextString(column.getComment());
            }

            int rowspan = (int)Math.ceil(column.getComment().length() / 20) + 1;
            anchor.setCol1(cell.getColumnIndex());
            anchor.setRow1(cell.getRowIndex());
            anchor.setCol2(anchor.getCol1() + 4);
            anchor.setRow2(anchor.getRow1() + rowspan);

            Comment comment = drawing.createCellComment(anchor);
            comment.setString(commentText);
            cell.setCellComment(comment);
        }

        if (column.getOptions() != null && column.getOptions().length > 0) {
            //创建隐藏sheet，存储列表
            String sheetCode = "OPT_" + column.getExcelTitle();
            String strFormula = createOptionsFormulaSheet(workbook, column.getOptions(), sheetCode);
            sheet.addValidationData(createDataValidation(sheet, strFormula, headerRow, 10000,
                    cell.getColumnIndex(), cell.getColumnIndex()));
        } else if (StringUtils.isNoneBlank(column.getValidationFormula())) {
            sheet.addValidationData(createDataValidation(sheet, column.getValidationFormula(), headerRow, 10000,
                    cell.getColumnIndex(), cell.getColumnIndex()));
        }

        return cell;
    }

    /**
     * 创建隐藏Sheet用于渲染下拉框
     * @param wb
     * @param options
     * @param sheetCode
     * @return
     */
    private String createOptionsFormulaSheet(Workbook wb, String[] options, String sheetCode) {
        // 创建下拉列表值存储工作表
        Sheet sheet = wb.createSheet(sheetCode);
        // 循环往该sheet中设置添加下拉列表的值
        for (int i = 0; i < options.length; i++) {
            Row row = sheet.createRow(i);
            Cell cell = row.createCell(0);
            cell.setCellValue(options[i]);
        }
        wb.setSheetHidden(wb.getSheetIndex(sheetCode), true);
        return sheetCode + "!$A$1:$A$" + options.length;
    }

    private DataValidation createDataValidation(Sheet sheet, String strFormula,
                                                    int firstRow, int endRow, int firstCol, int endCol) {

        // 设置数据有效性加载在哪个单元格上。四个参数分别是：起始行、终止行、起始列、终止
        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) sheet);
        CellRangeAddressList regions = new CellRangeAddressList((short) firstRow, (short) endRow, (short) firstCol, (short) endCol);
        DataValidationConstraint constraint = dvHelper.createFormulaListConstraint(strFormula);
        DataValidation dataValidation = dvHelper.createValidation(constraint, regions);

        dataValidation.createErrorBox("Error", "Error");
        dataValidation.createPromptBox("", null);

        return dataValidation;
    }

    private void createErrorCellStyle(Workbook workbook) {
        this.errorCellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setColor((short)10);
        this.errorCellStyle.setFont(font);
        this.errorCellStyle.setWrapText(true);
    }

    private Workbook getWorkbook(int size) {
        if (ExcelType.HSSF.equals(excelType)) {
            return new HSSFWorkbook();
        } else {
            return (Workbook)(size < USE_SXSSF_LIMIT ? new XSSFWorkbook() : new SXSSFWorkbook());
        }
    }
}
