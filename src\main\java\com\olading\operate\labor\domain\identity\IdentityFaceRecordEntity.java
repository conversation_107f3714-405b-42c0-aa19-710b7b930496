package com.olading.operate.labor.domain.identity;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

/**
 * 人脸识别记录实体
 * 任务 2.3: 创建人脸识别记录实体类
 */
@Entity
@Table(name = "t_identity_face_record")
@Comment("人脸识别记录表")
@Data
public class IdentityFaceRecordEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    @Comment("认证记录编号")
    @Column(name = "record_no", length = 64, nullable = false)
    private String recordNo;

    @Comment("用户ID（可空，兼容非登录场景）")
    @Column(name = "user_id")
    private Long userId;

    @Comment("供应商ID")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("作业主体ID")
    @Column(name = "corporation_id")
    private Long corporationId;

    @Comment("真实姓名")
    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @Comment("身份证号")
    @Column(name = "id_card", length = 18, nullable = false)
    private String idCard;

    @Comment("人脸视频文件ID")
    @Column(name = "face_video_file_id", length = 32)
    private String faceVideoFileId;

    @Comment("相似度分数")
    @Column(name = "similarity_score", precision = 5, scale = 4)
    private BigDecimal similarityScore;

    @Comment("活体检测分数")
    @Column(name = "liveness_score", precision = 5, scale = 4)
    private BigDecimal livenessScore;

    @Comment("认证场景：CONTRACT_SIGNING,PAYMENT_DISBURSEMENT,REAL_NAME_AUTH")
    @Column(name = "auth_scene", length = 30, nullable = false)
    private String authScene;

    @Comment("人脸识别状态：SUCCESS,FAILED")
    @Column(name = "face_status", length = 20, nullable = false)
    private String faceStatus;

    @Comment("识别结果：PASSED,REJECTED")
    @Column(name = "face_result", length = 20)
    private String faceResult;

    @Comment("错误码")
    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Comment("错误信息")
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    public IdentityFaceRecordEntity(TenantInfo tenantInfo) {
        setTenant(tenantInfo);
    }

    public IdentityFaceRecordEntity() {
    }
}