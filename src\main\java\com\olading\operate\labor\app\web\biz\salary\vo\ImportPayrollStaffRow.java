package com.olading.operate.labor.app.web.biz.salary.vo;

import com.olading.operate.labor.util.excel.ExcelColumn;
import com.olading.operate.labor.util.excel.ExcelRow;
import com.olading.operate.labor.util.validation.constraints.Name;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

@Data
public class ImportPayrollStaffRow extends ExcelRow {

    @ExcelColumn(name = "姓名", required = true)
    @Name(required = true)
    private String name;

    @ExcelColumn(name = "身份证号", required = true)
    @Name(required = true, maxLength = 60)
    private String idCard;

    private String cellPhone;

    @ExcelColumn(name = "应发金额", required = true)
    @ColumnDefault("0.00")
    @Pattern(regexp = "^[0-9]+(\\.[0-9]{1,2})?$", message = "应发金额格式错误，应为正数且最多两位小数")
    private String amount;

    @ExcelColumn(name = "当期免税收入")
    @ColumnDefault("0.00")
    @Pattern(regexp = "^$|^[0-9]+(\\.[0-9]{1,2})?$", message = "当期免税收入格式错误，应为正数且最多两位小数")
    private String currentDeductionIncome;

    @ExcelColumn(name = "依法确定的其他扣除")
    @ColumnDefault("0.00")
    @Pattern(regexp = "^$|^[0-9]+(\\.[0-9]{1,2})?$", message = "其他扣除格式错误，应为正数且最多两位小数")
    private String otherDeduction;

    @ExcelColumn(name = "减免税额")
    @ColumnDefault("0.00")
    @Pattern(regexp = "^$|^[0-9]+(\\.[0-9]{1,2})?$", message = "减免税额格式错误，应为正数且最多两位小数")
    private String reductionTax;
}