package com.olading.operate.labor.domain.job;

import com.olading.boot.core.component.job.BusinessJobCustomizer;
import com.olading.boot.core.component.job.BusinessJobRegister;
import com.olading.operate.labor.AppProperties;
import com.olading.operate.labor.domain.proxy.ProxyOrderManager;
import com.olading.operate.labor.domain.service.ProtocolService;
import com.olading.operate.labor.domain.service.ProtocolTemplateService;
import com.olading.operate.labor.domain.service.ProxyOrderService;
import com.olading.operate.labor.domain.service.SalaryCalculateService;
import com.olading.operate.labor.domain.share.notification.NotificationManager;
import com.olading.operate.labor.domain.share.task.TaskManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SupplierBusinessJobCustomizer implements BusinessJobCustomizer {

    private final AppProperties properties;
    private final NotificationManager notificationManager;

    private final TaskManager taskManager;
    private final ProxyOrderService proxyOrderService;
    private final ProtocolService protocolService;
    private final ProtocolTemplateService protocolTemplateService;
    private final SalaryCalculateService salaryCalculateService;

    @Override
    public void customizer(BusinessJobRegister register) {
        register.registerJob("queryRemit", proxyOrderService::queryRemitResult);
        register.registerJob("queryBatchStatus", proxyOrderService::refreshBatchStatus);
        register.registerJob("downloadSigningFileAndSign", protocolService::downloadSigningFileAndSign);
        register.registerJob("querySignStepStatus", protocolService::querySignStepStatus);
        register.registerJob("createSigningFile", protocolService::createSigningFile);
        register.registerJob("queryTemplateStatus", protocolTemplateService::queryTemplateStatus);
        register.registerJob("salaryTaxCalculateTask", salaryCalculateService::salaryTaxCalculateTask);
        register.registerJob("queryRemitVoucher", proxyOrderService::queryRemitVoucher);
    }
}
