package com.olading.operate.labor.domain.share.signing.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "文件属性响应参数")
public class GetFileAttributeResponse {


    @Schema(description = "文件名称")
    private String name;
    @Schema(description = "文件大小")
    private Long fileSize;
    @Schema(description = "文件Hash值")
    private String fileHash;


}
