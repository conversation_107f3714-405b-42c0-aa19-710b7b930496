package com.olading.operate.labor.domain.share.otp;

import java.util.Map;

/**
 * 短信服务
 */
public interface NSmsProvider {
    /**
     * 发送短信
     *
     * @param cellphone 手机号
     * @param parameters   消息参数
     * @param businessId 短信业务编号
     * @param sign 签名
     */
    void sendSms(String cellphone, Map<String, String> parameters, String businessId, String sign);

    /**
     * 验证短信
     *
     * @param receiver 手机号
     * @param challenge   验证码
     * @param businessId 短信业务编号
     * @param sign 签名
     */
    void sendOtp(String receiver, String challenge, String businessId, String sign);
}
