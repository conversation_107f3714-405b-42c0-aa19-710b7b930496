package com.olading.operate.labor.domain.share.tax.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PersonalIncomeTaxDetailVo {

    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "灵工平台ID")
    private Long supplierId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "申报ID")
    private Long taxDeclareId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "本期收入")
    private BigDecimal currentIncome;

    @Schema(description = "累计收入")
    private BigDecimal accumulatedIncome;

    @Schema(description = "累计费用")
    private BigDecimal accumulatedExpenses;

    @Schema(description = "累计免税收入")
    private BigDecimal accumulatedTaxFreeIncome;

    @Schema(description = "累计依法确定的其他扣除")
    private BigDecimal accumulatedOtherDeductions;

    @Schema(description = "累计已预缴税额")
    private BigDecimal accumulatedPrepaidTax;

    @Schema(description = "本期应预扣预缴税额")
    private BigDecimal currentWithholdingTax;

    @Schema(description = "税款所属期")
    private String taxPeriod;

    @Schema(description = "申报月份")
    private String declareMonth;

    @Schema(description = "手机号")
    private String cellphone;

    @Schema(description = "实际金额")
    private BigDecimal actualAmount;

    @Schema(description = "累计减除费用")
    private BigDecimal accumulatedTaxDeductionExpenses;

    @Schema(description = "累计减免税额")
    private BigDecimal accumulatedTaxReductions;

    @Schema(description = "累计应纳税额")
    private BigDecimal accumulatedTaxableAmount;
}
