package com.olading.operate.labor.domain.share.submission;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("企业信息报送表")
@Entity
@Table(name = "t_info_submission_enterprise", schema = "olading_labor")
public class InfoSubmissionEnterpriseEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;


    @Size(max = 20)
    @Comment("报送状态")
    @Column(name = "report_status", length = 20)
    private String reportStatus;

    @Size(max = 200)
    @NotNull
    @Comment("名称（姓名）")
    @Column(name = "name", nullable = false, length = 200)
    private String name;

    @Size(max = 64)
    @Comment("统一社会信用代码（纳税人识别号）")
    @Column(name = "social_credit_code", length = 64)
    private String socialCreditCode;

    @Size(max = 200)
    @Comment("平台内的平台名称")
    @Column(name = "platform_name", length = 200)
    private String platformName;

    @Size(max = 200)
    @Comment("平台内的平台唯一标识码")
    @Column(name = "platform_unique_code", length = 200)
    private String platformUniqueCode;

    @Size(max = 20)
    @Comment("经营开始时间")
    @Column(name = "start_date", length = 20)
    private String startDate;

    @Size(max = 20)
    @Comment("经营结束时间")
    @Column(name = "end_date", length = 20)
    private String endDate;

    @Size(max = 20)
    @Comment("信息状态标识")
    @Column(name = "info_status_flag", length = 20)
    private String infoStatusFlag;

    public InfoSubmissionEnterpriseEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }

    public InfoSubmissionEnterpriseEntity() {
    }

}