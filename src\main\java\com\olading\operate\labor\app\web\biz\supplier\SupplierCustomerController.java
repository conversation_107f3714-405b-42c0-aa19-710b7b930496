package com.olading.operate.labor.app.web.biz.supplier;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.Json;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.menu.Menu;
import com.olading.operate.labor.app.menu.MenuManager;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.RoleMemberQuery;
import com.olading.operate.labor.domain.query.RoleQuery;
import com.olading.operate.labor.domain.query.SupplierMemberQuery;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.util.Utils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Tag(name = "客户设置接口")
@RestController
@RequestMapping("/api/customer")
@RequiredArgsConstructor
@Slf4j
public class SupplierCustomerController  extends BusinessController {

    private final SupplierService supplierService;
    private final AuthorityManager authorityManager;
    private final MenuManager menuManager;
    private final QueryService queryService;

    @Operation(summary = "获取管理后台登录用户的信息")
    @PostMapping(value = "profile")
    @AuthorityGuard(any = {Authority.SYS_CUSTOMER})
    public WebApiResponse<MemberController.SupplierProfileVo> profile() {
        MemberController.SupplierProfileVo profile = new MemberController.SupplierProfileVo();
        profile.setCurrentSupplierId(currentSupplierId());
        final UserEntity userEntity = currentUser();
        if (profile.getCurrentSupplierId() != null) {
            var member = supplierService.requireCustomerMemberByUserId(currentSupplierId(), currentCustomerId(), userEntity.getId());
            profile.setUserPersonName(member.getName());
            profile.setAdmin(isCustomerAdmin());
            profile.setAuthorities(new ArrayList<>(currentAuthorities()));
            profile.setCreateTime(member.getCreateTime());
        }
        profile.setUserId(userEntity.getId());
        profile.setIsPwd(userEntity.getPassword() != null);
        profile.setCellphone(userEntity.getCellphone());

        return WebApiResponse.success(profile);
    }


    @Operation(summary = "权限树")
    @PostMapping(value = "getAuthorityTree")
    public WebApiResponse<SupplierController.AuthorityTree> getAuthorityTree() {
        var tree = Json.fromJson(Utils.loadResource("/authority/customer.json"), SupplierController.AuthorityTree.class);
        return WebApiResponse.success(tree);
    }


    @Operation(summary = "菜单")
    @PostMapping(value = "getMenu")
    public WebApiResponse<Menu> menu() {
        Menu m = menuManager.load(currentAuthorities(), "customer");
        return WebApiResponse.success(m);
    }


    @Operation(summary = "查询角色")
    @PostMapping(value = "listRole")
    public WebApiQueryResponse<MemberController.RoleVo> listRole(@Valid @RequestBody QueryFilter<MemberController.ListRoleFilters> request) {

        QueryFilter<RoleQuery.Filters> filter = request.convert(MemberController.ListRoleFilters::convert);
        filter.getFilters().setTenant(currentTenant());
        filter.sort("id", Direction.DESCENDING);
        DataSet<RoleData> ds = queryService.queryRole(filter);

        var f2 = new RoleMemberQuery.Filters();
        f2.setSupplierId(currentSupplierId());
        var grouped = queryService.queryRoleMember(new QueryFilter<>(f2)).getData()
                .stream().collect(Collectors.groupingBy(o -> o.getRoleMember().getRoleId()));

        return WebApiQueryResponse.success(ds.getData().stream()
                .map(o -> {
                    MemberController.RoleVo vo = new MemberController.RoleVo();
                    vo.setId(o.getId());
                    vo.setCode(o.getCode());
                    vo.setName(o.getName());
                    vo.setRemark(o.getRemark());
                    vo.setCreateTime(o.getCreateTime());
                    vo.setModifyTime(o.getModifyTime());
                    vo.setDisabled(o.isDisabled());
                    var members = grouped.get(vo.getId());
                    if (members != null) {
                        vo.setMembers(members.stream().map(m -> {
                            MemberController.MemberVo u = new MemberController.MemberVo();
                            u.setMemberId(m.getSupplierMember().getId());
                            u.setName(m.getSupplierMember().getName());
                            u.setCellphone(m.getSupplierMember().getCellphone());
                            u.setCreateTime(m.getSupplierMember().getCreateTime());
                            u.setModifyTime(m.getSupplierMember().getModifyTime());
                            return u;
                        }).collect(Collectors.toList()));
                    }

                    return vo;
                })
                .collect(Collectors.toList()), ds.getTotal());
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "添加角色")
    @PostMapping(value = "addRole")
    public WebApiResponse<Void> addRole(@Valid @RequestBody MemberController.AddRoleRequest request) {
        supplierService.addRole(currentSupplierId(), request.getName(),request.getCode(), request.getRemark(), request.getAuthorities());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "禁用/开启角色")
    @PostMapping(value = "disableRole")
    public WebApiResponse<Void> disableRole(@Valid @RequestBody MemberController.DisableRequest request) {
        supplierService.disableRole(currentSupplierId(),request.getId(), request.isDisabled());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "编辑角色")
    @PostMapping(value = "editRole")
    public WebApiResponse<Void> editRole(@Valid @RequestBody MemberController.EditRoleRequest request) {
        RoleData data = new RoleData();
        data.setId(request.getRoleId());
        data.setName(request.getName());
        data.setRemark(request.getRemark());
        supplierService.editRole(currentTenant(), currentSupplierId(), data, request.getAuthorities());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "删除角色")
    @PostMapping(value = "deleteRole")
    public WebApiResponse<Void> deleteRole(@Valid @RequestBody MemberController.DeleteRoleRequest request) {
        supplierService.deleteRole(currentTenant(),currentSupplierId(), request.getRoleId());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "角色详情")
    @PostMapping(value = "roleDetail")
    public WebApiResponse<MemberController.RoleDetailResponse> roleDetail(@Valid @RequestBody MemberController.RoleDetailRequest request) {
        RoleEntity role = supplierService.requireRole(currentSupplierId(), request.getRoleId());
        final Map<OwnerType, Map<Long, String>> roleDataScopes = authorityManager.getRoleDataScopes(currentTenant(), request.getRoleId());
        var data = new MemberController.RoleDetailResponse();
        data.setId(role.getId());
        data.setName(role.getName());
        data.setCode(role.getCode());
        data.setRemark(role.getRemark());
        data.setAuthorities(role.getAuthorities());
        data.setDataScopes(roleDataScopes);
        return WebApiResponse.success(data);
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "设置角色成员")
    @PostMapping(value = "setRoleMembers")
    public WebApiResponse<Void> setRoleMembers(@Valid @RequestBody MemberController.SetRoleMembersRequest request) {
        supplierService.setRoleMembers(currentSupplierId(), request.getRoleId(), request.getMemberId());
        return WebApiResponse.success();
    }

    @Operation(summary = "获取角色成员")
    @PostMapping(value = "getRoleMembers")
    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    public WebApiResponse<List<SupplierController.UserVo>> getRoleMembers(@Valid @RequestBody MemberController.GetRoleMembersRequest request) {

        var filters = new RoleMemberQuery.Filters();
        filters.setSupplierId(currentSupplierId());
        filters.setRoleId(request.getRoleId());
        var ds = queryService.queryRoleMember(new QueryFilter<>(filters));

        return WebApiResponse.success(ds.getData().stream().map(o -> {
            SupplierController.UserVo vo = new SupplierController.UserVo();
            vo.setId(o.getSupplierMember().getId());
            vo.setRealName(o.getSupplierMember().getName());
            return vo;
        }).collect(Collectors.toList()));
    }

    @Operation(summary = "获取成员")
    @PostMapping(value = "getMembers")
    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    public WebApiQueryResponse<MemberController.MemberVo> getMembers(@Valid @RequestBody QueryFilter<MemberController.GetMembers> request) {
        QueryFilter<SupplierMemberQuery.Filters> filter = request.convert(MemberController.GetMembers::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        var ds = queryService.queryMember(filter);
        ds.getData().forEach(o -> {
                    o.setRoles(Lists.newArrayList());
                    final RoleMemberQuery.Filters roleMemberFilters = new RoleMemberQuery.Filters();
                    roleMemberFilters.setSubjectId(Set.of(o.getId()));
                    final DataSet<RoleMemberQuery.Record> dataSet = queryService.queryRoleMember(new QueryFilter<>(roleMemberFilters));
                    dataSet.getData().forEach(roleMember -> {
                        o.getRoles().add(roleMember.getRole());
                    });
                }
        );
        final List<MemberController.MemberVo> collect = ds.getData().stream().map(o -> {
            MemberController.MemberVo vo = new MemberController.MemberVo();
            vo.setMemberId(o.getId());
            vo.setName(o.getName());
            vo.setCellphone(o.getCellphone());
            vo.setCreateTime(o.getCreateTime());
            vo.setModifyTime(o.getModifyTime());
            vo.setDisabled(o.isDisabled());
            if (CollectionUtil.isNotEmpty(o.getRoles())) {
                vo.setRoles(o.getRoles().stream().map(MemberController.RoleVo::transform).collect(Collectors.toSet()));
            }else{
                vo.setRoles(Set.of());
            }
            return vo;
        }).collect(Collectors.toList());
        return WebApiQueryResponse.success(collect,ds.getTotal());
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "禁用/开启成员")
    @PostMapping(value = "disableMember")
    public WebApiResponse<Void> disableMember(@Valid @RequestBody MemberController.DisableRequest request) {
        supplierService.disableMember(currentSupplierId(),request.getId(), request.isDisabled());
        return WebApiResponse.success();
    }


    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "删除成员")
    @PostMapping(value = "removeMember")
    public WebApiResponse<Void> removeMember(@Valid @RequestBody MemberController.DisableRequest request) {
        supplierService.removeMember(currentSupplierId(),request.getId(), request.isDisabled());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "添加成员")
    @PostMapping(value = "addMember")
    public WebApiResponse<Void> addMember(@Valid @RequestBody MemberController.AddMemberRequest request) {
        supplierService.addMember(currentSupplierId(),request.getName(),request.getCellphone(),request.getRoleIds());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.CUSTOMER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "编辑成员")
    @PostMapping(value = "editMember")
    public WebApiResponse<Void> editMember(@Valid @RequestBody MemberController.EditMemberRequest request) {
        supplierService.editSupplierMember(currentSupplierId(),request.getMerberId(),request.getCellphone(),request.getName(),request.getRoleIds());
        return WebApiResponse.success();
    }
}
