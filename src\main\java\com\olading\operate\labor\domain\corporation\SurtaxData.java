package com.olading.operate.labor.domain.corporation;

import lombok.Builder;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

@Data
@Builder
public class SurtaxData {
    @Comment("附加税名称")
    private String name;
    @Comment("附加税编码")
    private  SurtaxCodeEnum surtaxCode;
    @Comment("税率")
    private BigDecimal rate;
    @Comment("优惠比例")
    private BigDecimal discount_rate;
}