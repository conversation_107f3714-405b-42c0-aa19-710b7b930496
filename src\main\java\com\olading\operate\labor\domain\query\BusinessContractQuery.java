package com.olading.operate.labor.domain.query;

import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;


public class BusinessContractQuery implements EntityQuery<QueryFilter<BusinessContractQuery.Filters>, ContractVo> {

    private final QBusinessContractEntity t = QBusinessContractEntity.businessContractEntity;

    private final QCustomerEntity t2 = QCustomerEntity.customerEntity;

    private final QSupplierCorporationEntity t3 = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder builder = new BooleanBuilder();

        Filters f = filters.getFilters();

        if (f.getId() != null) {
            builder.and(t.id.eq(f.getId()));
        }else if (filters.getFilters().getContractIds() != null){
            builder.and(t.id.in(filters.getFilters().getContractIds()));
        }

        if (StringUtils.isNotBlank(f.getName())) {
            builder.and(t.name.like(JpaUtils.fullLike(f.getName())));
        }
        if (StringUtils.isNotBlank(f.getCustomerName())) {
            builder.and(t2.name.like(JpaUtils.fullLike(f.getCustomerName())));
        }
        if (f.getSupplierCorporationId() != null) {
            builder.and(t3.id.eq(f.getSupplierCorporationId()));
        }
        if (f.getCustomerId() != null) {
            builder.and(t2.id.eq(f.getCustomerId()));
        }
        if (StringUtils.isNotBlank(f.getSupplierName())) {
            builder.and(t3.name.like(JpaUtils.fullLike(f.getSupplierName())));
        }
        if (StringUtils.isNotBlank(f.getStatus())) {
            builder.and(t.status.eq(f.getStatus()));
        }
        if (f.getCreateTimeStart() != null) {
            builder.and(t.createTime.goe(f.getCreateTimeStart().atStartOfDay()));
        }
        if (f.getCreateTimeEnd() != null) {
            builder.and(t.createTime.lt(f.getCreateTimeEnd().plusDays(1).atStartOfDay()));
        }

        query.select(t,t2,t3)
                .from(t)
                .leftJoin(t2).on(t.customerId.eq(t2.id))
                .leftJoin(t3).on(t.supplierCorporationId.eq(t3.id))
                .where(builder);
    }

    @Override
    public ContractVo transform(Object v) {
        Tuple tuple = (Tuple) v;
        BusinessContractEntity entity = tuple.get(t);
        CustomerEntity customerEntity = tuple.get(t2);
        SupplierCorporationEntity supplierCorporationEntity = tuple.get(t3);
        ContractVo vo = new ContractVo();
        BeanUtils.copyProperties(entity, vo);
        if(customerEntity != null){
            vo.setCustomerName(customerEntity.getName());
        }
        if(supplierCorporationEntity != null){
            vo.setSupplierName(supplierCorporationEntity.getName());
        }
        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t.id;
        }
        return null;
    }

    @Data
    public static class Filters {
        private String name;
        private Long id;
        private String supplierName;
        private String customerName;
        private LocalDate createTimeStart;
        private LocalDate createTimeEnd;
        private String status;
        private Long supplierCorporationId;
        private Long customerId;
        private Set<Long> contractIds;
    }
}
