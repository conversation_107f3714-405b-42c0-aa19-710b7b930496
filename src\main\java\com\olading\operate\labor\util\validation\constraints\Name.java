package com.olading.operate.labor.util.validation.constraints;

import com.olading.operate.labor.util.excel.ExcelConstraint;
import com.olading.operate.labor.util.textfilter.TrimNameFilter;
import com.olading.operate.labor.util.textfilter.TrimQuoteFilter;
import com.olading.operate.labor.util.validation.validator.NameValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数字校验(如数据id、qq号等）
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        NameValidator.class
})
@ExcelConstraint(
        width = 12,
        filters = {
                TrimNameFilter.class,
                TrimQuoteFilter.class,
                TrimNameFilter.class
        }
)
public @interface Name {
    boolean required() default false;

    String label() default "";

    /**
     * 正则匹配规则
     * @return
     */
    String regex() default "";

    /**
     * 最大长度
     * @return
     */
    int maxLength() default NameValidator.MAX_LENGTH;

    /**
     * 最小长度
     * @return
     */
    int minLength() default NameValidator.MIN_LENGTH;

    String message() default "";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
