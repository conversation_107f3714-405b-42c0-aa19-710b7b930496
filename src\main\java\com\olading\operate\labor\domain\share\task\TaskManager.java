package com.olading.operate.labor.domain.share.task;

import com.olading.boot.util.ConcurrentUtils;
import com.olading.operate.labor.domain.share.info.OwnerType;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.stream.Collectors;

@Transactional
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskManager {

    private static final int MAX_PARALLELISM = 10;

    private final TaskRepository taskRepository;
    private final AsynTaskExecutor asynTaskExecutor;
    private final PlatformTransactionManager transactionManager;

    @PostConstruct
    public void post() {
    }


    public TaskEntity submit(long owerId, OwnerType ownerType, TaskType taskType, String taskParam, String fileName) {
        return taskRepository.submit(owerId, ownerType, taskType, taskParam, fileName);
    }

    public void delete(long taskId) {
        taskRepository.delete(taskId);
    }

    /**
     * 调度任务
     */
    public void dispatch() {

        log.info("开始执行异步任务");

        var tasks = taskRepository.findExecutableTasks();

        var grouped = tasks.stream().collect(Collectors.groupingBy(TaskEntity::getOwnedBy));

        var list = grouped.values().stream().map(o -> o.stream().findFirst().orElseThrow()).collect(Collectors.toList());

        ConcurrentUtils.concurrentExecute(list, MAX_PARALLELISM, task -> {
            runTask(task.getId());
        });
    }


    private void runTask(long taskId) {
        var task = taskRepository.markAsRunning(taskId);

        new TransactionTemplate(transactionManager).execute(s -> {
            try {
                String result = asynTaskExecutor.executeTask(task);
                taskRepository.markAsSuccess(taskId, result);
            } catch (Exception e) {
                log.error("Failed to execute task:{}", e);
                taskRepository.markAsFailed(task.getId(), e.getMessage());
            }
            return null;
        });

    }

}
