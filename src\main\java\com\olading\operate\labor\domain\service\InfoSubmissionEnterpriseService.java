package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.enums.BusinessContractStatusEnum;
import com.olading.operate.labor.app.web.biz.enums.InfoSubmissionReportStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.info.QEnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionEnterpriseEntity;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionEnterpriseManager;
import com.olading.operate.labor.domain.share.submission.QInfoSubmissionEnterpriseEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionEnterpriseVo;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class InfoSubmissionEnterpriseService {

    private final InfoSubmissionEnterpriseManager infoSubmissionEnterpriseManager;
    private final EntityManager entityManager;


    /**
     * 更新企业信息报送记录
     */
    public InfoSubmissionEnterpriseEntity updateInfoSubmissionEnterprise(TenantInfo tenantInfo, InfoSubmissionEnterpriseVo vo) {
        InfoSubmissionEnterpriseEntity entity = infoSubmissionEnterpriseManager.updateInfoSubmissionEnterprise(vo);
        return entity;
    }

    /**
     * 查询企业信息报送记录详情
     */
    public InfoSubmissionEnterpriseVo queryInfoSubmissionEnterprise(Long id) {
        InfoSubmissionEnterpriseVo vo = infoSubmissionEnterpriseManager.queryInfoSubmissionEnterprise(id);
        return vo;
    }

    /**
     * 删除企业信息报送记录
     */
    public void deleteInfoSubmissionEnterprise(TenantInfo tenantInfo, Long id) {
        // 删除记录
        infoSubmissionEnterpriseManager.deleteInfoSubmissionEnterprise(id);
    }

    /**
     * 根据作业主体ID查询企业信息报送记录列表（按ID倒序）
     */
    public List<InfoSubmissionEnterpriseEntity> queryInfoSubmissionEnterpriseBySupplierCorporationId(Long supplierCorporationId) {
        QInfoSubmissionEnterpriseEntity entity = QInfoSubmissionEnterpriseEntity.infoSubmissionEnterpriseEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.supplierCorporationId.eq(supplierCorporationId))
                .orderBy(entity.id.desc())
                .fetch();
    }

    /**
     * 根据客户ID和作业主体ID插入企业信息报送记录
     */
    @Transactional
    public InfoSubmissionEnterpriseEntity insertInfoSubmissionEnterprise(Long customerId, Long supplierCorporationId) {
        // 根据customerId查询客户信息
        CustomerEntity customer = findCustomerById(customerId);
        if (customer == null) {
            throw new BusinessException("未找到对应的客户记录");
        }

        // 根据customer查询企业信息
        EnterpriseInfoEntity enterpriseInfo = findEnterpriseInfoByCustomer(customer);
        if (enterpriseInfo == null) {
            throw new BusinessException("未找到对应的企业信息记录");
        }

        // 根据企业信息的社会信用代码和owner_type=CUSTOMER查询所有相关客户
        List<CustomerEntity> relatedCustomers = findCustomersBySocialCreditCode(enterpriseInfo.getSocialCreditCode());
        if (relatedCustomers.isEmpty()) {
            throw new BusinessException("未找到相关的客户记录");
        }

        // 验证传入的客户和作业主体是否有有效合同
        BusinessContractEntity customerContract = findContractByCustomerIdAndCorporationId(customerId, supplierCorporationId);
        if (customerContract == null) {
            throw new BusinessException("未找到客户在指定作业主体下的合同记录");
        }
        Long supplierId = customerContract.getSupplierId();

        // 查询这些客户在当前作业主体下的有效合同（status=INIT）
        List<BusinessContractEntity> validContracts = findValidContractsByCustomersAndCorporation(relatedCustomers, supplierCorporationId);
        if (validContracts.isEmpty()) {
            throw new BusinessException("未找到有效的合同记录");
        }

        // 检查是否已存在记录
        if (existsInfoSubmissionEnterprise(supplierCorporationId, enterpriseInfo.getSocialCreditCode())) {
            // 如果已存在，检查是否需要更新日期
            updateExistingRecordIfNeeded(supplierCorporationId, enterpriseInfo.getSocialCreditCode(), validContracts);
            log.info("企业信息报送记录已存在，supplierCorporationId: {}, socialCreditCode: {}",
                    supplierCorporationId, enterpriseInfo.getSocialCreditCode());
            return null;
        }

        // 计算开始和结束日期
        LocalDate minStartDate = validContracts.stream()
                .map(BusinessContractEntity::getStartDate)
                .filter(date -> date != null)
                .min(LocalDate::compareTo)
                .orElse(null);

        LocalDate maxEndDate = validContracts.stream()
                .map(BusinessContractEntity::getEndDate)
                .filter(date -> date != null)
                .max(LocalDate::compareTo)
                .orElse(null);

        // 创建企业信息报送记录
        InfoSubmissionEnterpriseEntity entity = new InfoSubmissionEnterpriseEntity();
        entity.setSupplierId(supplierId);
        entity.setSupplierCorporationId(supplierCorporationId);
        entity.setReportStatus(InfoSubmissionReportStatusEnum.PENDING_REPORT.name());
        entity.setName(customer.getShortName());
        entity.setSocialCreditCode(enterpriseInfo.getSocialCreditCode());
        entity.setPlatformUniqueCode(generatePlatformUniqueCode(supplierCorporationId, enterpriseInfo.getSocialCreditCode()));
        entity.setStartDate(minStartDate != null ? minStartDate.toString() : null);
        entity.setEndDate(maxEndDate != null ? maxEndDate.toString() : null);
        return entityManager.merge(entity);
    }

    /**
     * 根据ID查询客户信息
     */
    private CustomerEntity findCustomerById(Long customerId) {
        QCustomerEntity entity = QCustomerEntity.customerEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.id.eq(customerId))
                .fetchFirst();
    }

    /**
     * 根据客户查询企业信息
     */
    private EnterpriseInfoEntity findEnterpriseInfoByCustomer(CustomerEntity customer) {
        QEnterpriseInfoEntity entity = QEnterpriseInfoEntity.enterpriseInfoEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.id.eq(customer.getEnterpriseInfoId())
                        .and(entity.ownedBy.ownerType.eq(OwnerType.CUSTOMER)))
                .fetchFirst();
    }

    /**
     * 根据社会信用代码查询相关客户
     */
    private List<CustomerEntity> findCustomersBySocialCreditCode(String socialCreditCode) {
        QCustomerEntity customer = QCustomerEntity.customerEntity;
        QEnterpriseInfoEntity enterpriseInfo = QEnterpriseInfoEntity.enterpriseInfoEntity;

        return new JPAQueryFactory(entityManager)
                .select(customer)
                .from(customer)
                .join(enterpriseInfo).on(customer.enterpriseInfoId.eq(enterpriseInfo.id))
                .where(enterpriseInfo.socialCreditCode.eq(socialCreditCode)
                        .and(enterpriseInfo.ownedBy.ownerType.eq(OwnerType.CUSTOMER)))
                .fetch();
    }

    /**
     * 根据客户ID和作业主体ID查询合同
     */
    private BusinessContractEntity findContractByCustomerIdAndCorporationId(Long customerId, Long supplierCorporationId) {
        QBusinessContractEntity entity = QBusinessContractEntity.businessContractEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.customerId.eq(customerId)
                        .and(entity.supplierCorporationId.eq(supplierCorporationId))
                        .and(entity.status.eq(BusinessContractStatusEnum.INIT.name())))
                .fetchFirst();
    }

    /**
     * 查询客户在指定作业主体下的有效合同
     */
    private List<BusinessContractEntity> findValidContractsByCustomersAndCorporation(List<CustomerEntity> customers, Long supplierCorporationId) {
        if (customers.isEmpty()) {
            return List.of();
        }

        List<Long> customerIds = customers.stream()
                .map(CustomerEntity::getId)
                .toList();

        QBusinessContractEntity entity = QBusinessContractEntity.businessContractEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.customerId.in(customerIds)
                        .and(entity.supplierCorporationId.eq(supplierCorporationId))
                        .and(entity.status.eq(BusinessContractStatusEnum.INIT.name())))
                .fetch();
    }

    /**
     * 检查是否已存在企业信息报送记录
     */
    private boolean existsInfoSubmissionEnterprise(Long supplierCorporationId, String socialCreditCode) {
        QInfoSubmissionEnterpriseEntity entity = QInfoSubmissionEnterpriseEntity.infoSubmissionEnterpriseEntity;
        Long count = new JPAQueryFactory(entityManager)
                .select(entity.count())
                .from(entity)
                .where(entity.supplierCorporationId.eq(supplierCorporationId)
                        .and(entity.socialCreditCode.eq(socialCreditCode)))
                .fetchOne();
        return count != null && count > 0;
    }

    /**
     * 更新已存在的记录（如果需要）
     */
    private void updateExistingRecordIfNeeded(Long supplierCorporationId, String socialCreditCode, List<BusinessContractEntity> validContracts) {
        // 计算新的开始和结束日期
        LocalDate minStartDate = validContracts.stream()
                .map(BusinessContractEntity::getStartDate)
                .filter(date -> date != null)
                .min(LocalDate::compareTo)
                .orElse(null);

        LocalDate maxEndDate = validContracts.stream()
                .map(BusinessContractEntity::getEndDate)
                .filter(date -> date != null)
                .max(LocalDate::compareTo)
                .orElse(null);

        // 查询现有记录
        QInfoSubmissionEnterpriseEntity entity = QInfoSubmissionEnterpriseEntity.infoSubmissionEnterpriseEntity;
        InfoSubmissionEnterpriseEntity existingRecord = new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.supplierCorporationId.eq(supplierCorporationId)
                        .and(entity.socialCreditCode.eq(socialCreditCode)))
                .fetchFirst();

        if (existingRecord != null) {
            boolean needUpdate = false;

            // 比较开始日期
            if (minStartDate != null) {
                LocalDate existingStartDate = existingRecord.getStartDate() != null ?
                        LocalDate.parse(existingRecord.getStartDate()) : null;
                if (existingStartDate == null || minStartDate.isBefore(existingStartDate)) {
                    existingRecord.setStartDate(minStartDate.toString());
                    needUpdate = true;
                }
            }

            // 比较结束日期
            if (maxEndDate != null) {
                LocalDate existingEndDate = existingRecord.getEndDate() != null ?
                        LocalDate.parse(existingRecord.getEndDate()) : null;
                if (existingEndDate == null || maxEndDate.isAfter(existingEndDate)) {
                    existingRecord.setEndDate(maxEndDate.toString());
                    needUpdate = true;
                }
            }

            if (needUpdate) {
                entityManager.merge(existingRecord);
                log.info("更新企业信息报送记录日期，supplierCorporationId: {}, socialCreditCode: {}",
                        supplierCorporationId, socialCreditCode);
            }
        }
    }

    /**
     * 生成平台唯一标识码：supplierCorporationId_哈希后的socialCreditCode
     */
    private String generatePlatformUniqueCode(Long supplierCorporationId, String socialCreditCode) {
        String hashedSocialCreditCode = DigestUtils.md5Hex(socialCreditCode);
        return supplierCorporationId + "_" + hashedSocialCreditCode;
    }

    /**
     * 校验企业信息报送记录VO
     */
    private void validateInfoSubmissionEnterpriseVo(InfoSubmissionEnterpriseVo vo) {
        if (vo.getSupplierCorporationId() == null) {
            throw new BusinessException("作业主体ID不能为空");
        }
        if (vo.getSupplierId() == null) {
            throw new BusinessException("灵工平台ID不能为空");
        }
    }
}
