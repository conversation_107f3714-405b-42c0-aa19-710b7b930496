package com.olading.operate.labor.domain.bill;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单薪酬明细表（第三层）- 薪酬费用明细
 */
@Entity
@Table(name = "t_bill_salary_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("账单薪酬明细表")
public class BillSalaryDetailEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Comment("账单主表ID")
    @Column(name = "bill_master_id", nullable = false)
    private Long billMasterId;

    @NotNull
    @Comment("账单分类ID")
    @Column(name = "bill_category_id", nullable = false)
    private Long billCategoryId;

    @NotNull
    @Comment("薪酬明细ID（关联薪酬系统）")
    @Column(name = "salary_detail_id", nullable = false)
    private Long salaryDetailId;

    @NotNull
    @Comment("薪酬批次ID")
    @Column(name = "salary_batch_id", nullable = false)
    private Long salaryBatchId;

    @NotNull
    @Comment("人员姓名")
    @Column(name = "labor_name", nullable = false, length = 50)
    private String laborName;

    @NotNull
    @Comment("身份证号")
    @Column(name = "id_card", nullable = false, length = 18)
    private String idCard;

    @NotNull
    @Comment("应发工资")
    @Column(name = "gross_salary", nullable = false, precision = 19, scale = 2)
    private BigDecimal grossSalary = BigDecimal.ZERO;

    @NotNull
    @Comment("实发工资")
    @Column(name = "net_salary", nullable = false, precision = 19, scale = 2)
    private BigDecimal netSalary = BigDecimal.ZERO;

    @NotNull
    @Comment("应缴个税")
    @Column(name = "income_tax", nullable = false, precision = 19, scale = 2)
    private BigDecimal incomeTax = BigDecimal.ZERO;

    @NotNull
    @Comment("应缴增值税")
    @Column(name = "vat_tax", nullable = false, precision = 19, scale = 2)
    private BigDecimal vatTax = BigDecimal.ZERO;

    @NotNull
    @Comment("应缴附加税")
    @Column(name = "additional_tax", nullable = false, precision = 19, scale = 2)
    private BigDecimal additionalTax = BigDecimal.ZERO;

    @NotNull
    @Comment("账单月份")
    @Column(name = "bill_month", nullable = false)
    private LocalDate billMonth;

    @Comment("工资所属期")
    @Column(name = "salary_period", length = 20)
    private String salaryPeriod;

    @Comment("备注")
    @Column(name = "remark", length = 200)
    private String remark;

    public BillSalaryDetailEntity(TenantInfo info) {
        setTenant( info);
    }

    protected BillSalaryDetailEntity() {
    }


}