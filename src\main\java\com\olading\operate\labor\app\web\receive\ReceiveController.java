package com.olading.operate.labor.app.web.receive;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 接收消息通知的入口
 */
@RestController
@RequestMapping("/api/receive")
@RequiredArgsConstructor
@Slf4j
public class ReceiveController {

    /**
     * 通知
     */
    @PostMapping(value = "notify")
    public void alipayNotify(HttpServletRequest request) throws IOException {

        var body = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        log.info("支付宝通知原文: {}", body);

    }
}
