package com.olading.operate.labor.domain.provider;

import com.olading.operate.labor.util.SpringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NotificationProviderManager {

    private final NotificationProvider notificationProvider;

    public NotificationProviderManager(@Autowired(required = false) NotificationProvider notificationProvider) {
        this.notificationProvider = notificationProvider == null ? new EmptyNotificationExtension() : notificationProvider;
    }

    public void send(Notification notification) {

        // 如果开启了事务，在事务提交后真正发送消息
        SpringUtils.afterCommit(() -> sendCore(notification));
    }

    private void sendCore(Notification notification) {
        notificationProvider.send(notification);
    }

    static class EmptyNotificationExtension implements NotificationProvider {

        @Override
        public void send(Notification notification) {
            log.info("未配置NotificationProvider，跳过通知。 通知内容: {}", notification);
        }
    }

    @Data
    public static
    class Notification {

        /**
         * 通知地址
         */
        private String url;

        /**
         * 开发者账号
         */
        private String appKey;

        /**
         * 通知名称
         */
        private String name;

        /**
         * 推送的业务数据
         */
        private Object data;
    }
}
