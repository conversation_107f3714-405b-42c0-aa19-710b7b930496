package com.olading.operate.labor.app.web.biz.enums;

public enum InfoSubmissionReportStatusEnum {

    PENDING_REPORT("待报送");

    private final String name;

    InfoSubmissionReportStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameByKey(String key) {
        for (InfoSubmissionReportStatusEnum e : values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getName();
            }
        }
        return "";
    }
}
