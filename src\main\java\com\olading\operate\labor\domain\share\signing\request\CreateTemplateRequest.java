package com.olading.operate.labor.domain.share.signing.request;


import com.olading.operate.labor.domain.share.signing.common.Field;
import com.olading.operate.labor.domain.share.signing.common.Step;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "创建模板请求参数")
public class CreateTemplateRequest  extends BaseRequest<CreateTemplateRequest> {

    @Schema(description = "模板名称", required = true)
    private String name;
    @Schema(description = "模板备注", required = true)
    private String remark;
    @Schema(description = "模板文件大小", required = true)
    private Long fileSize;
    @Schema(description = "模板文件sha hash值", required = true)
    private String fileHash;
    @Schema(description = "模板域列表", required = true)
    private List<Field> fields;
    @Schema(description = "签名步骤列表", required = true)
    private List<Step> steps;
    @Schema(description = "模板签署类型")
    private String signWay;
    @Schema(description = "原始模板的编号")
    private Long id;
    @Schema(description = "模板来源")
    private String source = "EMPLOYEE";
}
