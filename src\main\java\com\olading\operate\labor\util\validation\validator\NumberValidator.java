package com.olading.operate.labor.util.validation.validator;


import com.lanmaoly.util.lang.ValidateUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Number;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class NumberValidator extends BaseValidator<Number> {
    private double max = Double.MAX_VALUE;

    private double min = 0;

    private int decimals = -1;

    public NumberValidator() {}

    public NumberValidator(String name) {
        setName(name);
    }

    public void setMin(double min) {
        this.min = min;
    }

    public void setMax(double max) {
        this.max = max;
    }

    public void setDecimals(int decimals) {
        this.decimals = decimals;
    }

    @Override
    public void initialize(Number constraintAnnotation) {
        max = constraintAnnotation.max();
        min = constraintAnnotation.min();
        decimals = constraintAnnotation.decimals();
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        super.initialize(constraintAnnotation);
    }

    @Override
    protected boolean constraintCheck(Object o) {
        String fieldName = getName();
        String s = String.valueOf(o);
        if (!ValidateUtils.isNumber(s)) {
            throw new ValidationException(fieldName, "输入必须为数字格式");
        }

        double number = Double.parseDouble(s);
        if (number < min) {
            throw new ValidationException(fieldName, String.format("不能小于%s", min));
        } else if (number > max) {
            throw new ValidationException(fieldName, String.format("不能大于%s", max));
        }

        int decimalLength = 0;
        if (s.indexOf(".") >= 0) {
            decimalLength = s.length() - s.indexOf(".") - 1;
        }

        if (decimals == 0 && decimalLength > 0) {
            throw new ValidationException(fieldName, "输入必须为整数");
        } else if (decimals > 0 && decimals < decimalLength) {
            throw new ValidationException(fieldName, String.format("小数不能超过%s位", decimals));
        }

        return true;
    }
}
