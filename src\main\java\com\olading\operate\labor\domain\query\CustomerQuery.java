package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.olading.operate.labor.domain.share.info.QEnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.QUserEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

public class CustomerQuery implements EntityQuery<QueryFilter<CustomerQuery.Filters>, CustomerWithInfo> {

    private final QCustomerEntity t1 = QCustomerEntity.customerEntity;
    private final QEnterpriseInfoEntity t2 = QEnterpriseInfoEntity.enterpriseInfoEntity;
    private final QUserEntity t3 = QUserEntity.userEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.eq(filters.getFilters().getId()));
        }else if (filters.getFilters().getCustomerIds() != null){
            criteria.and(t1.id.in(filters.getFilters().getCustomerIds()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getName())) {
            criteria.and(t1.name.like(JpaUtils.fullLike(filters.getFilters().getName())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getSocialCreditCode())) {
            criteria.and(t2.socialCreditCode.like(JpaUtils.fullLike(filters.getFilters().getSocialCreditCode())));
        }
        if (filters.getFilters().getCreateTimeStart() != null) {
            criteria.and(t1.createTime.goe(filters.getFilters().getCreateTimeStart().atStartOfDay()));
        }
        if (filters.getFilters().getCreateTimeEnd() != null) {
            criteria.and(t1.createTime.lt(filters.getFilters().getCreateTimeEnd().plusDays(1).atStartOfDay()));
        }

        query.select(t1, t2, t3)
                .from(t1)
                .leftJoin(t2).on(t1.enterpriseInfoId.eq(t2.id))
                .leftJoin(t3).on(t1.adminUserId.eq(t3.id))
                .where(criteria);
    }

    @Override
    public CustomerWithInfo transform(Object v) {
        Tuple tuple = (Tuple) v;
        CustomerEntity customer = tuple.get(t1);
        EnterpriseInfoEntity enterprise = tuple.get(t2);
        UserEntity user = tuple.get(t3);

        CustomerWithInfo result = new CustomerWithInfo(customer, enterprise);
        if (user != null) {
            result.setAdminName(user.getName());
            result.setAdminMobile(user.getCellphone());
        }
        return result;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    public static class Filters {
        private Long id;
        private String name;
        private String socialCreditCode;
        private LocalDate createTimeStart;
        private LocalDate createTimeEnd;
        private Set<Long> customerIds;
    }
}
