package com.olading.operate.labor.domain.share.file;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 存储文件
 */
@Table(name = "t_file", indexes = {
        @Index(name = "i_file_1", columnList = "owner_id, owner_type")
})
@Entity
public class FileEntity extends BaseEntity {

    @Id
    @Column(name = "id", length = 32)
    private String id;

    @Column(name = "storage_id", length = 100)
    private String storageId;

    @Comment("文件名")
    @Column(name = "name", length = 200)
    private String name;

    @Comment("文件过期时间")
    @Column(name = "expiry_time")
    private LocalDateTime expiryTime;

    @Embedded
    private OwnedByFragment ownedBy;

    public FileEntity(String id, String name, String storageId, OwnerType ownerType, String ownerId) {
        this.id = id;
        this.storageId = storageId;
        this.name = name;
        this.ownedBy = new OwnedByFragment(ownerType, ownerId);
    }

    protected FileEntity() {
    }

    public String getId() {
        return id;
    }

    public String getStorageId() {
        return storageId;
    }

    public String getName() {
        return name;
    }

    public LocalDateTime getExpiryTime() {
        return expiryTime;
    }

    void setExpiryTime(LocalDateTime expiryTime) {
        this.expiryTime = expiryTime;
    }

    public OwnedByFragment getOwnedBy() {
        return ownedBy;
    }
}
