package com.olading.operate.labor.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class NullIfEmptyInSetDeserializer extends JsonDeserializer<Set<Long>> implements ContextualDeserializer {

    private JavaType contentType;

    public NullIfEmptyInSetDeserializer() {}

    public NullIfEmptyInSetDeserializer(JavaType contentType) {
        this.contentType = contentType;
    }


    @Override
    public Set<Long> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        if (node == null || !node.isArray() || node.isEmpty()) {
            return null;
        }

        HashSet<Long> result = new HashSet<>();
        for (JsonNode element : node) {
            if (element.isNull()) {
                result.add(null);
            } else {
                Long text = element.asLong();
                result.add(text);
            }
        }

        return result;
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) throws JsonMappingException {
        if (property == null) {
            return this;
        }

        JavaType type = property.getType();
        // 防止错误用于 List<Object> 或 List<InvoiceItemRequest>
        if (!type.hasGenericTypes() || !type.getContentType().getRawClass().equals(Long.class)) {
            throw new JsonMappingException(ctxt.getParser(), "NullIfEmptyInListDeserializer only supports List<String>");
        }
        return this;
    }
}