package com.olading.operate.labor.domain.share.customer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CustomerData {

    private Long id;

    private String name;

    private LocalDateTime createTime;

    private String clientKey;

    private String securityKey;

    private String remark;

    private boolean disabled;

    @Schema(description = "企业信息")
    private EnterpriseInfoData info = new EnterpriseInfoData();

    public CustomerData() {
    }

    public CustomerData(CustomerEntity entity) {
       /* this.id = entity.getId();
        this.name = entity.getName();
        this.createTime = entity.getCreateTime();
        this.clientKey = entity.getClientKey();
        this.securityKey = entity.getSecurityKey();
        this.remark = entity.getRemark();
        this.disabled = entity.isDisabled();*/
    }
}
