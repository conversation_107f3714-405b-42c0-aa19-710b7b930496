package com.olading.operate.labor.domain.bill.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 其他费用导入结果
 */
@Data
public class BillOtherFeeImportResult {
    
    /**
     * 导入是否成功
     */
    private boolean success;
    
    /**
     * 导入条数
     */
    private int importCount;
    
    /**
     * 导入批次号
     */
    private String importBatchNo;
    
    /**
     * 导入总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 涉及人数
     */
    private int personCount;
    
    /**
     * 消息
     */
    private String message;

    private String uuid;
    
    public static BillOtherFeeImportResult success(int importCount, String importBatchNo, 
                                                  BigDecimal totalAmount, int personCount) {
        BillOtherFeeImportResult result = new BillOtherFeeImportResult();
        result.setSuccess(true);
        result.setImportCount(importCount);
        result.setImportBatchNo(importBatchNo);
        result.setTotalAmount(totalAmount);
        result.setPersonCount(personCount);
        result.setMessage("导入成功");
        return result;
    }
    
    public static BillOtherFeeImportResult failure(String message,String uuid) {
        BillOtherFeeImportResult result = new BillOtherFeeImportResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setUuid(uuid);
        return result;
    }
}