package com.olading.operate.labor.util;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.lanmaoly.util.lang.TimeUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Base64;
import java.util.Date;

public class JacksonBuilder {
    static final ZoneOffset DEFAULT_ZONE_OFFSET = OffsetDateTime.now().getOffset();
    public static final DateTimeFormatter DATE_PATTERN = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public JacksonBuilder() {
    }

    public static ObjectMapper build() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModules(new Module[]{new RpcModule()});
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        return mapper;
    }


    public static String json(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return build().writeValueAsString(object);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T deserialize(String content, Class<?> c) {
        try {
            return build().readValue(content, build().getTypeFactory().constructType(c));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws IOException {
        ObjectMapper mapper = build();
        LocalDateTime now = LocalDateTime.now();
        Date date = TimeUtils.asDate(now);
        LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), TimeUtils.DEFAULT_ZONE_OFFSET);
        System.out.println(now);
        System.out.println(date);
        System.out.println(time);
    }

    static class ByteArrayDeserializer extends JsonDeserializer<byte[]> {
        ByteArrayDeserializer() {
        }

        public byte[] deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
            String s = p.getValueAsString();
            return Base64.getDecoder().decode(s);
        }
    }

    static class ByteArraySerializer extends JsonSerializer<byte[]> {
        ByteArraySerializer() {
        }

        public void serialize(byte[] value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeString(Base64.getEncoder().encodeToString(value));
        }
    }

    static class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        LocalDateTimeDeserializer() {
        }

        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
            try {
                long time = Long.valueOf(p.getValueAsString());
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(time), JacksonBuilder.DEFAULT_ZONE_OFFSET);
            } catch (NumberFormatException var6) {
                try {
                    return LocalDateTime.parse(p.getValueAsString(), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                } catch (DateTimeParseException var5) {
                    return LocalDateTime.parse(p.getValueAsString(), JacksonBuilder.DATE_PATTERN);
                }
            }
        }
    }

    static class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
        LocalDateTimeSerializer() {
        }

        public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeString(value.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
    }

    static class DateDeserializer extends JsonDeserializer<Date> {
        DateDeserializer() {
        }

        public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
            LocalDateTime localDateTime = null;

            try {
                long time = Long.valueOf(p.getValueAsString());
                localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), JacksonBuilder.DEFAULT_ZONE_OFFSET);
            } catch (NumberFormatException var7) {
                try {
                    localDateTime = LocalDateTime.parse(p.getValueAsString(), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                } catch (DateTimeParseException var6) {
                    localDateTime = LocalDateTime.parse(p.getValueAsString(), JacksonBuilder.DATE_PATTERN);
                }
            }

            return TimeUtils.asDate(localDateTime);
        }
    }

    static class DateSerializer extends JsonSerializer<Date> {
        DateSerializer() {
        }

        public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            LocalDateTime time = TimeUtils.asLocalDateTime(value);
            gen.writeString(time.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
    }

    static class RpcModule extends SimpleModule {
        public RpcModule() {
            this.addSerializer(Date.class, new DateSerializer());
            this.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());
            this.addSerializer(byte[].class, new ByteArraySerializer());
            this.addDeserializer(Date.class, new DateDeserializer());
            this.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
            this.addDeserializer(byte[].class, new ByteArrayDeserializer());
        }
    }
}
