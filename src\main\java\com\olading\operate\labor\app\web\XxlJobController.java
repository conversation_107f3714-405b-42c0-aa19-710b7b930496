package com.olading.operate.labor.app.web;

import com.olading.boot.core.component.job.BusinessJobManager;
import com.olading.operate.labor.app.web.biz.BusinessController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/task")
@RequiredArgsConstructor
@Slf4j
public class XxlJobController extends BusinessController {

    private final BusinessJobManager businessJobManager;

    @PostMapping("{name}")
    public void run(@PathVariable("name") String name) {
        businessJobManager.runJob(name);
    }

}
