package com.olading.operate.labor.util.validation.validator;

import com.olading.operate.labor.util.validation.constraints.Required;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class RequiredValidator extends BaseValidator<Required> {
    public RequiredValidator() {
        setRequired(true);
    }

    public RequiredValidator(String name) {
        setRequired(true);
        setName(name);
    }

    @Override
    public void initialize(Required constraintAnnotation) {
        setRequired(constraintAnnotation.required());
        super.initialize(constraintAnnotation);
    }

    /**
     * 一般用于传参，严格校验
     * @param o
     * @return
     */
    @Override
    protected boolean constraintCheck(Object o) {
        // BaseValidator中有做校验
        return true;
    }
}
