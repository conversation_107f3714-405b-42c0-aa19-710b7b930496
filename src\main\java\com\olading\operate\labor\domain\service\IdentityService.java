package com.olading.operate.labor.domain.service;

import com.olading.operate.labor.domain.identity.AuthContext;
import com.olading.operate.labor.domain.identity.AuthRecordManager;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.identity.IdentifyProvider;
import com.olading.operate.labor.domain.share.identity.PersonVo;
import com.olading.operate.labor.domain.share.identity.dto.FaceAuthResult;
import com.olading.operate.labor.domain.share.identity.dto.OcrIdentifyResult;
import com.olading.operate.labor.domain.share.info.OwnerType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.util.Base64;

/**
 * 身份识别服务
 * 任务 4: IdentityService增强
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IdentityService {

    public static final int MAX_SIZE = 3 * 1024 * 1024;
    public static final float MIN_QUALITY = 0.1f;
    private final IdentifyProvider identifyProvider;
    private final AuthRecordManager authRecordManager;
    private final FileManager fileManager;

    /**
     * OCR身份证识别（只传个人证件照片面）
     *
     * @param personalIdImg 个人证件照片面图片base64
     * @return OCR识别结果
     */
    private  OcrIdentifyResult ocrIdentify(String personalIdImg,AuthContext context) {
        return ocrIdentifyWithContext(personalIdImg, null,context);
    }

    /**
     * 身份证OCR识别并验证
     *
     * @param personalIdImg 个人证件照片面图片base64
     * @param expectedName 期望的姓名（用于验证）
     * @param expectedIdNo 期望的身份证号（用于验证）
     * @return OCR识别结果，包含验证信息
     */
    public   OcrIdentifyResult ocrIdentifyAndVerify(String personalIdImg, String expectedName, String expectedIdNo, AuthContext context) {
        log.info("开始OCR身份证识别并验证: expectedName={}, expectedIdNo={}", expectedName, expectedIdNo);

        OcrIdentifyResult result = ocrIdentify(personalIdImg,context);

        if (!result.isSuccess()) {
            return result;
        }

        // 验证姓名和身份证号是否匹配
        boolean nameMatch = expectedName != null && expectedName.equals(result.getName());
        boolean idNoMatch = expectedIdNo != null && expectedIdNo.equals(result.getIdNo());

        if (!nameMatch || !idNoMatch) {
            String errorMsg = String.format("身份信息不匹配: 姓名匹配=%s, 身份证号匹配=%s", nameMatch, idNoMatch);
            log.warn("OCR识别验证失败: {}", errorMsg);
            return OcrIdentifyResult.failure(errorMsg, result.getFlowNo());
        }

        log.info("OCR身份证识别验证成功: flowNo={}", result.getFlowNo());
        return result;
    }

    // ========== 增强方法：带认证记录功能 ==========

    /**
     * OCR身份证识别（增强版 - 自动记录）
     * 任务 4.1: 增强OCR识别方法
     *
     * @param personalIdImg 个人证件照片面图片base64（必填）
     * @param nationalEmblemImg 国徽面图片base64（可选）
     * @param context 认证上下文
     * @return OCR识别结果
     */
    public OcrIdentifyResult ocrIdentifyWithContext(String personalIdImg, String nationalEmblemImg, AuthContext context) {
        log.info("开始OCR身份证识别（带记录）, authScene: {}", context.getAuthScene());

        if (personalIdImg == null || personalIdImg.trim().isEmpty()) {
            OcrIdentifyResult failResult = OcrIdentifyResult.failure("个人证件照片面图片不能为空", null);
            // 记录失败结果
            authRecordManager.saveOcrRecord(context, failResult);
            return failResult;
        }

        try {
            // 从fileManager中获取
            final byte[] idImageBytes = fileManager.load(personalIdImg);
            byte[] idImageProcessedBytes = idImageBytes;

            // 判断个人证件照片是否超过5M，超过则进行压缩
            if (idImageBytes.length > MAX_SIZE) {
                idImageProcessedBytes = compressImageIfNeeded(idImageBytes);
            }

            // 将处理后的字节数据转换为Base64
            final String idImgBase64 = Base64.getEncoder().encodeToString(idImageProcessedBytes);

            String nationalEmblemImgBase64 = null;
            if (nationalEmblemImg != null) {
                final byte[] emblemImageBytes = fileManager.load(nationalEmblemImg);
                byte[] emblemImageProcessedBytes = emblemImageBytes;

                // 判断国徽面图片是否超过5M，超过则进行压缩
                if (emblemImageBytes.length > MAX_SIZE) {
                    emblemImageProcessedBytes = compressImageIfNeeded(emblemImageBytes);
                }

                // 将处理后的字节数据转换为Base64
                nationalEmblemImgBase64 = Base64.getEncoder().encodeToString(emblemImageProcessedBytes);
            }
            // 调用原有逻辑
            OcrIdentifyResult result = identifyProvider.ocrIdentifyWithResult(nationalEmblemImgBase64, idImgBase64);

            // 记录结果（成功或失败都记录）
            Long recordId = authRecordManager.saveOcrRecord(context, result);

            if (result.isSuccess()) {
                log.info("OCR身份证识别成功: recordId={}, name={}, idNo={}",
                        recordId, result.getName(), result.getIdNo());
            } else {
                log.warn("OCR身份证识别失败: recordId={}, error={}",
                        recordId, result.getErrorMessage());
            }

            return result;
        } catch (Exception e) {
            log.error("OCR身份证识别异常", e);
            OcrIdentifyResult failResult = OcrIdentifyResult.failure("OCR识别服务异常", null);
            // 记录异常结果
            authRecordManager.saveOcrRecord(context, failResult);
            return failResult;
        }
    }

    /**
     * 活体人脸识别（增强版 - 自动记录）
     * 任务 4.2: 增强人脸识别方法
     *
     * @param name 姓名
     * @param idNo 身份证号
     * @param videoBase64 视频base64
     * @param context 认证上下文
     * @return 人脸识别结果
     */
    public FaceAuthResult liveFaceAuthWithContext(String name, String idNo, String videoBase64, AuthContext context) {
        log.info("开始活体人脸识别（带记录）: name={}, idNo={}, authScene={}", name, idNo, context.getAuthScene());



        // 参数验证
        if (name == null || name.trim().isEmpty()) {
            FaceAuthResult failResult = FaceAuthResult.failure("姓名不能为空", null);
            authRecordManager.saveFaceRecord(context, name, idNo, failResult);
            return failResult;
        }

        if (idNo == null || idNo.trim().isEmpty()) {
            FaceAuthResult failResult = FaceAuthResult.failure("身份证号不能为空", null);
            authRecordManager.saveFaceRecord(context, name, idNo, failResult);
            return failResult;
        }

        if (videoBase64 == null || videoBase64.trim().isEmpty()) {
            FaceAuthResult failResult = FaceAuthResult.failure("视频数据不能为空", null);
            authRecordManager.saveFaceRecord(context, name, idNo, failResult);
            return failResult;
        }

        // 从fileManager中获取
        final byte[] videoBytes = fileManager.load(videoBase64);
        byte[] processedVideoBytes = videoBytes;

        // 判断视频是否超过5M，超过则进行压缩
        if (videoBytes.length > MAX_SIZE) {
            processedVideoBytes = compressVideoIfNeeded(videoBytes);
            try {
                final String fileId = fileManager.save("compressed_video.mp4", new ByteArrayInputStream(processedVideoBytes), LocalDateTime.now().plusYears(100),
                        OwnerType.SUPPLIER, String.valueOf(1));
                context.setFaceVideoFileId(fileId);
                log.info("保存压缩后的视频成功，fileId: {}", fileId);
            } catch (Exception e) {
                log.info("保存压缩后的视频文件异常");
            }
        }

        final String videoToBase64 = Base64.getEncoder().encodeToString(processedVideoBytes);

        try {
            // 调用原有逻辑
            FaceAuthResult result = identifyProvider.liveFaceAuthWithResult(name, idNo, videoToBase64);

            // 记录结果
            Long recordId = authRecordManager.saveFaceRecord(context, name, idNo, result);

            if (result.isSuccess()) {
                log.info("活体人脸识别成功: recordId={}, similarity={}, liveRate={}, passed={}",
                        recordId, result.getSimilarity(), result.getLiveRate(), result.isPassed());
            } else {
                log.warn("活体人脸识别失败: recordId={}, error={}",
                        recordId, result.getErrorMessage());
            }

            return result;
        } catch (Exception e) {
            log.error("活体人脸识别异常", e);
            FaceAuthResult failResult = FaceAuthResult.failure("活体人脸识别服务异常", null);
            // 记录异常结果
            authRecordManager.saveFaceRecord(context, name, idNo, failResult);
            return failResult;
        }
    }

    /**
     * 二要素鉴权（增强版 - 自动记录）
     * 任务 4.3: 增强要素鉴权方法
     *
     * @param personVo 人员信息
     * @param context 认证上下文
     * @return 错误信息，null表示成功
     */
    public String authLimit2WithContext(PersonVo personVo, AuthContext context) {
        log.info("开始二要素鉴权（带记录）: name={}, idNo={}, authScene={}",
                personVo.getRealName(), personVo.getIdCardNo(), context.getAuthScene());

        try {
            // 调用原有逻辑
            String errorMsg = identifyProvider.authLimit2(personVo);

            // 记录结果
            Long recordId = authRecordManager.saveFactorRecord(context, "TWO_FACTOR",
                    personVo.getRealName(), personVo.getIdCardNo(), null, null, errorMsg);

            if (errorMsg == null) {
                log.info("二要素鉴权成功: recordId={}", recordId);
            } else {
                log.warn("二要素鉴权失败: recordId={}, error={}", recordId, errorMsg);
            }

            return errorMsg;
        } catch (Exception e) {
            log.error("二要素鉴权异常", e);
            String errorMsg = "鉴权服务异常: " + e.getMessage();
            // 记录异常结果
            authRecordManager.saveFactorRecord(context, "TWO_FACTOR",
                    personVo.getRealName(), personVo.getIdCardNo(), null, null, errorMsg);
            return errorMsg;
        }
    }

    /**
     * 三要素鉴权（增强版 - 自动记录）
     * 任务 4.3: 增强要素鉴权方法
     *
     * @param personVo 人员信息
     * @param context 认证上下文
     * @return 错误信息，null表示成功
     */
    public String authLimit3WithContext(PersonVo personVo, AuthContext context) {
        log.info("开始三要素鉴权（带记录）: name={}, idNo={}, bankCard={}, authScene={}",
                personVo.getRealName(), personVo.getIdCardNo(), personVo.getBankCardNo(), context.getAuthScene());

        try {
            // 调用原有逻辑
            String errorMsg = identifyProvider.authLimit3(personVo);

            // 记录结果
            Long recordId = authRecordManager.saveFactorRecord(context, "THREE_FACTOR",
                    personVo.getRealName(), personVo.getIdCardNo(), personVo.getBankCardNo(), null, errorMsg);

            if (errorMsg == null) {
                log.info("三要素鉴权成功: recordId={}", recordId);
            } else {
                log.warn("三要素鉴权失败: recordId={}, error={}", recordId, errorMsg);
            }

            return errorMsg;
        } catch (Exception e) {
            log.error("三要素鉴权异常", e);
            String errorMsg = "鉴权服务异常: " + e.getMessage();
            // 记录异常结果
            authRecordManager.saveFactorRecord(context, "THREE_FACTOR",
                    personVo.getRealName(), personVo.getIdCardNo(), personVo.getBankCardNo(), null, errorMsg);
            return errorMsg;
        }
    }

    /**
     * 运营商三要素鉴权（增强版 - 自动记录）
     * 任务 4.3: 增强要素鉴权方法
     *
     * @param personVo 人员信息
     * @param context 认证上下文
     * @return 错误信息，null表示成功
     */
    public String operatorLimit3WithContext(PersonVo personVo, AuthContext context) {
        log.info("开始运营商三要素鉴权（带记录）: name={}, idNo={}, phone={}, authScene={}",
                personVo.getRealName(), personVo.getIdCardNo(), personVo.getCellphoneNo(), context.getAuthScene());

        try {
            // 调用原有逻辑
            String errorMsg = identifyProvider.operatorLimit3(personVo);

            // 记录结果
            Long recordId = authRecordManager.saveFactorRecord(context, "OPERATOR_THREE_FACTOR",
                    personVo.getRealName(), personVo.getIdCardNo(), null, personVo.getCellphoneNo(), errorMsg);

            if (errorMsg == null) {
                log.info("运营商三要素鉴权成功: recordId={}", recordId);
            } else {
                log.warn("运营商三要素鉴权失败: recordId={}, error={}", recordId, errorMsg);
            }

            return errorMsg;
        } catch (Exception e) {
            log.error("运营商三要素鉴权异常", e);
            String errorMsg = "鉴权服务异常: " + e.getMessage();
            // 记录异常结果
            authRecordManager.saveFactorRecord(context, "OPERATOR_THREE_FACTOR",
                    personVo.getRealName(), personVo.getIdCardNo(), null, personVo.getCellphoneNo(), errorMsg);
            return errorMsg;
        }
    }

    /**
     * 图片压缩方法
     *
     * @param imageBytes 原始图片字节数组
     * @return 压缩后的字节数组
     */
    public static byte[] compressImageIfNeeded(byte[] imageBytes) {
        try {
            log.info("开始压缩图片, 大小={}M", imageBytes.length / 1024 / 1024);
            // 创建临时文件用于压缩处理
            File tempInputFile = File.createTempFile("ocr_input_", ".jpg");
            File tempOutputFile = File.createTempFile("ocr_output_", ".jpg");

            try {
                // 将字节数组写入临时输入文件
                try (FileOutputStream fos = new FileOutputStream(tempInputFile)) {
                    fos.write(imageBytes);
                }

                // 初始压缩质量因子
                float quality = 0.7f;

                // 执行压缩
                cn.hutool.core.img.ImgUtil.compress(tempInputFile, tempOutputFile, quality);

                // 检查压缩后文件大小，如果仍然超过5M，则调整质量因子继续压缩
                long fileSize = tempOutputFile.length();
                while (fileSize > MAX_SIZE && quality > MIN_QUALITY) {
                    // 降低质量因子
                    quality -= MIN_QUALITY;

                    // 重新压缩
                    cn.hutool.core.img.ImgUtil.compress(tempInputFile, tempOutputFile, quality);

                    // 更新文件大小
                    fileSize = tempOutputFile.length();
                }

                // 读取压缩后的文件并返回字节数组
                log.info("压缩图片成功, 大小={}M", fileSize / 1024 / 1024);
                return java.nio.file.Files.readAllBytes(tempOutputFile.toPath());

            } finally {
                // 清理临时文件
                try {
                    if (tempInputFile.exists()) {
                        tempInputFile.delete();
                    }
                    if (tempOutputFile.exists()) {
                        tempOutputFile.delete();
                    }
                } catch (Exception e) {
                    log.warn("删除临时文件时发生异常", e);
                }
            }
        } catch (Exception e) {
            log.warn("图片压缩失败，使用原始图片", e);
            // 压缩失败时返回原始图片
            return imageBytes;
        }
    }

    /**
     * 视频压缩方法
     * 使用FFmpeg进行视频压缩
     *
     * @param videoBytes 原始视频字节数组
     * @return 压缩后的字节数组
     */
    private byte[] compressVideoIfNeeded(byte[] videoBytes) {
        try {
            log.info("开始压缩视频,大小={}M", videoBytes.length / 1024 / 1024);
            // 创建临时文件用于压缩处理
            File tempInputFile = File.createTempFile("face_input_", ".mp4");
            File tempOutputFile = File.createTempFile("face_output_", ".mp4");

            try {
                // 将字节数组写入临时输入文件
                try (FileOutputStream fos = new FileOutputStream(tempInputFile)) {
                    fos.write(videoBytes);
                }

                // 使用FFmpeg进行视频压缩
                // 压缩目标：将视频压缩到接近但不超过maxSize
                ProcessBuilder processBuilder = new ProcessBuilder();

                // 计算目标比特率 (以kbps为单位)
                // 假设视频时长为5秒，目标大小为4.5MB (留出一些余量)
                // kbps
                double targetFileSizeKb = (MAX_SIZE * 8) / 1024 / 5 * 0.9;

                // 构建FFmpeg命令
                processBuilder.command(
                        "ffmpeg",
                        // 输入文件
                        "-i", tempInputFile.getAbsolutePath(),
                        // 视频比特率
                        "-b:v", targetFileSizeKb + "k",
                        // 缓冲区大小
                        "-bufsize", "64k",
                        // 最大比特率
                        "-maxrate", targetFileSizeKb + "k",
                        // 覆盖输出文件
                        "-y",
                        // 输出文件
                        tempOutputFile.getAbsolutePath()
                );

                // 执行压缩
                Process process = processBuilder.start();
                int exitCode = process.waitFor();

                // 检查压缩是否成功
                if (exitCode != 0) {
                    log.warn("FFmpeg视频压缩失败，使用原始视频");
                    return videoBytes;
                }

                // 如果压缩后的文件仍然超过限制，进一步压缩
                long outputFileSize = tempOutputFile.length();
                while (outputFileSize > MAX_SIZE && targetFileSizeKb > 100) {
                    // 减少比特率
                    targetFileSizeKb = (long) (targetFileSizeKb * 0.8);

                    // 重新压缩
                    processBuilder.command(
                            "ffmpeg",
                            "-i", tempInputFile.getAbsolutePath(),
                            "-b:v", targetFileSizeKb + "k",
                            "-bufsize", "64k",
                            "-maxrate", targetFileSizeKb + "k",
                            "-y",
                            tempOutputFile.getAbsolutePath()
                    );

                    process = processBuilder.start();
                    exitCode = process.waitFor();

                    if (exitCode != 0) {
                        log.warn("FFmpeg视频压缩失败，使用原始视频");
                        return videoBytes;
                    }

                    outputFileSize = tempOutputFile.length();
                }

                // 如果最终文件还是太大，使用更激进的压缩
                if (outputFileSize > MAX_SIZE) {
                    processBuilder.command(
                            "ffmpeg",
                            "-i", tempInputFile.getAbsolutePath(),
                            "-vcodec", "libx264",
                            // 恒定质量因子，值越大质量越低
                            "-crf", "28",
                            "-preset", "fast",
                            "-y",
                            tempOutputFile.getAbsolutePath()
                    );

                    process = processBuilder.start();
                    exitCode = process.waitFor();

                    if (exitCode != 0) {
                        log.warn("FFmpeg视频压缩失败，使用原始视频");
                        return videoBytes;
                    }
                }

                // 读取压缩后的文件并返回字节数组
                log.info("压缩视频成功, 大小={}M", outputFileSize / 1024 / 1024);
                return java.nio.file.Files.readAllBytes(tempOutputFile.toPath());

            } finally {
                // 清理临时文件
                try {
                    if (tempInputFile.exists()) {
                        tempInputFile.delete();
                    }
                    if (tempOutputFile.exists()) {
                        tempOutputFile.delete();
                    }
                } catch (Exception e) {
                    log.warn("删除临时文件时发生异常", e);
                }
            }
        } catch (Exception e) {
            log.warn("视频压缩失败，使用原始视频", e);
            // 压缩失败时返回原始视频
            return videoBytes;
        }
    }
}