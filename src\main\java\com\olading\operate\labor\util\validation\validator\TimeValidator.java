package com.olading.operate.labor.util.validation.validator;


import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Time;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class TimeValidator extends BaseValidator<Time> {
    private String format = "HH:mm:ss";

    public TimeValidator() {}

    public TimeValidator(String name) {
        setName(name);
    }

    public void setFormat(String format) {
        this.format = format;
    }

    @Override
    public void initialize(Time constraintAnnotation) {
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        format = constraintAnnotation.format();
        super.initialize(constraintAnnotation);
    }

    /**
     * 一般用于传参，严格校验
     * @param o
     * @return
     */
    @Override
    protected boolean constraintCheck(Object o) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        try {
            LocalTime.parse(String.valueOf(o), df);
        } catch (Exception e) {
            throw new ValidationException(getName(), String.format("的格式错误，标准格式为：%s", format));
        }

        return true;
    }
}
