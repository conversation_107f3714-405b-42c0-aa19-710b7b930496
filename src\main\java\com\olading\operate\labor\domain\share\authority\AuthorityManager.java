package com.olading.operate.labor.domain.share.authority;

import cn.hutool.core.collection.CollectionUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Transactional
public class AuthorityManager {

    private final EntityManager em;


    public RoleEntity addRole(TenantInfo tenant, RoleData data) {
        if (getRoleByName(tenant, data.getName()) != null) {
            throw new BusinessException("角色不能重名");
        }
        RoleEntity role = new RoleEntity(tenant, data.getName());
        role.setName(data.getName());
        role.setRemark(data.getRemark());
        return em.merge(role);
    }

    public RoleEntity getRoleByCode(TenantInfo tenant, String code) {
        return queryRole(t -> t.tenantId.eq(tenant.toTenantId()).and(t.code.eq(code))).fetchOne();
    }


    public void editRole(TenantInfo tenant, RoleData role) {
        RoleEntity entity = requireRole(role.getId());
        if (!entity.getTenant().equals(tenant)) {
            throw new IllegalArgumentException("租户不匹配");
        }

        if (role.getName() != null) {
            var exist = getRoleByName(tenant, role.getName());
            if (exist != null && !exist.getId().equals(entity.getId())) {
                throw new BusinessException("角色不能重名");
            }
            entity.setName(role.getName());
        }
        if (role.getRemark() != null) {
            entity.setRemark(role.getRemark());
        }
        em.merge(entity);
    }

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     */
    public void deleteRole(TenantInfo tenant, long roleId) {
        RoleEntity role = getRole(roleId);
        if (role != null) {
            if (!role.getTenant().equals(tenant)) {
                throw new IllegalArgumentException("租户不匹配");
            }
            em.remove(role);
            queryRoleMember(t -> t.roleId.eq(roleId)).fetch().forEach(em::remove);
            queryRoleDataScope(t -> t.roleId.eq(roleId)).fetch().forEach(em::remove);
        }
    }

    public RoleEntity getRoleByName(TenantInfo tenant, String name) {
        return queryRole(t -> t.tenantId.eq(tenant.toTenantId()).and(t.name.eq(name))).fetchOne();
    }

    /**
     * 查询当前登入用户都有哪些数据权限
     * @param tenant
     * @param userId
     * @return
     */
    public Set<RoleDataScope> getUserDataScopes(TenantInfo tenant, Long userId){
        final Set<RoleEntity> roleEntities = this.getRoleByTenantUserId(tenant, userId);
        if(roleEntities.isEmpty()){
            return Set.of();
        }
        final List<RoleDataScopeEntity> fetch = queryRoleDataScope(t -> t.roleId.in(roleEntities.stream().map(RoleEntity::getId).collect(Collectors.toList()))).fetch();
        return fetch.stream().map(RoleDataScope::new).collect(Collectors.toSet());
    }

    /**
     * 查询角色都有哪些数据权限
     * @param tenant
     * @param roleId
     * @return
     */
    public Map<OwnerType,Map<Long,String>>  getRoleDataScopes(TenantInfo tenant, Long roleId){
        final Set<SupplierCorporationEntity> supplierCorporationEntitySet = new HashSet<>();
        final Set<CustomerEntity> customerEntitySet = new HashSet<>();
        final Set<BusinessContractEntity> businessContractEntitySet = new HashSet<>();
        QRoleDataScopeEntity qRoleDataScopeEntity = QRoleDataScopeEntity.roleDataScopeEntity;
        final QSupplierCorporationEntity qSupplierCorporationEntity = QSupplierCorporationEntity.supplierCorporationEntity;
        QCustomerEntity qCustomerEntity = QCustomerEntity.customerEntity;
        QBusinessContractEntity qBusinessContractEntity = QBusinessContractEntity.businessContractEntity;
        Map<OwnerType,Map<Long,String>> dataScopes = new HashMap<>(Map.of());
        final List<Tuple> tuples = new JPAQueryFactory(em)
                .select(qRoleDataScopeEntity, qSupplierCorporationEntity, qCustomerEntity, qBusinessContractEntity)
                .from(qRoleDataScopeEntity)
                .leftJoin(qSupplierCorporationEntity).on(qRoleDataScopeEntity.dataId.eq(qSupplierCorporationEntity.id).and(qRoleDataScopeEntity.dataType.eq(OwnerType.CORPORATION)))
                .leftJoin(qCustomerEntity).on(qRoleDataScopeEntity.dataId.eq(qCustomerEntity.id).and(qRoleDataScopeEntity.dataType.eq(OwnerType.CUSTOMER)))
                .leftJoin(qBusinessContractEntity).on(qRoleDataScopeEntity.dataId.eq(qBusinessContractEntity.id).and(qRoleDataScopeEntity.dataType.eq(OwnerType.CONTRACT)))
                .where(qRoleDataScopeEntity.id.eq(roleId).and(qRoleDataScopeEntity.tenantId.eq(tenant.toTenantId())))
                .fetch();
        tuples.forEach(t -> {
                    switch (t.get(qRoleDataScopeEntity).getDataType()) {
                        case CORPORATION -> supplierCorporationEntitySet.add(t.get(qSupplierCorporationEntity));
                        case CUSTOMER -> customerEntitySet.add(t.get(qCustomerEntity));
                        case CONTRACT -> businessContractEntitySet.add(t.get(qBusinessContractEntity));
                        default -> {}
                    }
                });
        dataScopes.put(OwnerType.CORPORATION, CollectionUtil.isNotEmpty(supplierCorporationEntitySet)?supplierCorporationEntitySet.stream().collect(Collectors.toMap(SupplierCorporationEntity::getId, SupplierCorporationEntity::getName)) : Map.of());
        dataScopes.put(OwnerType.CUSTOMER, CollectionUtil.isNotEmpty(customerEntitySet)?customerEntitySet.stream().collect(Collectors.toMap(CustomerEntity::getId, CustomerEntity::getName)) : Map.of());
        dataScopes.put(OwnerType.CONTRACT, CollectionUtil.isNotEmpty(businessContractEntitySet)?businessContractEntitySet.stream().collect(Collectors.toMap(BusinessContractEntity::getId, BusinessContractEntity::getName)) : Map.of());
        return dataScopes;
    }


    /**
     * 查询当前登入用户都有哪些角色
     * @param tenant
     * @param userId
     * @return
     */
    public Set<RoleEntity> getRoleByTenantUserId(TenantInfo tenant, Long userId) {
        final SupplierMemberEntity supplierMemberEntity = querySupplierMember(t -> t.userId.eq(userId).and(t.ownedBy.eq(new OwnedByFragment(OwnerType.valueOf(tenant.getType().name()), tenant.getId())))).fetchOne();
        if (supplierMemberEntity == null) {
            throw new IllegalStateException("服务商内没有此用户");
        }

        final List<RoleMemberEntity> roleMemberEntityList = queryRoleMember(t -> t.subjectId.eq(supplierMemberEntity.getId())).fetch();

        if (roleMemberEntityList.isEmpty()) {
            return Set.of();
        }
        return new HashSet<>(queryRole(t -> t.id.in(roleMemberEntityList.stream().map(RoleMemberEntity::getRoleId).toList())).fetch());
    }

    /**
     * 设置角色的成员
     */
    public void setRoleMembers(long roleId, List<Long> subjects) {

        Set<Long> set = new HashSet<>(subjects);

        List<RoleMemberEntity> members = queryRoleMember(t -> t.roleId.eq(roleId)).fetch();

        // 处理删除并且去除已存在的member
        members.stream()
                .filter(o -> !set.remove(o.getSubjectId()))
                .forEach(em::remove);

        // 处理新增member
        set.forEach(o -> em.merge(new RoleMemberEntity(roleId, o)));
    }

    public List<RoleMemberEntity> getRoleMembers(long roleId) {
        return queryRoleMember(t -> t.roleId.eq(roleId)).fetch();
    }

    /**
     * 设置角色的权限
     *
     * @param roleId      角色ID
     * @param authorities 权限列表
     */
    public void setRoleAuthorities(long roleId, List<String> authorities) {
        RoleEntity role = queryRole(t -> t.id.eq(roleId)).fetchOne();
        if (role == null) {
            throw new IllegalArgumentException("角色不存在: roleId=" + roleId);
        }
        role.setAuthorities(authorities);
        em.merge(role);
    }

    /**
     * 获取角色的权限
     *
     * @param roleId 角色ID
     */
    public List<String> getRoleAuthorities(long roleId) {
        return requireRole(roleId).getAuthorities();
    }


    /**
     * 获取用户的权限
     */
    public Set<String> getSubjectAuthorities(TenantInfo tenant, Long subjectId) {

        QRoleEntity t2 = QRoleEntity.roleEntity;
        QRoleMemberEntity t1 = QRoleMemberEntity.roleMemberEntity;

        List<RoleEntity> roles = new JPAQueryFactory(em)
                .select(t2)
                .distinct()
                .from(t2)
                .innerJoin(t1).on(t1.roleId.eq(t2.id))
                .where(t1.subjectId.eq(subjectId).and(t2.tenantId.eq(tenant.toTenantId())).and(t2.disabled.eq(Boolean.FALSE)))
                .fetch();

        return roles.stream()
                .flatMap(o -> o.getAuthorities().stream())
                .collect(Collectors.toSet());
    }

    /**
     * 删除subject所有相关联的角色成员
     */
    public void deleteMembersOfSubject(TenantInfo tenant, Long subjectId) {

        var roles = queryRole(t -> t.tenantId.eq(tenant.toTenantId())).fetch();

        queryRoleMember(t -> t.roleId.in(roles.stream().map(RoleEntity::getId).collect(Collectors.toSet())).and(t.subjectId.eq(subjectId)))
                .fetch()
                .forEach(em::remove);
    }

    public RoleEntity requireRole(long roleId) {
        RoleEntity role = getRole(roleId);
        if (role == null) {
            throw new IllegalStateException("角色不存在: " + roleId);
        }
        return role;
    }

    private RoleEntity getRole(long roleId) {
        return em.find(RoleEntity.class, roleId);
    }

    private JPAQuery<RoleEntity> queryRole(Function<QRoleEntity, Predicate> condition) {
        QRoleEntity t = QRoleEntity.roleEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<RoleMemberEntity> queryRoleMember(Function<QRoleMemberEntity, Predicate> condition) {
        QRoleMemberEntity t = QRoleMemberEntity.roleMemberEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<SupplierMemberEntity> querySupplierMember(Function<QSupplierMemberEntity, Predicate> condition) {
        QSupplierMemberEntity t = QSupplierMemberEntity.supplierMemberEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<RoleDataScopeEntity> queryRoleDataScope(Function<QRoleDataScopeEntity, Predicate> condition) {
        QRoleDataScopeEntity t = QRoleDataScopeEntity.roleDataScopeEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    public void disableRole(long roleId, boolean disabled) {
        RoleEntity role = requireRole(roleId);
        role.setDisabled(disabled);
        em.merge(role);
    }

    public void memberSetRoles(long memberId, Set<Long> roleIds) {
        //删除
        queryRoleMember(t -> t.subjectId.eq(memberId)).fetch().forEach(em::remove);
        roleIds.forEach(roleId -> em.merge(new RoleMemberEntity(roleId, memberId)));
    }

    public RoleDataScopeEntity addRoleDataScopes(TenantInfo tenantInfo, Long roleId, OwnedByFragment ownedBy) {
        final RoleEntity roleEntity = requireRole(roleId);
        if (!roleEntity.getTenant().equals(tenantInfo)) {
            throw new BusinessException("无此角色");
        }
        final RoleDataScopeEntity roleDataScopeEntity = queryRoleDataScope(t -> t.roleId.eq(roleId).and(t.dataType.eq(ownedBy.getOwnerType()).and(t.dataId.eq(ownedBy.getOwnerId())))).fetchOne();
        if (roleDataScopeEntity != null){
            return roleDataScopeEntity;
        }
        final RoleDataScopeEntity entity = new RoleDataScopeEntity(roleEntity, ownedBy);
        em.persist(entity);
        return entity;
    }

    public List<RoleData> getRoleByDataScope(OwnerType ownerType, Long id) {
        final List<RoleDataScopeEntity> scopeEntities = queryRoleDataScope(t -> t.dataType.eq(ownerType).and(t.dataId.eq(id))).fetch();
        if (CollectionUtil.isNotEmpty(scopeEntities)){
            return scopeEntities.stream().map(t -> {
                final RoleData roleData = new RoleData();
                roleData.setId(t.getRoleId());
                roleData.setName(t.getName());
                roleData.setCode(t.getCode());
                return roleData;
            }).collect(Collectors.toList());
        }
        return null;
    }

    public void removeRoleMembers(Long id) {
        queryRoleMember(t->t.subjectId.eq(id)).fetch().forEach(em::remove);
    }

    public void removeRoleDataScopes(OwnedByFragment ownedByFragment) {
        queryRoleDataScope(t->t.dataType.eq(ownedByFragment.getOwnerType()).and(t.dataId.eq(ownedByFragment.getOwnerId()))).fetch().forEach(em::remove);
    }

    /**
     * 用于重新编辑数据权限
     * @param tenantInfo
     * @param ownedByFragment
     * @param roleData
     */
    public void editDataScope(TenantInfo tenantInfo, OwnedByFragment ownedByFragment, List<RoleData> roleData) {
        //清空原所有关于这个主体的权限
        this.removeRoleDataScopes(ownedByFragment);
        if (CollectionUtil.isNotEmpty(roleData)){
            roleData.forEach(o-> this.addRoleDataScopes(tenantInfo, o.getId(),new OwnedByFragment(ownedByFragment.getOwnerType(), ownedByFragment.getOwnerId())));
        }

    }
}
