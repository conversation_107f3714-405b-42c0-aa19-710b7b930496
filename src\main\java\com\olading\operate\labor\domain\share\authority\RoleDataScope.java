package com.olading.operate.labor.domain.share.authority;

import com.olading.operate.labor.domain.share.info.OwnerType;
import lombok.Data;

@Data
public class RoleDataScope {

    private Long id;

    private String code;

    private String name;

    private Long roleId;

    private OwnerType dataType;

    private Long dataId;

    public RoleDataScope() {
    }

    public RoleDataScope(RoleDataScopeEntity entity) {
        this.id = entity.getId();
        this.name = entity.getName();
        this.code = entity.getCode();
        this.roleId = entity.getRoleId();
        this.dataType = entity.getDataType();
        this.dataId = entity.getDataId();
    }
}
