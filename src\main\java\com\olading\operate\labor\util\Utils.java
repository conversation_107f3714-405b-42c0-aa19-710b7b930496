package com.olading.operate.labor.util;

import com.olading.operate.labor.util.valid.CommonValidate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class Utils {

    public static Date toDate(LocalDateTime time) {
        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDate(LocalDate time) {
        return toDate(time.atStartOfDay());
    }

    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String encodeUrl(String s) {
        try {
            return URLEncoder.encode(s, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalStateException(e);
        }
    }

    /**
     * 下划线转驼峰法(默认小驼峰)
     *
     * @param line       源字符串
     * @param smallCamel 大小驼峰,是否为小驼峰(驼峰，第一个字符是大写还是小写)
     * @return 转换后的字符串
     */
    public static String underline2Camel(String line, boolean... smallCamel) {
        if (line == null || "".equals(line)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        Pattern pattern = Pattern.compile("([A-Za-z\\d]+)(_)?");
        Matcher matcher = pattern.matcher(line);
        //匹配正则表达式
        while (matcher.find()) {
            String word = matcher.group();
            //当是true 或则是空的情况
            if ((smallCamel.length == 0 || smallCamel[0]) && matcher.start() == 0) {
                sb.append(Character.toLowerCase(word.charAt(0)));
            } else {
                sb.append(Character.toUpperCase(word.charAt(0)));
            }

            int index = word.lastIndexOf('_');
            if (index > 0) {
                sb.append(word.substring(1, index).toLowerCase());
            } else {
                sb.append(word.substring(1).toLowerCase());
            }
        }
        return sb.toString();
    }

    /**
     * 驼峰法转下划线
     *
     * @param line 源字符串
     * @return 转换后的字符串
     */
    public static String camel2Underline(String line) {
        if (line == null || "".equals(line)) {
            return "";
        }
        line = String.valueOf(line.charAt(0)).toUpperCase()
                .concat(line.substring(1));
        StringBuilder sb = new StringBuilder();
        Pattern pattern = Pattern.compile("[A-Z]([a-z\\d]+)?");
        Matcher matcher = pattern.matcher(line);
        while (matcher.find()) {
            String word = matcher.group();
            sb.append(word.toUpperCase());
            sb.append(matcher.end() == line.length() ? "" : "_");
        }
        return sb.toString();
    }

    public static String loadResource(String path) {
        try (InputStream in = Utils.class.getResourceAsStream(path)) {
            if (in == null) {
                return null;
            }
            return IOUtils.toString(in, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static List<String> loadResourceLine(String path) {
        try (InputStream in = Utils.class.getResourceAsStream(path)) {
            if (in == null) {
                throw new IllegalStateException("资源文件不存在: " + path);
            }
            return IOUtils.readLines(in, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 安全的转换为一个外部URL, 不允许为常见的内网地址
     */
    public static URL toSecureUrl(String s) throws MalformedURLException {
        URL url = new URL(s);
        if (!"https".equals(url.getProtocol())) {
            throw new IllegalArgumentException("必须是https协议");
        }
        if (CommonValidate.isIpV4(url.getHost())) {
            throw new IllegalArgumentException("不能是ip地址");
        }
        if ("localhost".equals(url.getHost())) {
            throw new IllegalArgumentException("主机名不正确");
        }
        return url;
    }

    public static void main(String[] args) throws MalformedURLException {
        toSecureUrl("https://localhost/a/b/c?a=1");
    }

    /**
     * 计算申报月份：税款所属期+1个月
     *
     * @param taxPeriod 税款所属期，格式：YYYY-MM
     * @return 申报月份，格式：YYYY-MM
     */
    public static String calculateDeclareMonth(String taxPeriod) {
        try {
            // taxPeriod格式：YYYY-MM
            String[] parts = taxPeriod.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 月份+1
            month++;
            if (month > 12) {
                month = 1;
                year++;
            }

            return String.format("%04d-%02d", year, month);
        } catch (Exception e) {
            log.error("计算申报月份失败，税款所属期: {}", taxPeriod, e);
            return taxPeriod; // 出错时返回原值
        }
    }

}
