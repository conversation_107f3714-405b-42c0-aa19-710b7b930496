package com.olading.operate.labor.domain.share.archive;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

@Transactional
@Component
@RequiredArgsConstructor
public class ArchiveManager {

    private static final long ARCHIVE_MAX_SIZE = 1024 * 1024 * 5;

    private final EntityManager em;

    /**
     * 保存一个文件
     */
    public long save(String name, byte[] data, String sha256) {

        if (data.length > ARCHIVE_MAX_SIZE) {
            throw new IllegalStateException("文件太大");
        }

        ArchiveEntity archive = new ArchiveEntity(name, data, sha256);
        return em.merge(archive).getId();
    }

    public InputStream load(long id) {
        return new ByteArrayInputStream(require(id).getData());
    }

    public String getName(long id) {
        return require(id).getName();
    }

    public ArchiveEntity require(long id) {
        ArchiveEntity archive = em.find(ArchiveEntity.class, id);
        if (archive == null) {
            throw new IllegalStateException("文件不存在");
        }
        return archive;
    }


}
