package com.olading.operate.labor.domain.identity;

import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.identity.repository.IdentityFactorAuthRecordRepository;
import com.olading.operate.labor.domain.identity.repository.IdentityFaceRecordRepository;
import com.olading.operate.labor.domain.identity.repository.IdentityOcrRecordRepository;
import com.olading.operate.labor.domain.share.identity.dto.FaceAuthResult;
import com.olading.operate.labor.domain.share.identity.dto.OcrIdentifyResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 认证记录管理器
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthRecordManager {
    
    private final IdentityOcrRecordRepository ocrRecordRepository;
    private final IdentityFaceRecordRepository faceRecordRepository;
    private final IdentityFactorAuthRecordRepository factorRecordRepository;
    
    /**
     * 保存OCR记录
     */
    public Long saveOcrRecord(AuthContext context, OcrIdentifyResult result) {
        IdentityOcrRecordEntity record = new IdentityOcrRecordEntity(TenantInfo.parse(context.getTenantId()));
        
        // 基础信息（必填）
        record.setRecordNo(generateRecordNo("OCR"));
        record.setAuthScene(context.getAuthScene());
        record.setOcrStatus(result.isSuccess() ? "SUCCESS" : "FAILED");
        
        // 用户信息（可空）
        record.setUserId(context.getUserId());
        
        // 业务信息（可选）
        if (context.getSupplierId() != null) {
            record.setSupplierId(context.getSupplierId());
        }
        if (context.getCorporationId() != null) {
            record.setCorporationId(context.getCorporationId());
        }

        
        // 文件信息
        record.setPersonalIdFileId(context.getPersonalIdFileId());
        record.setNationalEmblemFileId(context.getNationalEmblemFileId());
        
        // OCR结果信息
        if (result.isSuccess()) {
            record.setName(result.getName());
            record.setIdCardNo(result.getIdNo());
            record.setSex(result.getGender());
            record.setNation(result.getNation());
            record.setBirthday(result.getBirth());
            record.setBirthAddress(result.getAddress());
            record.setIdCardSigningOrgans(result.getAuthority());
            record.setIdCardValidityPeriod(result.getValidDate());
            // 置信度分数暂时设为null，如果后续RPC接口提供则可以设置
            record.setConfidenceScore(null);
        } else {
            record.setErrorMessage(result.getErrorMessage());
        }
        
        record = ocrRecordRepository.save(record);
        log.info("保存OCR记录成功: recordId={}, authScene={}, userId={}, supplierId={}", 
            record.getId(), context.getAuthScene(), context.getUserId(), context.getSupplierId());
        
        return record.getId();
    }
    
    /**
     * 保存人脸识别记录
     */
    public Long saveFaceRecord(AuthContext context, String name, String idCard, 
                              FaceAuthResult result) {
        IdentityFaceRecordEntity record = new IdentityFaceRecordEntity(TenantInfo.parse(context.getTenantId()));
        
        // 基础信息（必填）
        record.setRecordNo(generateRecordNo("FACE"));
        record.setAuthScene(context.getAuthScene());
        record.setName(name);
        record.setIdCard(idCard);
        record.setFaceStatus(result.isSuccess() ? "SUCCESS" : "FAILED");
        
        // 用户信息（可空）
        record.setUserId(context.getUserId());
        
        // 业务信息（可选）
        if (context.getSupplierId() != null) {
            record.setSupplierId(context.getSupplierId());
        }
        if (context.getCorporationId() != null) {
            record.setCorporationId(context.getCorporationId());
        }
        
        // 文件信息
        record.setFaceVideoFileId(context.getFaceVideoFileId());
        
        // 人脸识别结果
        if (result.isSuccess()) {
            record.setFaceResult(result.isPassed() ? "PASSED" : "REJECTED");
            record.setSimilarityScore(result.getSimilarity() != null ? 
                BigDecimal.valueOf(result.getSimilarity()).divide(BigDecimal.valueOf(100)) : null);
            record.setLivenessScore(result.getLiveRate() != null ? 
                BigDecimal.valueOf(result.getLiveRate()).divide(BigDecimal.valueOf(100)) : null);
        } else {
            record.setErrorMessage(result.getErrorMessage());
        }
        
        record = faceRecordRepository.save(record);
        log.info("保存人脸记录成功: recordId={}, authScene={}, userId={}, supplierId={}", 
            record.getId(), context.getAuthScene(), context.getUserId(), context.getSupplierId());
        
        return record.getId();
    }
    
    /**
     * 保存要素鉴权记录
     */
    public Long saveFactorRecord(AuthContext context, String authType, String name, String idCard, 
                                String bankCardNumber, String phoneNumber, String errorMsg) {
        IdentityFactorAuthRecordEntity record = new IdentityFactorAuthRecordEntity(TenantInfo.parse(context.getTenantId()));
        
        // 基础信息（必填）
        record.setRecordNo(generateRecordNo("FACTOR"));
        record.setAuthScene(context.getAuthScene());
        record.setAuthType(authType);
        record.setName(name);
        record.setIdCard(idCard);
        record.setBankCardNumber(bankCardNumber);
        record.setPhoneNumber(phoneNumber);
        
        // 用户信息（可空）
        record.setUserId(context.getUserId());
        
        // 业务信息（可选）
        if (context.getSupplierId() != null) {
            record.setSupplierId(context.getSupplierId());
        }
        if (context.getCorporationId() != null) {
            record.setCorporationId(context.getCorporationId());
        }
        
        // 认证结果
        if (errorMsg == null) {
            record.setAuthStatus("SUCCESS");
            record.setAuthResult("CONSISTENT");
        } else {
            record.setAuthStatus("FAILED");
            record.setAuthResult("INCONSISTENT");
            record.setErrorMessage(errorMsg);
        }
        
        record = factorRecordRepository.save(record);
        log.info("保存要素鉴权记录成功: recordId={}, authType={}, authScene={}, userId={}, supplierId={}", 
            record.getId(), authType, context.getAuthScene(), context.getUserId(), context.getSupplierId());
        
        return record.getId();
    }
    
    /**
     * 生成记录编号
     */
    public static String generateRecordNo(String type) {
        // 记录编号格式：类型 + 年月日时分秒 + 毫秒数 + 4位随机数
        return type + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"))+ String.format("%04d", (int)(Math.random() * 10000)) ;
    }
}