package com.olading.operate.labor.domain.share.signing;


import com.olading.operate.labor.domain.share.signing.response.*;
import com.olading.operate.labor.domain.share.signing.request.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

@Service
public interface CloudSigningService {

    /**
     * 授权访问资源，颁发token。
     * 给用户颁发token，用户浏览器携带token后方可以访问模板编辑和文件签名页面
     */
    BaseCloudSigningResponse<AuthorizeResponse> authorize(AuthorizeRequest request);

    /**
     * 创建一个空的模板草稿。
     * 1.	创建模板草稿后，需要继续调用上传接口上传模板文件，之后才能使用模板编辑页面进行编辑。
     * 2.	草稿一旦编辑保存后不再允许修改
     * 3.	草稿长时间未被编辑会被系统自动删除
     * 4.	每个模板可以设置签名步骤。例如两方协议就是两个步骤
     * 5.	每个模板的步骤不能重名
     */
    BaseCloudSigningResponse<CreateTemplateResponse> createTemplate(CreateTemplateRequest request);

    /**
     * 上传模板文件
     */
    UploadTemplateFileResponse uploadTemplateFile(ByteArrayOutputStream outputStream, UploadTemplateFileRequest request) throws IOException;

    /**
     * 编辑模板
     */
    BaseCloudSigningResponse<EditTemplateResponse> editTemplate(EditTemplateRequest request);

    /**
     * 创建待签名文件
     * 1.	调用接口创建后需要继续通过返回的地址上传文件数据
     * 2.	长时间未进行签名的文件会被自动关闭
     */
    BaseCloudSigningResponse<CreateSignFileResponse> createSignFile(CreateSignFileRequest request);

    /**
     * 传入用户信息准备进行签名
     * 1.	传入签名相关的信息。
     * 2.	如果需要用户在页面主动签名，将用户浏览器跳转至signUrl返回的地址
     * 3.	如果需要系统自动签名，调用【签名】接口
     */
    BaseCloudSigningResponse<PrepareSignResponse> prepareSign(PrepareSignRequest request);

    /**
     * 签名
     * 1.	此接口为异步接口，需要主动查询签名状态或者等待处理通知
     */
    BaseCloudSigningResponse<SignResponse> sign(SignRequest request);

    /**
     * 查询模板的信息
     */
    BaseCloudSigningResponse<QueryTemplateResponse> queryTemplate(QueryTemplateRequest request);

    /**
     * 查询待签名文件的信息
     */
    BaseCloudSigningResponse<QueryFileResponse> queryFile(QueryFileRequest request);

    /**
     * 查询签名信息
     * 1.	签名完成后，可以通过此接口进行查询
     */
    BaseCloudSigningResponse<QuerySignResponse> querySign(QuerySignRequest request);

    /**
     * 生成签章图片
     */
    BaseCloudSigningResponse<BuildSignImgResponse> buildSignImg(BuildSignImgRequest request) throws IOException ;

    /**
     * 生成签章图片（个人自定义签章）
     */
    BaseCloudSigningResponse<BuildPersonnelSignImgResponse> buildPersonnelSignImg(BuildPersonnelSignImgRequest request) throws IOException;

    /**
     * 对身份证进行OCR识别
     */
    BaseCloudSigningResponse<IdCardOcrResponse> idCardOcr(IdCardOcrRequest request);

    /**
     * 运营商三要素认证
     */
    BaseCloudSigningResponse<AuthPhone3Response> authPhone3(AuthPhone3Request request);

    /**
     * 下载文档
     * @param url
     * @return
     */
    InputStream downLoadByUrl(String url);
}
