package com.olading.operate.labor.domain.share.task;

import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional
@Component
@RequiredArgsConstructor
public class TaskRepository {

    // 表示最多返回的任务数量
    private static final long MAX_TASKS_PER_OWNER = 10;

    private final EntityManager em;
    private final FileManager fileManager;


    public TaskEntity submit(long owerId, OwnerType ownerType, TaskType taskType, String taskParam, String attachments) {
        TaskEntity task = new TaskEntity(owerId, ownerType, taskType, taskParam, attachments);
        return em.merge(task);
    }

    public TaskEntity markAsRunning(long taskId) {
        var task = em.find(TaskEntity.class, taskId);
        task.markAsRunning();
        return em.merge(task);
    }

    public void markAsAttachments(long taskId, String attachments) {
        var task = em.find(TaskEntity.class, taskId);
        task.setAttachments(List.of(attachments));
        em.merge(task);
    }

    public void markAsSuccess(long taskId, String result) {
        var task = em.find(TaskEntity.class, taskId);
        task.markAsSuccess(result);
        em.merge(task);
    }

    public void markAsFailed(long taskId, String error) {
        var task = em.find(TaskEntity.class, taskId);
        task.markAsFailed(error);
        em.merge(task);
    }

    public List<TaskEntity> findExecutableTasks() {
        QTaskEntity t = QTaskEntity.taskEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.taskStatus.eq(TaskStatus.WAITING))
                .limit(MAX_TASKS_PER_OWNER)
                .fetch();
    }

    public void delete(long taskId) {
        var task = em.find(TaskEntity.class, taskId);
        task.delete();
        for (String id : task.getAttachments()) {
            fileManager.delete(id);
        }
        em.merge(task);
    }
}
