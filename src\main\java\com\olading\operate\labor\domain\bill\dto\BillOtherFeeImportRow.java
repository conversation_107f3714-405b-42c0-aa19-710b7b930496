package com.olading.operate.labor.domain.bill.dto;

import com.olading.operate.labor.app.web.biz.bill.ImportValidationErrorData;
import com.olading.operate.labor.util.excel.ExcelColumn;
import com.olading.operate.labor.util.excel.ExcelRow;
import com.olading.operate.labor.util.validation.constraints.Date;
import com.olading.operate.labor.util.validation.constraints.IdNumber;
import com.olading.operate.labor.util.validation.constraints.Money;
import com.olading.operate.labor.util.validation.constraints.Text;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 其他费用导入Excel行模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BillOtherFeeImportRow extends ExcelRow {
    
    @ExcelColumn(name = "费用名称", required = true, orderNum = 1)
    @Text(required = true, maxLength = 100,message = "请输入费用名称，长度不能超过100个字符")
    private String feeName;
    
    @ExcelColumn(name = "费用金额", required = true, orderNum = 2)
    @Money(message = "请输入正确的金额格式，如：100.00")
    private BigDecimal feeAmount;
    
    @ExcelColumn(name = "产生时间", required = true, orderNum = 3)
    @Date(required = true, message = "请输入正确的日期格式，如：2025-07-15")
    private LocalDate occurDate;
    
    @ExcelColumn(name = "产生人", required = true, orderNum = 4)
    @Text(required = true, maxLength = 50,message = "请输入产生人，长度不能超过50个字符")
    private String laborName;
    
    @ExcelColumn(name = "身份证号", required = false, orderNum = 5)
    @IdNumber(required = false, message = "请输入身份证号，长度不能超过18个字符")
    private String idCard;
    
    @ExcelColumn(name = "费用用途", required = false, orderNum = 6)
    @Text(required = false, maxLength = 200,message = "费用用途长度不能超过200个字符")
    private String feePurpose;

    public ImportValidationErrorData toValidationErrorData() {
        return new ImportValidationErrorData(feeName, feeAmount, occurDate, laborName, idCard, feePurpose, this.getRowErrorString());
    }

}