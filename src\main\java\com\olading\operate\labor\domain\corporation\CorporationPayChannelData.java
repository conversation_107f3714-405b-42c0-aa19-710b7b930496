package com.olading.operate.labor.domain.corporation;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.olading.operate.labor.app.web.biz.corporation.CorporationController;
import com.olading.operate.labor.domain.supplier.PayChannelConfigEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.util.stream.Collectors;

@Data
public class CorporationPayChannelData {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "灵工平台id")
    private Long supplierId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "通道编码")
    private String payChannel;

    @Schema(description = "通道名称")
    private String payChannelName;

    @NotNull
    @Schema(description = "是否默认通道")
    private Boolean isDefault =  false;

    @Schema(description = "是否启用")
    private Boolean isOpen = true;

    @Schema(description = "通道配置信息")
    private String channelConfig;

    public CorporationPayChannelData() {
    }

    public CorporationPayChannelData(CorporationPayChannelEntity entity, PayChannelConfigEntity payChannelConfigEntity) {
        this.id = entity.getId();
        this.supplierCorporationId = entity.getSupplierCorporationId();
        this.supplierId = entity.getSupplierId();
        this.payChannel = entity.getPayChannel();
        this.channelConfig = entity.getChannelConfig();
        this.isDefault = entity.getIsDefault();
        this.isOpen = entity.getIsOpen();
        this.payChannelName = payChannelConfigEntity.getChannelName();
    }

    public static CorporationPayChannelData of(CorporationController.CorporationConfigPayChannel data){
        CorporationPayChannelData result = new CorporationPayChannelData();
        result.payChannel = data.getPayChannel();
        result.channelConfig = JSONUtil.toJsonStr(data.getChannelConfig());
        result.isOpen = data.isOpen();
        result.isDefault = data.isDefault();
        result.supplierCorporationId = data.getCorporationId();
        return result;
    }

}