package com.olading.operate.labor.domain.proxy.order;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/10 18:47
 */
public enum ProxyOrderStatusEnum {
    CREATE("创建"),
    CHECK_SUCC("校验成功"),
    CHECK_FAIL("校验失败"),
    PROCESSING("处理中"),
    REMIT("出款成功"),
    FAIL("失败"),
    REFUND("退款"),
    DELETED("删除");
    private String desc;
    ProxyOrderStatusEnum(String desc) {
        this.desc = desc;
    }
}
