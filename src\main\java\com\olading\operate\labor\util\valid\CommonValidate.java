package com.olading.operate.labor.util.valid;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

public class CommonValidate {

    private static final Pattern IP_V6 = Pattern.compile("^([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4})$");

    public static boolean isIpV4(String s) {
        if (StringUtils.isBlank(s)) {
            return false;
        }

        String[] arr = s.split(Pattern.quote("."));
        if (arr.length != 4) {
            return false;
        }
        try {
            for (String o : arr) {
                int i = Integer.parseInt(o);
                if (i < 0 || i > 256) {
                    return false;
                }
            }
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }

    public static boolean isIpV6(String s) {
        if (StringUtils.isBlank(s)) {
            return false;
        }
        if ("::1".equals(s)) {
            return true;
        }
        return IP_V6.matcher(s).find();
    }

    public static void main(String[] args) {
        System.out.println(isIpV4("127.0.0.1"));
        System.out.println(isIpV4("localhost"));
        System.out.println(isIpV6("2001:0db8:85a3:0000:0000:8a2e:0370:7334"));
    }
}
