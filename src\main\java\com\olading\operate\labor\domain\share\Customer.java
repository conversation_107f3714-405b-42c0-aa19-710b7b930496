package com.olading.operate.labor.domain.share;

import lombok.Builder;

import java.util.Set;

/**
 * 客户
 */
@Builder
public class Customer {

    private long id;

    private String name;

    /**
     * 开通的虚拟账户ID
     */
    private Set<Long> accounts;

    private String clientKey;

    private String notifyUrl;

    public long getId() {
        return id;
    }

    public String getClientKey() {
        return clientKey;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public Set<Long> getAccounts() {
        return accounts;
    }
}
