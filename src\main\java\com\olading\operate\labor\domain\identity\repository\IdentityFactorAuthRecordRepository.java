package com.olading.operate.labor.domain.identity.repository;

import com.olading.operate.labor.domain.identity.IdentityFactorAuthRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 要素鉴权记录仓储接口
 * 任务 2.4: 创建Repository接口
 */
@Repository
public interface IdentityFactorAuthRecordRepository extends JpaRepository<IdentityFactorAuthRecordEntity, Long>, JpaSpecificationExecutor<IdentityFactorAuthRecordEntity> {

    /**
     * 根据记录编号查找记录
     */
    Optional<IdentityFactorAuthRecordEntity> findByRecordNoAndDeletedFalse(String recordNo);

    /**
     * 根据用户ID查找认证记录
     */
    Page<IdentityFactorAuthRecordEntity> findByUserIdAndDeletedFalse(Long userId, Pageable pageable);

    /**
     * 根据供应商ID查找认证记录
     */
    Page<IdentityFactorAuthRecordEntity> findBySupplierIdAndDeletedFalse(Long supplierId, Pageable pageable);

    /**
     * 根据作业主体ID查找认证记录
     */
    Page<IdentityFactorAuthRecordEntity> findByCorporationIdAndDeletedFalse(Long corporationId, Pageable pageable);

    /**
     * 根据身份证号查找认证记录
     */
    List<IdentityFactorAuthRecordEntity> findByIdCardAndDeletedFalse(String idCard);

    /**
     * 根据认证场景查找记录
     */
    List<IdentityFactorAuthRecordEntity> findByAuthSceneAndDeletedFalse(String authScene);
}