package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.app.web.biz.enums.EnumContractSignState;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.Instant;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "t_corporation_protocol_step")
public class CorporationProtocolStepEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Comment("平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Size(max = 20)
    @Comment("租户")
    @Column(name = "tenant_id", length = 20)
    private String tenantId;

    @Comment("合同id")
    @Column(name = "protocol_id")
    private Long protocolId;

    @Comment("操作类型  公章签署、个人签署")
    @Enumerated(EnumType.STRING)
    @Column(name = "operate", length = 32)
    private EnumOperateType operate;

    @Comment("步骤顺序")
    @Column(name = "sortby")
    private Integer sortby;

    @Size(max = 18)
    @Comment("证件号码")
    @Column(name = "id_card", length = 18)
    private String idCard;

    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id")
    private Long supplierCorporationId;

    @Comment("步骤名称")
    @Column(name = "step_name")
    private String stepName;

    @Size(max = 32)
    @Comment("请求云签签名流水号")
    @Column(name = "flow_no", length = 32)
    private String flowNo;

    @Size(max = 32)
    @Comment("签署图片")
    @Column(name = "sign_image", length = 32)
    private String signImage;

    @Comment("签署状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "sign_status", length = 32)
    private EnumContractSignState signStatus;

    @Size(max = 128)
    @Comment("pc端签署url")
    @Column(name = "sign_url", length = 128)
    private String signUrl;

    @Size(max = 128)
    @Comment("移动端签署url")
    @Column(name = "sign_url_mobile", length = 128)
    private String signUrlMobile;

    @Size(max = 32)
    @Comment("签名文件文档服务id 步骤签署完成后查询文件，存储到文件服务")
    @Column(name = "sign_file_id", length = 32)
    private String signFileId;

    @Comment("签署时间")
    @Column(name = "sign_time")
    private LocalDateTime signTime;

    @Comment("是否为当前步骤")
    @Column(name = "current_step")
    private Boolean currentStep;

    @Comment("是否已查看")
    @Column(name = "received")
    private Boolean received;

    @Comment("查看时间")
    @Column(name = "received_time")
    private LocalDateTime receivedTime;

    @Size(max = 500)
    @Comment("签署失败原因")
    @Column(name = "file_reason", length = 500)
    private String fileReason;

    @Comment("version")
    @Column(name = "version")
    private Integer version;

    @Comment("创建时间")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Comment("更新时间")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    public CorporationProtocolStepEntity(TenantInfo tenant) {
        setTenant(tenant);
        createTime = LocalDateTime.now();
        updateTime = createTime;
    }

    public CorporationProtocolStepEntity() {
        createTime = LocalDateTime.now();
        updateTime = createTime;
    }

    public TenantInfo getTenant() {
        if (tenantId == null) {
            return null;
        }
        return TenantInfo.parse(tenantId);
    }

    protected void setTenant(TenantInfo tenant) {
        this.tenantId = tenant.toTenantId();
    }

}