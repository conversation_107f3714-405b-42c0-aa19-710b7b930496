package com.olading.operate.labor.domain.share.signing.response;


import com.olading.operate.labor.domain.share.signing.common.Control;
import com.olading.operate.labor.domain.share.signing.common.Field;
import com.olading.operate.labor.domain.share.signing.common.Step;
import com.olading.operate.labor.domain.share.signing.enums.TemplateStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "查询模板请求参数")
public class QueryTemplateResponse {


    @Schema(description = "模板ID", required = true)
    private Long id;
    @Schema(description = "模板名称", required = true)
    private String name;
    @Schema(description = "模板备注", required = true)
    private String remark;
    @Schema(description = "模板状态", required = true)
    private TemplateStatus status;
    @Schema(description = "模板文件大小", required = true)
    private Long fileSize;
    @Schema(description = "模板文件sha hash值", required = true)
    private String fileHash;
    @Schema(description = "签名步骤列表", required = true)
    private List<Step> steps;
    @Schema(description = "模板域列表", required = true)
    private List<Field> fields;
    @Schema(description = "模板控件列表", required = true)
    private List<Control> controls;

}
