package com.olading.operate.labor.util.excel;


import com.olading.operate.labor.util.exception.ExcelIOException;
import com.lanmaoly.util.lang.ConvertUtils;
import com.lanmaoly.util.lang.StringUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2022/2/22 15:53
 */
public class ExcelReader extends BaseExcel {

    /**
     * 做多读取的数据行数
     */
    private int maxDataRows = 10000;

    /**
     * 是否需要校验
     */
    private boolean needVerify = true;

    /**
     * 是否自动过滤未定义的数据
     */
    private boolean autoFilterOtherText = true;

    /**
     * 是否移除自定义（未绑定字段Field）的列数据
     */
    private boolean removeCustomColumn = false;

    /**
     * Excel字段与系统字段的映射关系
     */
    private ExcelHeaderMappingRule headerMapping;

    /**
     * 数据绑定的类
     */
    private Class<? extends ExcelRow> pojoClass;

    /**
     * 自定义行数据处理器
     * 可以对数据进行校对，初始化等处理
     * 这个函数在数据过滤校对之后执行
     */
    private Consumer<ExcelRow> rowHandler;

    /**
     * 自定义头部数据数据处理器
     */
    private Consumer<ExcelColumnEntity> headerHandler;

    private FormulaEvaluator formulaEvaluator;

    /**
     * 读取数值的最大小数点位数，
     */
    private int maximumFractionDigits = 10;

    /**
     * 读取的结果
     */
    private ExcelResult excelResult;

    /**
     * 只读头部
     */
    private boolean onlyReadHeader = false;

    public static ExcelReaderBuilder builder(Class<? extends ExcelRow> pojoClass) {
        return new ExcelReaderBuilder(pojoClass);
    }

    public ExcelReader() {

    }

    public ExcelReader(Class<? extends ExcelRow> pojoClass) {
        this.pojoClass = pojoClass;
    }

    public int getMaxDataRows() {
        return maxDataRows;
    }

    public void setMaxDataRows(int maxDataRows) {
        this.maxDataRows = maxDataRows;
    }

    public boolean isNeedVerify() {
        return needVerify;
    }

    public void setNeedVerify(boolean needVerify) {
        this.needVerify = needVerify;
    }

    public void setAutoFilterOtherText(boolean autoFilterOtherText) {
        this.autoFilterOtherText = autoFilterOtherText;
    }

    public boolean isRemoveCustomColumn() {
        return removeCustomColumn;
    }

    public void setRemoveCustomColumn(boolean removeCustomColumn) {
        this.removeCustomColumn = removeCustomColumn;
    }

    public ExcelHeaderMappingRule getHeaderMapping() {
        return headerMapping;
    }

    public void setHeaderMapping(ExcelHeaderMappingRule headerMapping) {
        this.headerMapping = headerMapping;
    }

    public Consumer<ExcelRow> getRowHandler() {
        return rowHandler;
    }

    public void setRowHandler(Consumer<ExcelRow> rowHandler) {
        this.rowHandler = rowHandler;
    }

    public Consumer<ExcelColumnEntity> getHeaderHandler() {
        return headerHandler;
    }

    public void setHeaderHandler(Consumer<ExcelColumnEntity> headerHandler) {
        this.headerHandler = headerHandler;
    }

    public ExcelResult getExcelResult() {
        return excelResult;
    }

    public int getMaximumFractionDigits() {
        return maximumFractionDigits;
    }

    public void setMaximumFractionDigits(int maximumFractionDigits) {
        this.maximumFractionDigits = maximumFractionDigits;
    }

    public ExcelHeader readHeader(InputStream inputStream) throws ExcelIOException {
        try {
            onlyReadHeader = true;
            excelResult = read(inputStream);
            onlyReadHeader = false;
            return excelResult.getExcelHeader();
        } catch (Exception e) {
            onlyReadHeader = false;
            throw new ExcelIOException(e.getMessage());
        }
    }

    public ExcelResult readResourceFile(String filePath) {
        InputStream inputStream = null;
        try {
            File file = new File(getClass().getClassLoader().getResource(filePath).getFile());
            inputStream = new FileInputStream(file);
        } catch (Exception e) {
            throw new ExcelIOException(String.format("读取本地文件'%s'失败。", filePath));
        }

        return read(inputStream);
    }

    public ExcelResult read(InputStream inputStream) throws ExcelIOException {
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(inputStream);
        } catch (Exception e) {
            throw new ExcelIOException("读取Excel文件流失败，请检查文件是否损坏。");
        }

        return read(workbook);
    }

    public ExcelResult read(Workbook workbook) {
        formulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
        if (null == sheetIndex) {
            if (!StringUtils.isBlank(this.sheetName)) {
                sheetIndex = workbook.getSheetIndex(sheetName);
            } else {
                sheetIndex = workbook.getActiveSheetIndex();
            }
        }

        Sheet sheet = workbook.getSheetAt(sheetIndex);

        ExcelHeader excelHeader = new ExcelHeader();
        excelResult = new ExcelResult();
        excelResult.setExcelHeader(excelHeader);

        /**
         * 标准格式：
         * 分组(可无)： 基本信息     | 收入
         * 表头： 姓名,手机号  | 工资,劳务费
         * 数据：张三,13xxx| 1000.00,100.00
         */
        this.sheetName = sheet.getSheetName().trim();
        int readDataRows = 0;
        Map<Integer, String> colGroupMap = new HashMap<>();
        Map<Integer, String> colTitleMap = new HashMap<>();
        Map<Integer, ExcelColumnEntity> colColumnMap = new HashMap<>();

        Map<String, ExcelColumnEntity> pojoColumnMap = ExcelHelper.getColumnMap(pojoClass);
        Set<Integer> errorRows = new HashSet<>();

        ExcelColumnChecker columnChecker = new ExcelColumnChecker();
        for (int i = 1; i <= sheet.getLastRowNum() + 1; i++) {
            Row row = sheet.getRow(i - 1);
            if (null == row) {
                continue;
            }

            if (readDataRows > maxDataRows) {
                throw new ExcelIOException(String.format("最多只能读取%s行数据", maxDataRows));
            } else if (i < headerGroupRow) {
                continue;
            } else if (i == headerGroupRow) {
                colGroupMap = readHeaderGroupRow(row);
            } else if (i < headerRow) {
                continue;
            } else if (i == headerRow) {
                colTitleMap = readHeaderRow(row);
                if (!onlyReadHeader && null != headerMapping) {
                    headerMapping.verify(colTitleMap.values(), pojoColumnMap.keySet());
                }

                String groupName = null;
                for (Map.Entry<Integer, String> entry: colTitleMap.entrySet()) {
                    Integer col = entry.getKey();
                    if (colGroupMap.containsKey(col)) {
                        groupName = colGroupMap.get(col);
                    }

                    String title = entry.getValue();
                    String name = null != headerMapping ? headerMapping.title2name(title) : title;
                    if (null == name) {
                        name = title;
                    }

                    ExcelColumnEntity column = pojoColumnMap.get(name);
                    if (null == column) {
                        // 非ExcelColumn注解定义的字段
                        if (this.removeCustomColumn) {
                            continue;
                        }

                        column = ExcelHelper.createDefaultColumn(autoFilterOtherText);
                    } else {
                        pojoColumnMap.remove(name);
                    }

                    column.setColIndex(col);
                    column.setExcelTitle(title);
                    if (StringUtils.isBlank(column.getGroup()) && !StringUtils.isBlank(groupName)) {
                        // 未设置，使用Excel的值
                        column.setGroup(groupName);
                    }

                    excelHeader.put(column);
                    colColumnMap.put(col, column);
                    if (null != headerHandler) {
                        headerHandler.accept(column);
                    }
                }

                Set<String> missTitles = new HashSet<>();
                if (!pojoColumnMap.isEmpty()) {
                    // pojo定义，单Excel中缺少的列
                    int extCol = -1;
                    for (Map.Entry<String, ExcelColumnEntity> entry: pojoColumnMap.entrySet()) {
                        ExcelColumnEntity column = entry.getValue();
                        if (column.isRequired()) {
                            // 非必填的忽略
                            column.setExcelTitle(column.getName());
                            colColumnMap.put(extCol, column);
                            extCol--;
                            missTitles.add(column.getName());
                        }
                    }
                }
                if (onlyReadHeader) {
                    break;
                }

                if (needVerify) {
                    if (!missTitles.isEmpty()) {
                        throw new ExcelIOException(String.format("缺少必填字段'%s'的数据列", StringUtils.join(missTitles, "、")));
                    }

                    excelHeader.verify();
                }
            } else {
                // 数据读取
                ExcelRow excelRow = null;
                try {
                    excelRow = readRowData(row, excelHeader, colColumnMap);
                } catch (Exception e) {
                    errorRows.add(i);
                }

                if (null != excelRow && !excelRow.isEmptyRow()) {
                    readDataRows++;
                    excelRow.setRowNum(readDataRows);

                    columnChecker.uniqueCheck(excelRow, i);
                    excelResult.addRow(excelRow);

                    if (null != rowHandler) {
                        rowHandler.accept(excelRow);
                    }
                }
            }
        }

        if (excelHeader.isEmpty()) {
            throw new ExcelIOException(String.format("读取第%s行的表头数据为空", headerRow + 1));
        }

        if (!errorRows.isEmpty()) {
            throw new ExcelIOException(String.format("第%s行数据格式错误，请检查", StringUtils.join(errorRows, "、")));
        }

        if (excelResult.isEmpty()) {
            throw new ExcelIOException("读取的Excel文件数据内容为空");
        }

        return excelResult;
    }

    /**
     * 读取头部分组的数据
     * @param row
     * @return
     */
    private Map<Integer, String> readHeaderGroupRow(Row row) {
        Map<Integer, String> groupMap = new HashMap<>();
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (null != cell) {
                String title = ExcelHeaderMappingRule.parseText(cell.getStringCellValue());
                if (!StringUtils.isBlank(title)) {
                    groupMap.put(i, title);
                }
            }
        }

        return groupMap;
    }

    /**
     * 读取头部列
     * @param row
     * @return
     */
    private Map<Integer, String> readHeaderRow(Row row) {
        Map<Integer, String> columnMap = new HashMap<>();
        Set<String> uniqueTitles = new HashSet<>();
        Set<String> duplicateTitles = new HashSet<>();
        Set<Integer> errCols = new HashSet<>();

        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (null != cell) {
                String text = null;

                try {
                    Object value = readCellValue(cell, "表头");
                    if (null != value) {
                        text = value.toString();
                    }
                } catch (ValidationException e) {
                    errCols.add(i + 1);
                    continue;
                }

                String title = ExcelHeaderMappingRule.parseText(text);
                if (!StringUtils.isBlank(title)) {
                    columnMap.put(i, title);

                    if (uniqueTitles.contains(title)) {
                        duplicateTitles.add(title);
                    } else {
                        uniqueTitles.add(title);
                    }
                }
            }
        }

        if (!errCols.isEmpty()) {
            throw new ExcelIOException(String.format("第\"%s\"列表头格式错误", StringUtils.join(errCols, "、")));
        }

        if (!duplicateTitles.isEmpty()) {
            throw new ExcelIOException(String.format("第%s行的表头列\"%s\"重复", headerRow, StringUtils.join(duplicateTitles, "、")));
        }

        return columnMap;
    }

    private ExcelRow readRowData(Row row, ExcelHeader excelHeader, Map<Integer, ExcelColumnEntity> colColumnMap) {
        try {
            ExcelRow excelRow = pojoClass.newInstance();
            excelRow.setExcelHeader(excelHeader);
            for (Map.Entry<Integer, ExcelColumnEntity> entry: colColumnMap.entrySet()) {
                ExcelColumnEntity column = entry.getValue();
                if (null == column) {
                    continue;
                }

                Cell cell = null;
                if (entry.getKey() >= 0) {
                    cell = row.getCell(entry.getKey());
                }

                String excelTitle = column.getExcelTitle();
                Object value = null;
                if (null != cell) {
                    try {
                        value = readCellValue(cell, excelTitle);
                    } catch (ValidationException e) {
                        excelRow.pushError(e);
                        continue;
                    }
                }

                // 数据过滤和校验
                String newValue = verifyCellValue(value, column, excelRow);
                if (StringUtils.isBlank(newValue)) {
                    // 空值值不处理，也不存贮
                    continue;
                }

                Field field = column.getField();
                if (null != field) {
                    Class<?> fieldType = field.getType();
                    try {
                        field.setAccessible(true);
                        field.set(excelRow, ConvertUtils.basicTypeConvert(newValue, fieldType));
                    } catch (Exception e) {
                        excelRow.pushError(excelTitle, String.format("%s的格式错误", excelTitle), null);
                    }
                }

                excelRow.addSnapshot(excelTitle, newValue, field);
            }

            return excelRow;
        } catch (Exception e) {
            throw new ExcelIOException(e.getMessage());
        }
    }

    private Object readCellValue(Cell cell, String cellTitle) {
        CellType cellType = cell.getCellType();
        Object value = null;
        try {
            /**
             * 只支持Date和String两种类型
             */
            switch (cellType) {
                case STRING: {
                    value = cell.getRichStringCellValue() == null ? "" : cell.getRichStringCellValue().getString();
                    break;
                }
                case NUMERIC: {
                    value = readNumericCell(cell);
                    break;
                }
                case BOOLEAN: {
                    value = Boolean.toString(cell.getBooleanCellValue());
                    break;
                }
                case FORMULA: {
                    try {
                        // 数字结果
                        value = readNumericCell(cell);
                        /**
                        if (null != formulaEvaluator) {
                            value = StringUtils.trimDotZero(formulaEvaluator.evaluate(cell).getNumberValue());
                        } else {
                            cell.setCellType(CellType.STRING);
                            value = cell.getStringCellValue();
                        }
                         */
                    } catch (Exception var3) {
                        try {
                            value = cell.getRichStringCellValue() == null ? "" : cell.getRichStringCellValue().getString();
                        } catch (Exception var7) {
                            throw new ValidationException(String.format("获取'%s'的公式类型的数据失败", cellTitle));
                        }
                    }

                    break;
                }
                default: {
                    value = cell.getStringCellValue();
                }
            }
        } catch (Exception e) {
            throw new ValidationException(String.format("%s的格式错误", cellTitle));
        }

        return value;
    }

    private String verifyCellValue(Object value, ExcelColumnEntity column, ExcelRow excelRow) {
        String newValue = null == value ? null : String.valueOf(value);
        if (value instanceof Date) {
            SimpleDateFormat formatter = null;
            String dateFormat = column.getDateFormat();
            if (null != dateFormat) {
                newValue = new SimpleDateFormat(dateFormat).format((Date)value);
            } else {
                newValue = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date)value)
                        .replace(" 00:00:00", "");
            }
        } else {
            // 过滤
            newValue = column.doFilter(newValue);
            try {
                // 先判断是否日期文本，尝试转换日期，
                // 日期时间格式的内容，格式为标准格式
                // 避免纯数字格式的字符串(如：20210302)格式化后成为2021-03-02
                Object datetime = ConvertUtils.tryConvertStringToDate(newValue, column.isDateField());
                newValue = String.valueOf(datetime).replace('T', ' ');
            }catch (Exception e) {
                // 不处理
            }

            if (needVerify) {
                try {
                    // 校验
                    column.doValidate(newValue);
                } catch (ValidationException ve) {
                    excelRow.pushError(column.getExcelTitle(), ve.getMessage(), ve.getDetail());
                }
            }

            // 格式化
            newValue = column.doFormatter(newValue);
        }

        return newValue;
    }

    private Object readNumericCell(Cell cell) {
        Object value = null;
        String fs = cell.getCellStyle().getDataFormatString();
        double cellValue = cell.getNumericCellValue();
        if (DateUtil.isCellDateFormatted(cell) || fs.equals("reserved-0x1F")) {
            // 日期Date || 中文日期(2021年12月3日)的fs='reserved-0x1F'，
            Date dt = DateUtil.getJavaDate(cellValue);
            if (fs.equals("h:mm:ss")) {
                // 时分秒会读成带年月日的格式（年月日不正确）
                SimpleDateFormat fmt = new SimpleDateFormat("HH:mm:ss");
                value = fmt.format(dt);
            } else {
                value = dt;
            }
        } else {
            // 解决文本读成了科学计数
            NumberFormat nf = NumberFormat.getInstance();
            nf.setGroupingUsed(false);
            nf.setMaximumFractionDigits(maximumFractionDigits);
            value = StringUtils.trimDotZero(nf.format(cellValue));
        }

        return value;
    }
}
