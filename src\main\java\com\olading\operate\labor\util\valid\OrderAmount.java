package com.olading.operate.labor.util.valid;

import com.olading.boot.util.validate.Min;
import com.olading.boot.util.validate.Scale;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import jakarta.validation.constraints.NotNull;

import java.lang.annotation.*;

/**
 * 订单金额
 */
@Constraint(validatedBy = {})
@Documented
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Scale(2)
@Min("0.1")
@NotNull
public @interface OrderAmount {

    String message() default "金额格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
