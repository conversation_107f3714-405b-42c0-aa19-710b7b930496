package com.olading.operate.labor;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.Builder;
import lombok.Data;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.io.IOUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SimpleExample {

    private final static Charset charset = StandardCharsets.UTF_8;

    private final static ObjectMapper mapper = new ObjectMapper()
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
            .enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS)
            .registerModule(new SimpleModule() {{
                addSerializer(LocalDateTime.class, new JsonSerializer<LocalDateTime>() {
                    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                        gen.writeString(value.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                    }
                });
                addDeserializer(LocalDateTime.class, new JsonDeserializer<LocalDateTime>() {
                    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                        return LocalDateTime.parse(p.getValueAsString(), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                    }
                });
            }});


    private static OkHttpClient client;

    static {
        client = new OkHttpClient.Builder()
                .hostnameVerifier((hostname, sslSession) -> true)
                .connectTimeout(Duration.ofSeconds(5))
                .readTimeout(Duration.ofSeconds(10))
                .writeTimeout(Duration.ofSeconds(10))
                .build();
    }

    private final String address;
    private final String clientKey;
    private final String securityKey;

    public SimpleExample(String address, String clientKey, String securityKey) {
        this.address = address;
        this.clientKey = clientKey;
        this.securityKey = securityKey;
    }

    public <T, R> ApiResponse<R> request(String service, String version, String name, T data, Class<R> returnClass) throws IOException {

        ApiRequest<T> request = new ApiRequest<>();
        request.setName(name);
        request.setClientKey(clientKey);
        request.setTimestamp(LocalDateTime.now());
        request.setData(data);

        Map<String, String> form = encrypt(request);

        String url = address + service + "/" + version;
        return post(url, form, returnClass);
    }

    protected <T> Map<String, String> encrypt(ApiRequest<T> request) {
        try {
            Map<String, String> form = new HashMap<>();
            String req = mapper.writeValueAsString(request);
            String sign = hmacSha256(securityKey, req);
            form.put("req", req);
            form.put("sign", sign);
            return form;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    protected String decrypt(String body, String sign) {
        if (!hmacSha256(securityKey, body).equals(sign)) {
            throw new RuntimeException("签名不正确");
        }
        return body;
    }


    private static String hmacSha256(String key, String data) {
        byte[] sign = hmacSha256(key.getBytes(StandardCharsets.UTF_8), data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(sign);
    }

    private static byte[] hmacSha256(byte[] key, byte[] data) {
        try {
            SecretKey secretKey = new SecretKeySpec(key, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKey);
            return mac.doFinal(data);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new IllegalStateException("签名出现严重问题", e);
        }
    }

    private <R> ApiResponse<R> post(String u, Map<String, String> form, Class<R> returnClass) throws IOException {

        FormBody.Builder builder = new FormBody.Builder();
        form.forEach(builder::add);
        Request request = new Request.Builder()
                .url(u)
                .post(builder.build())
                .build();

        try (var resp = client.newCall(request).execute()) {
            try (InputStream input = resp.body().byteStream()) {
                String sign = resp.header("Api-Sign");
                String body = decrypt(IOUtils.toString(input, charset), sign);

                JavaType type = mapper.getTypeFactory().constructParametricType(ApiResponse.class, returnClass);
                return mapper.readValue(body, type);
            }
        }
    }

    @Data
    public static class ApiRequest<T> {
        private String name;
        private String clientKey;
        private LocalDateTime timestamp;
        private T data;
    }

    @Data
    public static class ApiResponse<T> {
        private String name;
        private String code;
        private String message;
        private T data;
    }

    @Builder
    @Data
    public static class QueryOrderRequest {
        private String orderNo;
    }

    @Builder
    @Data
    public static class CreateOrderRequest {

        private String orderNo;
        private String accountNo;
        private BigDecimal amount;
        private String payeeType;
        private String payeeAccountNo;
        private String payeeName;
        private String appId;
        private String remark;
        private String bizKey;
    }

    @Data
    public static class CreateOrderResponse {
        private String status;
    }

    @Data
    public static class QueryOrderResponse {

        private String status;
        private String accountNo;
        private BigDecimal amount;
        private String payeeType;
        private String payeeAccountNo;
        private String payeeName;
        private String appId;
        private String failReason;
        private String bizKey;
    }

    @Builder
    @Data
    public static class WechatSendRedPacketRequest {
        private String orderNo;
        private String accountNo;
        private BigDecimal amount;
        private String appId;
        private String payeeAccountNo;
        private String payeeCellphone;
        private String payeeIdNo;
        private String payeeName;
        private String remark;
        private String bizKey;
        private String sender;
        private String actName;
        private String wishing;
    }

    @Data
    public static class WechatSendRedPacketResponse {
    }

    @Builder
    @Data
    public static class WechatTransferBillOrderRequest {
        private String orderNo;
        private String accountNo;
        private BigDecimal amount;
        private String appId;
        private String payeeAccountNo;
        private String payeeCellphone;
        private String payeeIdNo;
        private String payeeName;
        private String remark;
        private String bizKey;
        private String sceneId;
        private List<Info> sceneInfo;

        @Builder
        @Data
        public static class Info {
            private String type;
            private String content;
        }

    }

    @Builder
    @Data
    public static class CancelWechatTransferBillOrderRequest {
        private String orderNo;
    }

    @Data
    public static class WechatTransferBillOrderResponse {
        private String state;
        private String failReason;
        private String packageInfo;
    }

}
