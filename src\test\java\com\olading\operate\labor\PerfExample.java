package com.olading.operate.labor;


import java.io.IOException;
import java.math.BigDecimal;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

public class PerfExample {

    /**
     * api地址
     */
    private static final String ADDRESS = "http://*************:8111/api/oapi/";
//    private static final String ADDRESS = "http://************:44701/api/oapi/";
//    private static final String ADDRESS = "http://127.0.0.1:8111/api/oapi/";

    private static String[] CLIENT_KEYS = new String[]{
            "8e760d23661344a7acb37bd44aa53498",
            "03ea9ed7c2804c598cf1adf84c489c21",
            "03ea9ed7c2804c598cf1adf84c489c22"
    };
    private static String[] SECURE_KEYS = new String[]{
            "efcbe757b0b242c2b0cff6690ab8472b",
            "03ea9ed7c2804c598cf1adf84c489c21",
            "03ea9ed7c2804c598cf1adf84c489c22"
    };
//    private static String[] ACCOUNTS = new String[]{
//            "4",
//            "6",
//            "9",
//    };

    private static String[] ACCOUNTS = new String[]{
            "14",
            "2",
            "3",
    };

    public static void run(int loop, int threads, Consumer<Integer> consumer) throws IOException, InterruptedException {

        ExecutorService executor = Executors.newFixedThreadPool(threads);

        long start = System.currentTimeMillis();
        AtomicInteger index = new AtomicInteger();
        AtomicInteger count = new AtomicInteger();
        AtomicLong begin = new AtomicLong(System.currentTimeMillis());
        for (int i = 0; i < loop; i++) {
            executor.execute(() -> {

                int j = index.getAndIncrement();
                consumer.accept(j);
                count.incrementAndGet();
                long now = System.currentTimeMillis();

                if (j % 100 == 0) {
                    var t = now - begin.getAndSet(System.currentTimeMillis());
                    var c = count.getAndSet(0);
                    System.out.println("tps=" + (1000.0 * c / t));

                }
            });
        }

        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.HOURS);

    }

    public static void createOrder() throws IOException, InterruptedException {

        int loop = 5;
        int threads = 1;

        long start = System.currentTimeMillis();

        run(loop, threads, (index) -> {

            var clientKey = CLIENT_KEYS[index % CLIENT_KEYS.length];
            var secureKey = SECURE_KEYS[index % SECURE_KEYS.length];
            var accountNo = ACCOUNTS[index % ACCOUNTS.length];

            clientKey = CLIENT_KEYS[0];
            secureKey = SECURE_KEYS[0];
            accountNo = ACCOUNTS[0];


            String payee = UUID.randomUUID().toString().replace("-", "");
            payee = "************";
            var request = SimpleExample.CreateOrderRequest.builder()
                    .orderNo(UUID.randomUUID().toString().replace("-", ""))
                    .appId("appid")
                    .accountNo(accountNo)
                    .amount(new BigDecimal("0.10"))
                    .payeeAccountNo(payee)
                    .payeeType("WECHAT_OPENID")
                    .build();

            try {
                var client = new SimpleExample(ADDRESS, clientKey, secureKey);
                var response = client.request("transfer", "v1", "CREATE_ORDER", request, SimpleExample.CreateOrderResponse.class);
                if (!response.getCode().equals("0")) {
                    System.out.println(response);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        System.out.println("结束, 耗时" + (System.currentTimeMillis() - start) + "ms");
    }


    public static void main(String[] args) throws IOException, InterruptedException {

        createOrder();
    }
}
