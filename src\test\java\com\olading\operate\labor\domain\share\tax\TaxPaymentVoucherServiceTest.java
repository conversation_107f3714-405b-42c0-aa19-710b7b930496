package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.service.TaxPaymentVoucherService;
import com.olading.operate.labor.domain.share.tax.vo.TaxPaymentVoucherVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.test.annotation.Rollback;

import static org.junit.jupiter.api.Assertions.*;

public class TaxPaymentVoucherServiceTest extends BaseTest {

    @Autowired
    private TaxPaymentVoucherService taxPaymentVoucherService;

    @Test
    @Rollback(false)
    public void testAddAndQueryTaxPaymentVoucher() {
        // 准备测试数据
        TaxPaymentVoucherVo vo = new TaxPaymentVoucherVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setFileIds("1,2,3");
        vo.setSupplierId(100l);

        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 测试新增
        TaxPaymentVoucherEntity entity = taxPaymentVoucherService.addTaxPaymentVoucher(tenantInfo, vo);
        assertNotNull(entity);
        assertNotNull(entity.getId());
        assertEquals(vo.getSupplierCorporationId(), entity.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), entity.getTaxPaymentPeriod());
        assertEquals(vo.getFileIds(), entity.getFileIds());

        // 测试查询
        TaxPaymentVoucherVo queryResult = taxPaymentVoucherService.queryTaxPaymentVoucher(entity.getId());
        assertNotNull(queryResult);
        assertEquals(entity.getId(), queryResult.getId());
        assertEquals(vo.getSupplierCorporationId(), queryResult.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), queryResult.getTaxPaymentPeriod());
        assertEquals(vo.getFileIds(), queryResult.getFileIds());
    }

    @Test
    public void testUpdateTaxPaymentVoucher() {
        // 先创建一个记录
        TaxPaymentVoucherVo vo = new TaxPaymentVoucherVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setFileIds("1,2,3");

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        TaxPaymentVoucherEntity entity = taxPaymentVoucherService.addTaxPaymentVoucher(tenantInfo, vo);

        // 更新记录
        vo.setId(entity.getId());
        vo.setTaxPaymentPeriod("2025-02");
        vo.setFileIds("4,5,6");

        TaxPaymentVoucherEntity updatedEntity = taxPaymentVoucherService.updateTaxPaymentVoucher(tenantInfo, vo);
        assertNotNull(updatedEntity);
        assertEquals("2025-02", updatedEntity.getTaxPaymentPeriod());
        assertEquals("4,5,6", updatedEntity.getFileIds());
    }

    @Test
    public void testDeleteTaxPaymentVoucher() {
        // 先创建一个记录
        TaxPaymentVoucherVo vo = new TaxPaymentVoucherVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setFileIds("1,2,3");

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        TaxPaymentVoucherEntity entity = taxPaymentVoucherService.addTaxPaymentVoucher(tenantInfo, vo);

        // 删除记录
        taxPaymentVoucherService.deleteTaxPaymentVoucher(tenantInfo, entity.getId());

        // 验证删除成功（应该抛出异常）
        assertThrows(IllegalStateException.class, () -> {
            taxPaymentVoucherService.queryTaxPaymentVoucher(entity.getId());
        });
    }
}
