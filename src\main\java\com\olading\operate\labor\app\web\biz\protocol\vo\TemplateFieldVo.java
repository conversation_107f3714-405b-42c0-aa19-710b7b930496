package com.olading.operate.labor.app.web.biz.protocol.vo;

import com.olading.operate.labor.domain.share.protocol.CorporationProtocolTempFiledEntity;
import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Schema(description="查询企业预设字段")
public class TemplateFieldVo {

    @Schema(description = "企业签署方预设字段")
    private List<Field> companyFields;

    @Schema(description = "个人签署方预设字段")
    private List<Field> personalFields;

    public static TemplateFieldVo from(List<CorporationProtocolTempFiledEntity> commonFields) {
        Map<EnumOperateType, List<Field>> fieldMap =commonFields.stream().map(Field::from)
                .collect(Collectors.groupingBy(Field::getSignatory));
        TemplateFieldVo response = new TemplateFieldVo();
        response.companyFields = fieldMap.getOrDefault(CorporationProtocolTempFiledEntity.COMPANY_SIGNATORY, Collections.emptyList());
        response.personalFields = fieldMap.getOrDefault(CorporationProtocolTempFiledEntity.PERSONAL_SIGNATORY, Collections.emptyList());
        return response;
    }

    @Data
    @Schema(description="合同管理-企业预设字段")
    static class Field {
        @Schema(description = "字段名")
        private String name;

        @Schema(description = "域默认值")
        private String value;

        @Schema(description = "关联项编码")
        private String relationCode;

//        @Schema(description = "关联项组编码")
//        private String relationGroup;
//
//        @Schema(description = "关联项名称")
//        private String relationName;

        @Schema(description = "签署方 SEAL-企业签署方 SIGN-个人签署方")
        private EnumOperateType signatory;

        public static Field fromDefault(CorporationProtocolTempFiledEntity commonField) {
            Field field = new Field();
            field.name = commonField.getFieldName();
            field.relationCode = commonField.getFieldCode();
//            field.relationGroup = commonField.getRelationGroup();
            field.signatory = commonField.getSignatory();
            return field;
        }

        public static Field from(CorporationProtocolTempFiledEntity companyField) {
            Field field = new Field();
            field.name = companyField.getFieldName();
            field.value = companyField.getFieldCode();
            field.relationCode = companyField.getFieldCode();
//            field.relationGroup = companyField.getRelationGroup();
//            field.relationName = companyField.getRelationName();
            field.signatory = companyField.getSignatory();
            return field;
        }
    }

}
