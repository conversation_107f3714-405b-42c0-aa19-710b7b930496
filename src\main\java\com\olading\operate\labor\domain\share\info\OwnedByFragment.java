package com.olading.operate.labor.domain.share.info;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import org.hibernate.annotations.Comment;

@Embeddable
public class OwnedByFragment {

    @Enumerated(EnumType.STRING)
    @Comment("实体类型")
    @Column(name = "owner_type", length = 20)
    private OwnerType ownerType;

    @Comment("实体ID")
    @Column(name = "owner_id", length = 50)
    private Long ownerId;

    public OwnedByFragment(OwnerType ownerType, long ownerId) {
        setOwner(ownerType, ownerId);
    }

    public OwnedByFragment(OwnerType ownerType, String ownerId) {
        setOwner(ownerType, ownerId);
    }

    protected OwnedByFragment() {
    }

    public OwnerType getOwnerType() {
        return this.ownerType;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    private void setOwner(OwnerType ownerType, String id) {
        this.setOwner(ownerType, Long.parseLong(id));
    }

    private void setOwner(OwnerType ownerType, long id) {
        this.ownerType = ownerType;
        this.ownerId = id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OwnedByFragment that = (OwnedByFragment) o;
        return ownerType == that.ownerType && java.util.Objects.equals(ownerId, that.ownerId);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(ownerType, ownerId);
    }
}
