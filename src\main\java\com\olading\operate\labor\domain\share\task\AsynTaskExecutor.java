package com.olading.operate.labor.domain.share.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.query.WebApiQuery;
import com.olading.operate.labor.app.query.WebApiQueryService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;

@Service
@Slf4j
public class AsynTaskExecutor {

    protected final WebApiQueryService webApiQueryService;
    protected final QueryService queryService;
    protected final FileManager fileManager;
    protected final TaskRepository taskRepository;

    protected AsynTaskExecutor(WebApiQueryService webApiQueryService, QueryService queryService, FileManager fileManager, TaskRepository taskRepository) {
        this.webApiQueryService = webApiQueryService;
        this.queryService = queryService;
        this.fileManager = fileManager;
        this.taskRepository = taskRepository;
    }


    public String executeTask(TaskEntity task) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        try {
            if (task.getOwnedBy().getOwnerType() == OwnerType.SUPPLIER) {

            } else if (task.getOwnedBy().getOwnerType() == OwnerType.CUSTOMER) {
                //return handleCustomerTask(task, objectMapper);
            }
        } catch (Exception e) {
            log.error("Error executing task", e);
            throw new RuntimeException("Error executing task", e);
        }
        return "fail";
    }












    private <T, F, R> void downloadExcel(TaskEntity task, WebApiQuery<T, F, R> query, QueryFilter<F> filter) {
        File tempFile = null;
        try {
            tempFile = File.createTempFile("temp", ".zip");
            // 写入数据到临时文件
            try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                query.excel(filter, outputStream, task.getFileName());
            } catch (IOException e) {
                log.error("Failed to write to temporary file", e);
                throw new RuntimeException("Failed to generate Excel file", e);
            }
            // 保存文件并处理输入流
            try (InputStream inputStream = new FileInputStream(tempFile)) {
                String id = fileManager.save(task.getFileName(), inputStream, LocalDateTime.now().plusYears(100), task.getOwnedBy().getOwnerType(), String.valueOf(task.getOwnedBy().getOwnerId()));
                taskRepository.markAsAttachments(task.getId(), id);
            } catch (IOException e) {
                log.error("Failed to read temporary file for upload", e);
                throw new RuntimeException("Failed to upload generated file", e);
            }
        } catch (IOException e) {
            // 处理创建临时文件的异常
            log.error("Failed to create temporary file", e);
            throw new RuntimeException("Failed to prepare for file download", e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: " + tempFile.getAbsolutePath());
                }
            }
        }
    }
}
