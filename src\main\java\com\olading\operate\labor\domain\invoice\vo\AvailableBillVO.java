package com.olading.operate.labor.domain.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "可开票账单信息")
public class AvailableBillVO {
    
    @Schema(description = "账单ID")
    private Long billId;
    
    @Schema(description = "账单编号")
    private String billNo;
    
    @Schema(description = "账单月份")
    private LocalDate billMonth;
    
    @Schema(description = "客户名称")
    private String customerName;
    
    @Schema(description = "开票总金额")
    private BigDecimal totalInvoiceAmount;
    
    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;
    
    @Schema(description = "可开票金额")
    private BigDecimal availableAmount;
    
    @Schema(description = "可用的发票类目")
    private List<String> availableCategories;
}