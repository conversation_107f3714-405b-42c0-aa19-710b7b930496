package com.olading.operate.labor.app.web.biz.labor;

import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.service.SupplierLaborService;
import com.olading.operate.labor.util.crypto.HashPassword;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "个人端-人员")
@RestController
@RequestMapping("/api/personal/labor")
@RequiredArgsConstructor
@Slf4j
public class H5SupplierLaborController extends BusinessController {

    private final SupplierLaborService supplierLaborService;

    @Operation(summary = "OCR识别确认", description = "OCR识别身份证并验证与期望信息是否匹配")
    @PostMapping("/ocrVerify")
    public WebApiResponse<Boolean> ocrIdentifyAndVerify(@Valid @RequestBody H5SupplierLaborController.H5OcrVerifyRequest request) {
        Long supplierId = currentSupplierId();
        log.info("OCR识别验证请求: supplierId={}, expectedName={}, expectedIdNo={}",
                supplierId, request.getExpectedName(), request.getExpectedIdNo());
        if(!HashPassword.hashPassword(request.getExpectedIdNo()+request.getExpectedName()).equals(request.getFlow())){
            throw ApiException.paramError("信息验证不正确");
        }
        supplierLaborService.userVerifySuccess(supplierId, currentUser(), request.getExpectedIdNo(), request.getExpectedName());
        return WebApiResponse.success(true);
    }


    @Data
    @Schema(description = "个人端-OCR识别验证请求")
    public static class H5OcrVerifyRequest {

        @NotBlank(message = "个人证件照片面图片不能为空")
        @Schema(description = "个人证件照片面图片id", required = true)
        private String personalIdImg;

        @NotBlank(message = "国徽面图片不能为空")
        @Schema(description = "国徽面图片id",required = true)
        private String nationalEmblemImg;

        @NotBlank(message = "期望姓名不能为空")
        @Schema(description = "期望的姓名", required = true)
        private String expectedName;

        @NotBlank(message = "期望身份证号不能为空")
        @Schema(description = "期望的身份证号", required = true)
        private String expectedIdNo;

        @NotBlank(message = "流水号不能为空")
        @Schema(description = "流水号", required = true)
        private String flow;
    }
}