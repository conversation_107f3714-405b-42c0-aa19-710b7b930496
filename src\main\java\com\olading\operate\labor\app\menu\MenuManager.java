package com.olading.operate.labor.app.menu;

import com.olading.boot.util.Json;
import com.olading.boot.util.collection.Tree;
import com.olading.operate.labor.util.Utils;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class MenuManager {

    public static void main(String[] args) {

        new MenuManager().loadMenu("supplier");

    }

    public Menu load(Set<String> authorities, String path) {
        Menu menu = loadMenu(path);
        Tree.Node<Menu> tree = new Tree().from(menu, Menu::getChildren);

        tree.removeIf(o -> {

            // 无权限的需要删除
            if (o.getData().getAuthority() != null && !authorities.contains(o.getData().getAuthority())) {
                return true;
            }
            // 纯菜单需要删除
            if (o.getData().getAuthority() == null && o.isLeaf()) {
                return true;
            }

            return false;
        });

        return tree.map((n, children) -> {
            n.getData().setChildren(children);
            n.getData().setAuthority(null);
            return n.getData();
        });
    }

    public Menu load() {
        return loadMenu("supplier");
    }

    private Menu loadMenu(String name) {
        String s = Utils.loadResource("/menu/" + name + ".json");
        return Json.fromJson(s, Menu.class);
    }
}
