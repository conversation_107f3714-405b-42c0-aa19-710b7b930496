package com.olading.operate.labor.app.web.biz.submission;

import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.domain.query.InfoSubmissionLaborQuery;
import com.olading.operate.labor.domain.service.InfoSubmissionLaborService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.file.FileInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionLaborEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborVo;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.DefaultResourceLoader;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Tag(name = "人员信息报送相关接口")
@RestController
@RequestMapping("/api/supplier/infolabor")
@RequiredArgsConstructor
@Slf4j
public class InfoSubmissionLaborController extends BusinessController {

    private final InfoSubmissionLaborService infoSubmissionLaborService;
    private final QueryService queryService;
    private final FileManager fileManager;

    private static final String INFO_SUBMISSION_LABOR_TEMPLATE = "/template/人员信息报送.xlsx";

    @Operation(summary = "人员信息报送记录列表")
    @PostMapping("/list")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.corporationIds")
    })
    public WebApiQueryResponse<InfoSubmissionLaborVo> listInfoSubmissionLabor(@RequestBody QueryFilter<WebQueryInfoSubmissionLaborFilters> request) {
        QueryFilter<InfoSubmissionLaborQuery.Filters> filter = request.convert(WebQueryInfoSubmissionLaborFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        //数据权限控制
        filter.getFilters().setCorporationIds(currentDataScope().get(OwnerType.CORPORATION));
        DataSet<InfoSubmissionLaborVo> ds = queryService.queryInfoSubmissionLabor(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }





    @Operation(summary = "更新人员信息报送记录")
    @PostMapping("/update")
    public WebApiResponse<Void> updateInfoSubmissionLabor(@Valid @RequestBody InfoSubmissionLaborParam param) {
        InfoSubmissionLaborVo vo = new InfoSubmissionLaborVo();
        BeanUtils.copyProperties(param, vo);

        infoSubmissionLaborService.updateInfoSubmissionLabor(currentTenant(), vo);
        return WebApiResponse.success();
    }

    @Operation(summary = "人员信息报送记录详情")
    @PostMapping("/query")
    public WebApiResponse<InfoSubmissionLaborVo> queryInfoSubmissionLabor(@Valid @RequestBody IdRequest request) {
        InfoSubmissionLaborVo vo = infoSubmissionLaborService.queryInfoSubmissionLabor(request.getId());
        return WebApiResponse.success(vo);
    }

    @Operation(summary = "删除人员信息报送记录")
    @PostMapping("/delete")
    public WebApiResponse<Void> deleteInfoSubmissionLabor(@Valid @RequestBody IdRequest request) {
        infoSubmissionLaborService.deleteInfoSubmissionLabor(currentTenant(), request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "人员信息报送文件下载")
    @PostMapping(value = "/download")
    public void downloadInfoSubmissionLabor(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody SupplierCorporationIdRequest param) {
        try {
            // 根据作业主体ID查询人员信息报送记录列表
            List<InfoSubmissionLaborEntity> entities = infoSubmissionLaborService.queryInfoSubmissionLaborBySupplierCorporationId(param.getSupplierCorporationId());

            // 读取模板文件
//            Workbook workbook = new HSSFWorkbook(new DefaultResourceLoader().getResource(INFO_SUBMISSION_LABOR_TEMPLATE).getInputStream());
            Workbook workbook = new XSSFWorkbook(new DefaultResourceLoader().getResource(INFO_SUBMISSION_LABOR_TEMPLATE).getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 填充数据到模板
            fillTemplateData(sheet, entities);

            // 设置文件名
//            String fileName = String.format("人员信息报送_%s.xls", param.getSupplierCorporationId());
            String fileName = String.format("人员信息报送_%s.xlsx", param.getSupplierCorporationId());

            // 设置响应头
            response.setCharacterEncoding("UTF-8");
//            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("下载人员信息报送文件成功，作业主体ID: {}, 记录数量: {}", param.getSupplierCorporationId(), entities.size());

        } catch (Exception e) {
            log.error("下载人员信息报送文件失败", e);
            throw new RuntimeException("下载文件失败: " + e.getMessage());
        }
    }

    @Data
    public static class WebQueryInfoSubmissionLaborFilters {
        @Schema(description = "人员信息报送记录ID")
        private Long id;

        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "报送状态")
        private String reportStatus;

        @Schema(description = "姓名")
        private String laborName;

        @Schema(description = "身份证号")
        private String idCard;

        @Schema(description = "创建时间-起")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建时间-止")
        private LocalDateTime createTimeEnd;

        @Schema(description = "作业主体ID列表")
        private Set<Long> corporationIds;

        public InfoSubmissionLaborQuery.Filters convert() {
            return Beans.copyBean(this, InfoSubmissionLaborQuery.Filters.class);
        }
    }

    @Data
    public static class InfoSubmissionLaborParam {
        @Schema(description = "人员信息报送记录ID")
        private Long id;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "报送状态")
        private String reportStatus;

        @Schema(description = "是否已取得登记证照")
        private String registrationLicenseObtained;

        @Schema(description = "名称（姓名）")
        private String name;

        @Schema(description = "统一社会信用代码（纳税人识别号）")
        private String unifiedSocialCreditCode;

        @Schema(description = "专业服务机构标识")
        private String professionalServiceAgencyFlag;

        @Schema(description = "姓名")
        private String laborName;

        @Schema(description = "证件类型")
        private String certificateType;

        @Schema(description = "身份证号")
        private String idCard;

        @Schema(description = "国家或地区")
        private String householdCity;

        @Schema(description = "是否存在免于报送收入信息情形")
        private String incomeReportingExemptionFlag;

        @Schema(description = "免报类型")
        private String exemptionType;

        @Schema(description = "地址")
        private String householdAddress;

        @Schema(description = "店铺（用户）名称")
        private String storeName;

        @Schema(description = "店铺（用户）唯一标识码")
        private String storeUniqueCode;

        @Schema(description = "网址链接（选填）")
        private String websiteUrl;

        @Schema(description = "开户银行/非银行支付机构")
        private String cardBank;

        @Schema(description = "账户名称")
        private String accountName;

        @Schema(description = "银行账号/支付账户")
        private String bankCard;

        @Schema(description = "联系人姓名")
        private String contactName;

        @Schema(description = "联系电话")
        private String contactPhone;

        @Schema(description = "经营开始时间")
        private String startDate;

        @Schema(description = "经营结束时间")
        private String endDate;

        @Schema(description = "信息状态标识")
        private String infoStatusFlag;
    }

    @Data
    public static class IdRequest {
        @NotNull(message = "ID不能为空")
        @Schema(description = "记录ID")
        private Long id;
    }

    @Data
    public static class SupplierCorporationIdRequest {
        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;
    }



    /**
     * 填充模板数据
     */
    private void fillTemplateData(Sheet sheet, List<InfoSubmissionLaborEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        // 从第三行开始写入数据（前两行是表头）
        int rowIndex = 2;
        int sequenceNumber = 1;

        for (InfoSubmissionLaborEntity entity : entities) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            // A列：序号
            setCellValue(row, 0, String.valueOf(sequenceNumber));

            // F列：姓名
            setCellValue(row, 5, entity.getLaborName());

            // G列：证件类型
            setCellValue(row, 6, getCertificateTypeName(entity.getCertificateType()));

            // H列：身份证号
            setCellValue(row, 7, entity.getIdCard());

            // I列：户籍城市
            setCellValue(row, 8, entity.getHouseholdCity());

            // L列：户籍地址
            setCellValue(row, 11, entity.getHouseholdAddress());

            // M列：店铺名称
            setCellValue(row, 12, entity.getStoreName());

            // N列：店铺唯一标识码
            setCellValue(row, 13, entity.getStoreUniqueCode());

            // P列：开户银行
            setCellValue(row, 15, entity.getCardBank());

            // R列：银行账号
            setCellValue(row, 17, entity.getBankCard());

            // S列：联系人姓名
            setCellValue(row, 18, entity.getContactName());

            // T列：联系电话
            setCellValue(row, 19, entity.getContactPhone());

            // U列：经营开始时间
            setCellValue(row, 20, entity.getStartDate());

            // V列：经营结束时间
            setCellValue(row, 21, entity.getEndDate());

            rowIndex++;
            sequenceNumber++;
        }
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Row row, int columnIndex, String value) {
        if (row.getCell(columnIndex) == null) {
            row.createCell(columnIndex);
        }
        row.getCell(columnIndex).setCellValue(value != null ? value : "");
    }

    /**
     * 获取证件类型名称
     */
    private String getCertificateTypeName(String certificateType) {
        if (certificateType == null) {
            return "";
        }
        try {
            return CertificateTypeEnum.valueOf(certificateType).getName();
        } catch (IllegalArgumentException e) {
            return certificateType;
        }
    }

    /**
     * 根据文件ID下载文件
     */
    private void downloadFileById(String fileId, String fileName, HttpServletResponse response) {
        try (OutputStream output = response.getOutputStream()) {
            FileInfo fileInfo = fileManager.getInfo(fileId);

            response.setContentType("application/octet-stream");
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);

            fileManager.load(fileId, output);
        } catch (IOException e) {
            throw new IllegalStateException("下载文件失败: " + fileId, e);
        }
    }
}
