package com.olading.operate.labor.app.web.biz.submission;

import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.domain.query.InfoSubmissionLaborIncomeQuery;
import com.olading.operate.labor.domain.service.InfoSubmissionLaborIncomeService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.file.FileInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionLaborIncomeEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborIncomeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Set;

@Tag(name = "人员收入信息报送相关接口")
@RestController
@RequestMapping("/api/supplier/infoincome")
@RequiredArgsConstructor
@Slf4j
public class InfoSubmissionLaborIncomeController extends BusinessController {

    private final InfoSubmissionLaborIncomeService infoSubmissionLaborIncomeService;
    private final QueryService queryService;
    private final FileManager fileManager;

    @Operation(summary = "人员收入信息报送记录列表")
    @PostMapping("/list")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.corporationIds")
    })
    public WebApiQueryResponse<InfoSubmissionLaborIncomeVo> listInfoSubmissionLaborIncome(@RequestBody QueryFilter<WebQueryInfoSubmissionLaborIncomeFilters> request) {
        QueryFilter<InfoSubmissionLaborIncomeQuery.Filters> filter = request.convert(WebQueryInfoSubmissionLaborIncomeFilters::convert);
        filter.sort("id", Direction.DESCENDING);

        //数据权限控制
        filter.getFilters().setCorporationIds(currentDataScope().get(OwnerType.CORPORATION));
        DataSet<InfoSubmissionLaborIncomeVo> ds = queryService.queryInfoSubmissionLaborIncome(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "新增人员收入信息报送记录")
    @PostMapping("/add")
    public WebApiResponse<Long> addInfoSubmissionLaborIncome(@Valid @RequestBody InfoSubmissionLaborIncomeParam param) {
        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        BeanUtils.copyProperties(param, vo);
        vo.setSupplierId(currentSupplierId());
        vo.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATING.name());

        InfoSubmissionLaborIncomeEntity entity = infoSubmissionLaborIncomeService.addInfoSubmissionLaborIncome(currentTenant(), vo);
        

        return WebApiResponse.success(entity.getId());
    }

    @Operation(summary = "更新人员收入信息报送记录")
    @PostMapping("/update")
    public WebApiResponse<Void> updateInfoSubmissionLaborIncome(@Valid @RequestBody InfoSubmissionLaborIncomeParam param) {
        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        BeanUtils.copyProperties(param, vo);

        infoSubmissionLaborIncomeService.updateInfoSubmissionLaborIncome(currentTenant(), vo);
        return WebApiResponse.success();
    }

    @Operation(summary = "人员收入信息报送记录详情")
    @PostMapping("/query")
    public WebApiResponse<InfoSubmissionLaborIncomeVo> queryInfoSubmissionLaborIncome(@Valid @RequestBody IdRequest request) {
        InfoSubmissionLaborIncomeVo vo = infoSubmissionLaborIncomeService.queryInfoSubmissionLaborIncome(request.getId());
        return WebApiResponse.success(vo);
    }

    @Operation(summary = "删除人员收入信息报送记录")
    @PostMapping("/delete")
    public WebApiResponse<Void> deleteInfoSubmissionLaborIncome(@Valid @RequestBody IdRequest request) {
        infoSubmissionLaborIncomeService.deleteInfoSubmissionLaborIncome(currentTenant(), request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "人员收入信息报送文件下载")
    @PostMapping(value = "/download")
    public void downloadInfoSubmissionLaborIncome(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody IdRequest param) {
        try {
            // 获取人员收入信息报送记录详情
            InfoSubmissionLaborIncomeVo vo = infoSubmissionLaborIncomeService.queryInfoSubmissionLaborIncome(param.getId());
            
            if (vo.getFileId() == null || vo.getFileId().isEmpty()) {
                throw new RuntimeException("文件尚未生成或文件ID为空");
            }
            
            // 根据fileId下载文件流
//            downloadFileById(vo.getFileId(), "人员收入信息报送_" + param.getId() + ".txt", response);
            downloadFileById(vo.getFileId(), "人员收入信息报送_" + param.getId() + ".xlsx", response);
            
            log.info("下载人员收入信息报送文件，记录ID: {}, 文件ID: {}", param.getId(), vo.getFileId());
            
        } catch (Exception e) {
            log.error("下载人员收入信息报送文件失败", e);
            throw new RuntimeException("下载文件失败: " + e.getMessage());
        }
    }

    @Data
    public static class WebQueryInfoSubmissionLaborIncomeFilters {
        @Schema(description = "人员收入信息报送记录ID")
        private Long id;

        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "开始日期")
        private String startDate;

        @Schema(description = "结束日期")
        private String endDate;

        @Schema(description = "生成状态")
        private String status;

        @Schema(description = "创建时间-起")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建时间-止")
        private LocalDateTime createTimeEnd;

        @Schema(description = "作业主体ID列表")
        private Set<Long> corporationIds;


        public InfoSubmissionLaborIncomeQuery.Filters convert() {
            return Beans.copyBean(this, InfoSubmissionLaborIncomeQuery.Filters.class);
        }
    }

    @Data
    public static class InfoSubmissionLaborIncomeParam {
        @Schema(description = "人员收入信息报送记录ID")
        private Long id;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "开始日期")
        private String startDate;

        @Schema(description = "结束日期")
        private String endDate;

        @Schema(description = "生成状态")
        private String status;

        @Schema(description = "附件ID")
        private String fileId;
    }

    @Data
    public static class IdRequest {
        @NotNull(message = "ID不能为空")
        @Schema(description = "记录ID")
        private Long id;
    }

    /**
     * 根据文件ID下载文件
     */
    private void downloadFileById(String fileId, String fileName, HttpServletResponse response) {
        try (OutputStream output = response.getOutputStream()) {
            FileInfo fileInfo = fileManager.getInfo(fileId);

            response.setContentType("application/octet-stream");
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);

            fileManager.load(fileId, output);
        } catch (IOException e) {
            throw new IllegalStateException("下载文件失败: " + fileId, e);
        }
    }
}
