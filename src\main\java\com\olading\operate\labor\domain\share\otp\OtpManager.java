package com.olading.operate.labor.domain.share.otp;

import com.olading.boot.util.Json;
import com.olading.boot.util.RandomUtils;
import com.olading.boot.util.crypto.Aes;
import com.olading.operate.labor.AppProperties;
import lombok.Data;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

@Component
public class OtpManager {

    /**
     * 有效时间，单位秒
     */
    public static final long TTL = 300;
    private final static String CACHE_NAME_OTP = OtpManager.class.getName();
    private final AppProperties properties;
    private final NSmsProvider provider;
    private final Cache cache;

    public OtpManager(AppProperties properties, NSmsProvider provider, CacheManager cacheManager) {
        this.properties = properties;
        this.provider = provider;
        this.cache = cacheManager.getCache(CACHE_NAME_OTP);
    }

    public Otp send(String receiver, String businessId, String sign) {

        OtpInfo info = new OtpInfo();
        info.setReceiver(receiver);
        info.setChallenge(randomCode());
        info.setBusinessId(businessId);

        String token = Aes.encrypt(Json.toJson(info), properties.getMasterKey());

        provider.sendOtp(receiver, info.getChallenge(),businessId, sign);

        cache.put(token, 1);

        return new Otp(token, receiver);
    }

    public boolean verify(String receiver, String token, String challenge) {

        if (properties.isMock()) {
            return true;
        }

        return verify(receiver, token, challenge, null);
    }

    public boolean verify(String receiver, String token, String challenge, String businessId) {

        if (properties.isMock()) {
            return true;
        }

        String json = Aes.decrypt(token, properties.getMasterKey());
        OtpInfo info = Json.fromJson(json, OtpInfo.class);

        // 检查是否过期
        if (info.getTimestamp() + TTL * 1000 < System.currentTimeMillis()) {
            return false;
        }
        if (!info.getChallenge().equals(challenge) && info.getReceiver().equals(receiver)) {
            return false;
        }

        if (cache.get(token) == null) {
            return false;
        }

        if (businessId != null && !businessId.equals(info.getBusinessId())){
            return false;
        }
        ;
        cache.evict(token);
        return true;
    }

    private String randomCode() {
        return RandomUtils.string("0123456789", 6);
    }

    @Data
    static class OtpInfo {

        private String receiver;

        private String challenge;

        private String businessId;

        private long timestamp = System.currentTimeMillis();
    }
}
