package com.olading.operate.labor.domain.corporation;

import cn.hutool.core.collection.CollectionUtil;
import com.olading.operate.labor.app.web.biz.corporation.CorporationController;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.app.web.biz.supplier.SupplierController;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoData;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class CorporationData {

    private Long id;

    private Long supplierId;

    @Schema(description = "公司名称")
    private String name;


    @Schema(description = "开户行")
    private String bankName;

    @Schema(description = "开户账号")
    private String bankAccount;

    @Schema(description = "企业电话")
    private String companyTel;

    @Schema(description = "社会信用代码")
    private String socialCreditCode;

    @Schema(description = "平台UUID")
    private String taxUuid;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新时间")
    private LocalDateTime modifyTime;

    private boolean disabled = false;

    private String defaultPayChannel;

    private EnterpriseInfoData info = new EnterpriseInfoData();

    private CorporationConfigData configData = new CorporationConfigData();

    private List<CorporationPayChannelData> payChannelDataList = new ArrayList<>();

    private List<RoleData> RoleData = new ArrayList<>();


    public CorporationData() {
    }

    public CorporationData(SupplierCorporationEntity entity,
                           EnterpriseInfoEntity infoEntity,
                           CorporationConfigEntity configEntity,
                           List<CorporationPayChannelData> payChannelList,
                           List<RoleData> roleDataList) {
        this.socialCreditCode = entity.getSocialCreditCode();
        this.id = entity.getId();
        this.name = entity.getName();
        this.disabled = entity.getDisabled();
        this.bankAccount = entity.getBankAccount();
        this.supplierId = entity.getSupplierId();
        this.bankName = entity.getBankName();
        this.taxUuid = entity.getTaxUuid();
        if(infoEntity != null){
            this.info.setSocialCreditCode(infoEntity.getSocialCreditCode());
            this.info.setAttachments(infoEntity.getAttachments());
            this.info.setName(infoEntity.getName());
            this.info.setContacts(infoEntity.getContacts());
            this.info.setRemark(infoEntity.getRemark());
            this.info.setContactPhone(infoEntity.getContactPhone());
            this.info.setBusinessLicenseImage(infoEntity.getBusinessLicenseImage());
            this.info.setRegisterAddress(infoEntity.getRegisteredAddress());
            this.info.setCertificateType(infoEntity.getCertificateType());
            this.info.setCertificateNo(infoEntity.getCertificateNo());
            this.info.setCertificateFrontImage(infoEntity.getCertificateFrontImage());
            this.info.setCertificateBackImage(infoEntity.getCertificateBackImage());
            this.info.setId(infoEntity.getId());
            this.info.setRepresentativeName(infoEntity.getRepresentativeName());
        }

        if(configEntity != null){
            this.configData.setSurtaxData(configEntity.getSurtaxData());
            this.configData.setInvoiceCategory(configEntity.getInvoiceCategory());
            this.configData.setId(configEntity.getId());
            this.configData.setMinAgeLimit(configEntity.getMinAgeLimit());
            this.configData.setMaxAgeLimit(configEntity.getMaxAgeLimit());
            this.configData.setVatStart(configEntity.getVatStart());
            this.configData.setVatRate(configEntity.getVatRate());
            this.configData.setId(configEntity.getId());
        }
        this.payChannelDataList = payChannelList;
        if(CollectionUtil.isNotEmpty(roleDataList)){
            this.RoleData = roleDataList;
        }

    }

    public static CorporationData of(CorporationController.SupplierCorporationDetailVo corporationDetailVo) {
        CorporationData data = new CorporationData();
        data.setName(corporationDetailVo.getName());
        data.setTaxUuid(corporationDetailVo.getTaxUuid());
        data.setBankName(corporationDetailVo.getBankName());
        data.setBankAccount(corporationDetailVo.getBankAccount());
        data.setCompanyTel(corporationDetailVo.getCompanyTel());
        data.setSocialCreditCode(corporationDetailVo.getSocialCreditCode());
        data.setId(corporationDetailVo.getId());
        data.setInfo(new EnterpriseInfoData());
        data.getInfo().setName(corporationDetailVo.getName());
        data.getInfo().setContacts(corporationDetailVo.getContactName());
        data.getInfo().setContactPhone(corporationDetailVo.getContactMobile());
        data.getInfo().setSocialCreditCode(corporationDetailVo.getSocialCreditCode());
        data.getInfo().setRegisterAddress(corporationDetailVo.getRegisterAddress());
        data.getInfo().setBusinessLicenseImage(corporationDetailVo.getBusinessLicenseImage());
        data.getInfo().setCertificateFrontImage(corporationDetailVo.getCertificateFrontImage());
        data.getInfo().setCertificateBackImage(corporationDetailVo.getCertificateBackImage());
        data.getInfo().setCertificateType(CertificateTypeEnum.ID_CARD);
        data.getInfo().setCertificateNo(corporationDetailVo.getCertificateNo());
        data.getInfo().setRepresentativeName(corporationDetailVo.getRepresentativeName());
        if(CollectionUtil.isNotEmpty(corporationDetailVo.getRoleIds())){
            data.setRoleData(corporationDetailVo.getRoleIds().stream().map(id -> {
                RoleData roleData = new RoleData();
                roleData.setId(id);
                return roleData;
            }).collect(Collectors.toList()));
        }
        return data;
    }

    public static CorporationData of(CorporationController.CorporationBusinessVo businessVo) {
        CorporationData data = new CorporationData();
        data.setId(businessVo.getCorporationId());
        data.setConfigData(businessVo.getConfigData());
        data.setDefaultPayChannel(businessVo.getDefaultPayChannel());
        return data;
    }
}
