package com.olading.operate.labor.domain.share.authority;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.Comment;

/**
 * 角色成员
 */
@Table(name = "t_role_member")
@Entity
public class RoleMemberEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Comment("角色ID")
    @Column(name = "role_id", nullable = false)
    private Long roleId;

    @Comment("主体ID")
    @Column(name = "subject_id")
    private Long subjectId;

    public RoleMemberEntity(Long roleId, Long subjectId) {
        this.roleId = roleId;
        this.subjectId = subjectId;
    }

    protected RoleMemberEntity() {
    }

    public Long getId() {
        return id;
    }

    public Long getRoleId() {
        return roleId;
    }

    public Long getSubjectId() {
        return subjectId;
    }
}
