package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.bill.BillManagementFeeDetailEntity;
import com.olading.operate.labor.domain.bill.QBillCategoryEntity;
import com.olading.operate.labor.domain.bill.QBillManagementFeeDetailEntity;
import com.olading.operate.labor.domain.bill.QBillMasterEntity;
import com.olading.operate.labor.domain.bill.vo.BillManagementFeeDetailVO;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.util.Set;

/**
 * 账单管理费明细查询
 */
public class BillManagementFeeDetailQuery implements EntityQuery<QueryFilter<BillManagementFeeDetailQuery.Filters>, BillManagementFeeDetailVO> {

    private final QBillManagementFeeDetailEntity t1 = QBillManagementFeeDetailEntity.billManagementFeeDetailEntity;
    private final QBillMasterEntity t3 = QBillMasterEntity.billMasterEntity;
    private final QBillCategoryEntity t2 = QBillCategoryEntity.billCategoryEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.eq(filters.getFilters().getId()));
        }

        if (filters.getFilters().getBillMasterId() != null) {
            criteria.and(t1.billMasterId.eq(filters.getFilters().getBillMasterId()));
        }

        if (filters.getFilters().getBillCategoryId() != null) {
            criteria.and(t1.billCategoryId.eq(filters.getFilters().getBillCategoryId()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getName())) {
            criteria.and(t1.laborName.like(JpaUtils.fullLike(filters.getFilters().getName())));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getIdCard())) {
            criteria.and(t1.idCard.like(JpaUtils.fullLike(filters.getFilters().getIdCard())));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getFeeItem())) {
            criteria.and(t1.feeItem.like(JpaUtils.fullLike(filters.getFilters().getFeeItem())));
        }

        if (filters.getFilters().getBillMonth() != null) {
            criteria.and(t1.billMonth.eq(filters.getFilters().getBillMonth()));
        }

        if (filters.getFilters().getBillMonthStart() != null) {
            criteria.and(t1.billMonth.goe(filters.getFilters().getBillMonthStart()));
        }

        if (filters.getFilters().getBillMonthEnd() != null) {
            criteria.and(t1.billMonth.loe(filters.getFilters().getBillMonthEnd()));
        }

        if (filters.getFilters().getContractIds() != null) {
            criteria.and(t3.contractId.in(filters.getFilters().getContractIds()));
        }

        // 默认只查询未删除的记录
        criteria.and(t1.deleted.eq(false));

        query.select(t1,t2,t3)
                .from(t1).leftJoin(t2).on(t1.billCategoryId.eq(t2.id))
                .leftJoin(t3).on(t1.billMasterId.eq(t3.id));
        query.where(criteria);
    }

    @Override
    public BillManagementFeeDetailVO transform(Object v) {
        Tuple tuple = (Tuple) v;
        BillManagementFeeDetailEntity detail = tuple.get(t1);

        BillManagementFeeDetailVO vo = new BillManagementFeeDetailVO();
        BeanUtils.copyProperties(detail, vo);
        vo.setCalculationRule(detail.getCalculationRule().getName());

        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        if ("createTime".equals(column)) {
            return t1.createTime;
        }
        if ("laborName".equals(column)) {
            return t1.laborName;
        }
        return null;
    }

    @Data
    public static class Filters {
        private Long id;
        private Long billMasterId;
        private Long billCategoryId;
        private String name;
        private String idCard;
        private String feeItem;
        private LocalDate billMonth;
        private LocalDate billMonthStart;
        private LocalDate billMonthEnd;
        private Set<Long> contractIds;
    }
}