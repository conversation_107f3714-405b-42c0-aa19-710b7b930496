package com.olading.operate.labor.domain.share.identity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * OCR身份证识别结果
 */
@Data
@Schema(description = "OCR身份证识别结果")
public class OcrIdentifyResult {
    
    @Schema(description = "是否识别成功")
    private boolean success;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    @Schema(description = "姓名")
    private String name;
    
    @Schema(description = "身份证号")
    private String idNo;
    
    @Schema(description = "性别")
    private String gender;
    
    @Schema(description = "民族")
    private String nation;
    
    @Schema(description = "出生日期")
    private String birth;
    
    @Schema(description = "地址")
    private String address;
    
    @Schema(description = "签发机关")
    private String authority;
    
    @Schema(description = "有效期限")
    private String validDate;
    
    @Schema(description = "流水号")
    private String flowNo;
    
    /**
     * 创建成功结果
     */
    public static OcrIdentifyResult success(String flowNo) {
        OcrIdentifyResult result = new OcrIdentifyResult();
        result.setSuccess(true);
        result.setFlowNo(flowNo);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static OcrIdentifyResult failure(String errorMessage, String flowNo) {
        OcrIdentifyResult result = new OcrIdentifyResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setFlowNo(flowNo);
        return result;
    }
}