package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.app.web.biz.enums.TaxDeclareStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.service.PersonalIncomeTaxDeclareService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDetailVo;
import com.olading.operate.labor.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class PersonalIncomeTaxDeclareTest extends BaseTest {

    @Autowired
    private PersonalIncomeTaxDeclareService personalIncomeTaxDeclareService;

    @Autowired
    private PersonalIncomeTaxDetailManager personalIncomeTaxDetailManager;

    @Autowired
    private PersonalIncomeTaxDeclareManager personalIncomeTaxDeclareManager;

    @Autowired
    private QueryService queryService;

    @Test
    public void testAddAndQueryPersonalIncomeTaxDeclare() {
        // 准备测试数据
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");
        vo.setSupplierId(100l);

        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 测试新增
        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo);
        log.info("testAddAndQueryPersonalIncomeTaxDeclare={}", JSONUtils.json(entity));
    }

    @Test
    public void testUpdatePersonalIncomeTaxDeclare() {
        // 先创建一个记录
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo);

        // 更新记录
        vo.setId(entity.getId());
        vo.setTaxpayersCount("15");
        vo.setCurrentIncome("150000.00");

        PersonalIncomeTaxDeclareEntity updatedEntity = personalIncomeTaxDeclareService.updatePersonalIncomeTaxDeclare(tenantInfo, vo);
        log.info("testAddAndQueryPersonalIncomeTaxDeclare={}", JSONUtils.json(updatedEntity));
    }

    @Test
    public void testDeletePersonalIncomeTaxDeclare() {
        // 先创建一个记录
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo);

        // 删除记录
        personalIncomeTaxDeclareService.deletePersonalIncomeTaxDeclare(tenantInfo, entity.getId());

        // 验证删除成功（应该抛出异常）
        assertThrows(IllegalStateException.class, () -> {
            personalIncomeTaxDeclareService.queryPersonalIncomeTaxDeclare(entity.getId());
        });
    }

    @Test
    public void testUpdateTaxStatusToDeclared() {
        // 先创建一个记录
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo);

        // 验证初始状态为未申报
        PersonalIncomeTaxDeclareVo queryVo = personalIncomeTaxDeclareService.queryPersonalIncomeTaxDeclare(entity.getId());
        assertEquals(TaxDeclareStatusEnum.NOT_DECLARED.name(), queryVo.getTaxStatus());

        // 更新状态为已申报
        personalIncomeTaxDeclareService.updateTaxStatusToDeclared(entity.getId());

        // 验证状态已更新
        PersonalIncomeTaxDeclareVo updatedVo = personalIncomeTaxDeclareService.queryPersonalIncomeTaxDeclare(entity.getId());
        assertEquals(TaxDeclareStatusEnum.DECLARED.name(), updatedVo.getTaxStatus());
    }

    @Test
    public void testAddPersonalIncomeTaxDeclareWithDetails() {
        // 准备测试数据
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-07");
        vo.setIncomeTaxMonth("2025-08");
        vo.setSupplierId(100L);

        // 准备明细数据
        List<PersonalIncomeTaxDetailVo> details = new ArrayList<>();

        // 明细1
        PersonalIncomeTaxDetailVo detail1 = new PersonalIncomeTaxDetailVo();
        detail1.setName("张三");
        detail1.setIdCard("110101199001011234");
        detail1.setCellphone("13800138001");
        detail1.setCurrentIncome(new BigDecimal("5000.00"));
        detail1.setAccumulatedIncome(new BigDecimal("35000.00"));
        detail1.setAccumulatedExpenses(new BigDecimal("21000.00"));
        detail1.setAccumulatedTaxFreeIncome(BigDecimal.ZERO);
        detail1.setAccumulatedOtherDeductions(BigDecimal.ZERO);
        detail1.setAccumulatedPrepaidTax(new BigDecimal("420.00"));
        detail1.setCurrentWithholdingTax(new BigDecimal("60.00"));
        detail1.setTaxPeriod("2025-07");
        detail1.setDeclareMonth("2025-08");
        detail1.setActualAmount(new BigDecimal("5000.00"));
        detail1.setAccumulatedTaxDeductionExpenses(new BigDecimal("21000.00"));
        detail1.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail1.setAccumulatedTaxableAmount(new BigDecimal("14000.00"));
        details.add(detail1);

        // 明细2
        PersonalIncomeTaxDetailVo detail2 = new PersonalIncomeTaxDetailVo();
        detail2.setName("李四");
        detail2.setIdCard("110101199002022345");
        detail2.setCellphone("13800138002");
        detail2.setCurrentIncome(new BigDecimal("8000.00"));
        detail2.setAccumulatedIncome(new BigDecimal("56000.00"));
        detail2.setAccumulatedExpenses(new BigDecimal("33600.00"));
        detail2.setAccumulatedTaxFreeIncome(BigDecimal.ZERO);
        detail2.setAccumulatedOtherDeductions(BigDecimal.ZERO);
        detail2.setAccumulatedPrepaidTax(new BigDecimal("672.00"));
        detail2.setCurrentWithholdingTax(new BigDecimal("96.00"));
        detail2.setTaxPeriod("2025-07");
        detail2.setDeclareMonth("2025-08");
        detail2.setActualAmount(new BigDecimal("8000.00"));
        detail2.setAccumulatedTaxDeductionExpenses(new BigDecimal("33600.00"));
        detail2.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail2.setAccumulatedTaxableAmount(new BigDecimal("22400.00"));
        details.add(detail2);

        // 明细3
        PersonalIncomeTaxDetailVo detail3 = new PersonalIncomeTaxDetailVo();
        detail3.setName("王五");
        detail3.setIdCard("110101199003033456");
        detail3.setCellphone("13800138003");
        detail3.setCurrentIncome(new BigDecimal("12000.00"));
        detail3.setAccumulatedIncome(new BigDecimal("84000.00"));
        detail3.setAccumulatedExpenses(new BigDecimal("50400.00"));
        detail3.setAccumulatedTaxFreeIncome(BigDecimal.ZERO);
        detail3.setAccumulatedOtherDeductions(BigDecimal.ZERO);
        detail3.setAccumulatedPrepaidTax(new BigDecimal("2520.00"));
        detail3.setCurrentWithholdingTax(new BigDecimal("360.00"));
        detail3.setTaxPeriod("2025-07");
        detail3.setDeclareMonth("2025-08");
        detail3.setActualAmount(new BigDecimal("12000.00"));
        detail3.setAccumulatedTaxDeductionExpenses(new BigDecimal("50400.00"));
        detail3.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail3.setAccumulatedTaxableAmount(new BigDecimal("33600.00"));
        details.add(detail3);

        vo.setDetails(details);

        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 测试新增
        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo);

        // 验证主记录
        assertNotNull(entity);
        assertNotNull(entity.getId());
        assertEquals(vo.getSupplierCorporationId(), entity.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), entity.getTaxPaymentPeriod());
        assertEquals(vo.getIncomeTaxMonth(), entity.getIncomeTaxMonth());

        // 等待异步处理完成
        waitForAsyncProcessing(entity.getId());

        // 重新查询获取最新数据
        PersonalIncomeTaxDeclareEntity updatedEntity = personalIncomeTaxDeclareManager.getPersonalIncomeTaxDeclareById(entity.getId());

        // 验证汇总数据
        assertEquals("3", updatedEntity.getTaxpayersCount()); // 3个人
        assertEquals("25000.00", updatedEntity.getCurrentIncome()); // 5000 + 8000 + 12000 = 25000

        log.info("testAddPersonalIncomeTaxDeclareWithDetails 主记录={}", JSONUtils.json(entity));

        // 验证明细记录
        List<PersonalIncomeTaxDetailEntity> savedDetails = personalIncomeTaxDetailManager.queryDetailsByTaxDeclareId(entity.getId());
        assertNotNull(savedDetails);
        assertEquals(3, savedDetails.size());

        // 验证明细数据
        PersonalIncomeTaxDetailEntity savedDetail1 = savedDetails.stream()
                .filter(d -> "张三".equals(d.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(savedDetail1);
        assertEquals(entity.getId(), savedDetail1.getTaxDeclareId());
        assertEquals(new BigDecimal("5000.00"), savedDetail1.getCurrentIncome());
        assertEquals("2025-07", savedDetail1.getTaxPeriod());

        log.info("testAddPersonalIncomeTaxDeclareWithDetails 明细记录={}", JSONUtils.json(savedDetails));
    }

    @Test
    public void testQueryPersonalIncomeTaxDeclareWithDetails() {
        // 先创建一个带明细的申报记录
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-09");
        vo.setIncomeTaxMonth("2025-10");
        vo.setSupplierId(100L);

        // 准备明细数据
        List<PersonalIncomeTaxDetailVo> details = new ArrayList<>();

        // 明细1
        PersonalIncomeTaxDetailVo detail1 = new PersonalIncomeTaxDetailVo();
        detail1.setName("详情测试用户1");
        detail1.setIdCard("110101199001011111");
        detail1.setCellphone("13900139001");
        detail1.setCurrentIncome(new BigDecimal("7000.00"));
        detail1.setAccumulatedIncome(new BigDecimal("42000.00"));
        detail1.setAccumulatedExpenses(new BigDecimal("25200.00"));
        detail1.setAccumulatedTaxFreeIncome(BigDecimal.ZERO);
        detail1.setAccumulatedOtherDeductions(BigDecimal.ZERO);
        detail1.setAccumulatedPrepaidTax(new BigDecimal("504.00"));
        detail1.setCurrentWithholdingTax(new BigDecimal("72.00"));
        detail1.setTaxPeriod("2025-09");
        detail1.setDeclareMonth("2025-10");
        detail1.setActualAmount(new BigDecimal("7000.00"));
        detail1.setAccumulatedTaxDeductionExpenses(new BigDecimal("25200.00"));
        detail1.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail1.setAccumulatedTaxableAmount(new BigDecimal("16800.00"));
        details.add(detail1);

        // 明细2
        PersonalIncomeTaxDetailVo detail2 = new PersonalIncomeTaxDetailVo();
        detail2.setName("详情测试用户2");
        detail2.setIdCard("110101199002022222");
        detail2.setCellphone("13900139002");
        detail2.setCurrentIncome(new BigDecimal("15000.00"));
        detail2.setAccumulatedIncome(new BigDecimal("90000.00"));
        detail2.setAccumulatedExpenses(new BigDecimal("54000.00"));
        detail2.setAccumulatedTaxFreeIncome(BigDecimal.ZERO);
        detail2.setAccumulatedOtherDeductions(BigDecimal.ZERO);
        detail2.setAccumulatedPrepaidTax(new BigDecimal("2700.00"));
        detail2.setCurrentWithholdingTax(new BigDecimal("450.00"));
        detail2.setTaxPeriod("2025-09");
        detail2.setDeclareMonth("2025-10");
        detail2.setActualAmount(new BigDecimal("15000.00"));
        detail2.setAccumulatedTaxDeductionExpenses(new BigDecimal("54000.00"));
        detail2.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail2.setAccumulatedTaxableAmount(new BigDecimal("36000.00"));
        details.add(detail2);

        vo.setDetails(details);

        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 新增申报记录
        PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo);
        assertNotNull(entity);
        assertNotNull(entity.getId());

        // 等待异步处理完成
        waitForAsyncProcessing(entity.getId());

        // 测试查询详情（包含明细）
        PersonalIncomeTaxDeclareVo queryResult = personalIncomeTaxDeclareService.queryPersonalIncomeTaxDeclare(entity.getId());

        // 验证主记录
        assertNotNull(queryResult);
        assertEquals(entity.getId(), queryResult.getId());
        assertEquals(vo.getSupplierCorporationId(), queryResult.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), queryResult.getTaxPaymentPeriod());
        assertEquals(vo.getIncomeTaxMonth(), queryResult.getIncomeTaxMonth());

        // 验证汇总数据
        assertEquals("2", queryResult.getTaxpayersCount()); // 2个人
        assertEquals("22000.00", queryResult.getCurrentIncome()); // 7000 + 15000 = 22000
        assertEquals(new BigDecimal("522.00"), queryResult.getCurrentWithholdingTax()); // 72 + 450 = 522

        // 验证明细数据
        assertNotNull(queryResult.getDetails());
        assertEquals(2, queryResult.getDetails().size());


        log.info("testQueryPersonalIncomeTaxDeclareWithDetails 查询结果={}", JSONUtils.json(queryResult));
        log.info("testQueryPersonalIncomeTaxDeclareWithDetails 明细数量={}", queryResult.getDetails().size());
    }

    @Test
    public void testAddPersonalIncomeTaxDeclareWithUniqueConstraint() {
        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 第一次添加申报记录
        PersonalIncomeTaxDeclareVo vo1 = new PersonalIncomeTaxDeclareVo();
        vo1.setSupplierCorporationId(2L);
        vo1.setTaxPaymentPeriod("2025-10");
        vo1.setIncomeTaxMonth("2025-11");
        vo1.setSupplierId(100L);

        // 准备第一次的明细数据
        List<PersonalIncomeTaxDetailVo> details1 = new ArrayList<>();
        PersonalIncomeTaxDetailVo detail1 = new PersonalIncomeTaxDetailVo();
        detail1.setName("第一次用户1");
        detail1.setIdCard("110101199001011111");
        detail1.setCellphone("13700137001");
        detail1.setCurrentIncome(new BigDecimal("5000.00"));
        detail1.setCurrentWithholdingTax(new BigDecimal("50.00"));
        detail1.setTaxPeriod("2025-10");
        detail1.setDeclareMonth("2025-11");
        detail1.setActualAmount(new BigDecimal("5000.00"));
        detail1.setAccumulatedTaxDeductionExpenses(new BigDecimal("3000.00"));
        detail1.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail1.setAccumulatedTaxableAmount(new BigDecimal("2000.00"));
        details1.add(detail1);
        vo1.setDetails(details1);

        // 第一次新增
        PersonalIncomeTaxDeclareEntity entity1 = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo1);
        assertNotNull(entity1);
        assertNotNull(entity1.getId());

        // 等待异步处理完成
        waitForAsyncProcessing(entity1.getId());

        // 重新查询获取最新数据
        PersonalIncomeTaxDeclareEntity updatedEntity1 = personalIncomeTaxDeclareManager.getPersonalIncomeTaxDeclareById(entity1.getId());
        assertEquals("1", updatedEntity1.getTaxpayersCount());
        assertEquals("5000.00", updatedEntity1.getCurrentIncome());

        Long firstEntityId = entity1.getId();
        log.info("第一次添加的申报记录ID: {}", firstEntityId);

        // 验证第一次的明细数据
        List<PersonalIncomeTaxDetailEntity> firstDetails = personalIncomeTaxDetailManager.queryDetailsByTaxDeclareId(firstEntityId);
        assertEquals(1, firstDetails.size());
        assertEquals("第一次用户1", firstDetails.get(0).getName());

        // 第二次添加相同的作业主体ID + 税款所属期的申报记录
        PersonalIncomeTaxDeclareVo vo2 = new PersonalIncomeTaxDeclareVo();
        vo2.setSupplierCorporationId(2L); // 相同的作业主体ID
        vo2.setTaxPaymentPeriod("2025-10"); // 相同的税款所属期
        vo2.setIncomeTaxMonth("2025-11");
        vo2.setSupplierId(100L);

        // 准备第二次的明细数据（不同的数据）
        List<PersonalIncomeTaxDetailVo> details2 = new ArrayList<>();
        PersonalIncomeTaxDetailVo detail2_1 = new PersonalIncomeTaxDetailVo();
        detail2_1.setName("第二次用户1");
        detail2_1.setIdCard("110101199002022222");
        detail2_1.setCellphone("13700137002");
        detail2_1.setCurrentIncome(new BigDecimal("8000.00"));
        detail2_1.setCurrentWithholdingTax(new BigDecimal("80.00"));
        detail2_1.setTaxPeriod("2025-10");
        detail2_1.setDeclareMonth("2025-11");
        detail2_1.setActualAmount(new BigDecimal("8000.00"));
        detail2_1.setAccumulatedTaxDeductionExpenses(new BigDecimal("4800.00"));
        detail2_1.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail2_1.setAccumulatedTaxableAmount(new BigDecimal("3200.00"));
        details2.add(detail2_1);

        PersonalIncomeTaxDetailVo detail2_2 = new PersonalIncomeTaxDetailVo();
        detail2_2.setName("第二次用户2");
        detail2_2.setIdCard("110101199003033333");
        detail2_2.setCellphone("13700137003");
        detail2_2.setCurrentIncome(new BigDecimal("12000.00"));
        detail2_2.setCurrentWithholdingTax(new BigDecimal("120.00"));
        detail2_2.setTaxPeriod("2025-10");
        detail2_2.setDeclareMonth("2025-11");
        detail2_2.setActualAmount(new BigDecimal("12000.00"));
        detail2_2.setAccumulatedTaxDeductionExpenses(new BigDecimal("7200.00"));
        detail2_2.setAccumulatedTaxReductions(BigDecimal.ZERO);
        detail2_2.setAccumulatedTaxableAmount(new BigDecimal("4800.00"));
        details2.add(detail2_2);

        vo2.setDetails(details2);

        // 第二次新增（应该删除第一次的记录并重新创建）
        PersonalIncomeTaxDeclareEntity entity2 = personalIncomeTaxDeclareService.addPersonalIncomeTaxDeclare(tenantInfo, vo2);
        assertNotNull(entity2);
        assertNotNull(entity2.getId());

        // 验证新记录的统计数据
        assertEquals("2", entity2.getTaxpayersCount()); // 2个人
        assertEquals("20000.00", entity2.getCurrentIncome()); // 8000 + 12000 = 20000

        Long secondEntityId = entity2.getId();
        log.info("第二次添加的申报记录ID: {}", secondEntityId);

        // 验证第一次的记录已被删除（查询应该抛出异常或返回null）
        try {
            PersonalIncomeTaxDeclareVo deletedVo = personalIncomeTaxDeclareService.queryPersonalIncomeTaxDeclare(firstEntityId);
            // 如果能查到，说明没有被删除，测试失败
            fail("第一次的申报记录应该已被删除");
        } catch (Exception e) {
            // 预期的异常，说明记录已被删除
            log.info("第一次的申报记录已被正确删除");
        }

        // 验证第一次的明细数据也已被删除
        List<PersonalIncomeTaxDetailEntity> deletedDetails = personalIncomeTaxDetailManager.queryDetailsByTaxDeclareId(firstEntityId);
        assertEquals(0, deletedDetails.size());

        // 验证第二次的明细数据
        List<PersonalIncomeTaxDetailEntity> secondDetails = personalIncomeTaxDetailManager.queryDetailsByTaxDeclareId(secondEntityId);
        assertEquals(2, secondDetails.size());

        // 验证明细数据内容
        boolean foundUser1 = secondDetails.stream().anyMatch(d -> "第二次用户1".equals(d.getName()));
        boolean foundUser2 = secondDetails.stream().anyMatch(d -> "第二次用户2".equals(d.getName()));
        assertTrue(foundUser1);
        assertTrue(foundUser2);

        log.info("testAddPersonalIncomeTaxDeclareWithUniqueConstraint 测试通过");
    }

    /**
     * 等待异步处理完成
     */
    private void waitForAsyncProcessing(Long entityId) {
        int maxWaitTime = 10; // 最大等待10秒
        int waitInterval = 100; // 每100毫秒检查一次
        int maxAttempts = maxWaitTime * 1000 / waitInterval;

        for (int i = 0; i < maxAttempts; i++) {
            try {
                PersonalIncomeTaxDeclareEntity entity = personalIncomeTaxDeclareManager.getPersonalIncomeTaxDeclareById(entityId);
                if (PersonalIncomeTaxDeclareStatusEnum.GENERATED.name().equals(entity.getStatus())) {
                    log.info("异步处理完成，等待了 {} 毫秒", i * waitInterval);
                    return;
                }
                Thread.sleep(waitInterval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待异步处理被中断", e);
            } catch (Exception e) {
                log.warn("检查异步处理状态时出错: {}", e.getMessage());
            }
        }

        log.warn("异步处理超时，等待了 {} 秒", maxWaitTime);
        // 不抛出异常，让测试继续执行，可能异步处理只是比较慢
    }

}
