package com.olading.operate.labor.util.excel;


import com.lanmaoly.util.lang.StringUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.validator.BaseValidator;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Excel导出数据（行）映射的基础类（Pojo）
 * <AUTHOR>
 * @date 2022/2/23 10:35
 */
public class ExcelRow {

    /**
     * 行号（从1开始）
     */
    private int rowNum = 0;

    /**
     * 校验错误的信息
     */
    private Map<String, ExcelCellError> errors = new HashMap<>();

    /**
     * 所有数据到快照，保存过滤后的数据
     * Map<Excel表头名，值>
     */
    private Map<String, Object> snapshot = new HashMap<>();

    private Map<String, Field> titleFieldMap = new HashMap<>();

    private ExcelHeader excelHeader = null;

    public Map<String, Object> getSnapshot() {
        return snapshot;
    }

    public void setRowNum(int rowNum) {
        this.rowNum = rowNum;
    }

    public int getRowNum() {
        return rowNum;
    }

    public ExcelHeader getExcelHeader() {
        return excelHeader;
    }

    public void setExcelHeader(ExcelHeader excelHeader) {
        this.excelHeader = excelHeader;
    }

    public void pushError(ValidationException e) {
        pushError(e.getName(), e.getMessage(), e.getDetail());
    }

    public void pushError(String excelTitle, String error, String detail) {
        if (!errors.containsKey(excelTitle)) {
            // 一个Cell只保留第一个错误，后面的错误可能是第一个错误引起的。
            errors.put(excelTitle, new ExcelCellError(error, detail));
        }
    }

    public void pushCustomError(String error) {
        pushError("", error, "");
    }

    public Integer getTitleIndex(String excelTitle)
    {
        if (null == excelHeader) {
            return -1;
        }

        ExcelColumnEntity column = excelHeader.getColumnByTitle(excelTitle);
        return null != column ? column.getColIndex() : 0;
    }

    /**
     * 按照实际读取的表头的顺序，返回数据
     * @return
     */
    public List<Object> getDataListByColumnIndex(boolean appendError)
    {
        if (null == excelHeader) {
            return new ArrayList<>();
        }

        return getDataListByColumnIndex(excelHeader.getColumns(), appendError);
    }

    /**
     * 按照定义的表头的顺序，返回数据
     * @return
     */
    public List<Object> getDataListByColumnIndex(List<ExcelColumnEntity> columns, boolean appendError)
    {
        List<Object> dataList = new ArrayList<>();
        if (null == columns) {
            return dataList;
        }

        for (ExcelColumnEntity column : columns) {
            String title = column.getExcelTitle();
            if (StringUtils.isBlank(title)) {
                title = column.getName();
            }

            dataList.add(snapshot.getOrDefault(title, ""));
        }

        if (appendError && isVerifyFail()) {
            dataList.add(new ExcelCellError(getRowErrorString()));
        }

        return dataList;
    }

    public Map<String, ExcelCellError> getErrors() {
        return errors;
    }

    public ExcelCellError getCellError(String excelTitle) {
        if (errors.containsKey(excelTitle)) {
            return errors.get(excelTitle);
        }

        return null;
    }

    public String getRowErrorString() {
        Set<String> messages = new HashSet<>();
        for (Map.Entry<String, ExcelCellError> entry: errors.entrySet()) {
            messages.add(entry.getValue().getError());
        }

        return StringUtils.join(messages, BaseValidator.ERRORS_SPLIT);
    }

    public int getErrorCount() {
        return errors.size();
    }

    /**
     * 返回绑定关系之外的字段
     * @return
     */
    public Map<String, Object> getExtraData() {
        Map<String, Object> extraData = new HashMap<>();
        for (Map.Entry<String, Object> entry: snapshot.entrySet()) {
            if (!titleFieldMap.containsKey(entry.getKey())) {
                extraData.put(entry.getKey(), entry.getValue());
            }
        }

        return extraData;
    }

    public Object getCellValue(String excelTitle) {
        return snapshot.get(excelTitle);
    }

    public void addSnapshot(String key, Object obj, Field field) {
        if (null == obj || StringUtils.isBlank(String.valueOf(obj))) {
            return;
        }

        if (obj instanceof Date) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            snapshot.put(key, formatter.format((Date)obj).replace(" 00:00:00", ""));
        } else {
            snapshot.put(key, obj);
        }

        if (null != field) {
            titleFieldMap.put(key, field);
        }
    }

    public boolean isVerifyFail() {
        return !errors.isEmpty();
    }

    /**
     * 是否空数据
     * @return
     */
    public boolean isEmptyRow() {
        for (Map.Entry<String, Object> entry: snapshot.entrySet()) {
            if (!isEmptyValue(entry.getValue())) {
                return false;
            }
        }

        return true;
    }

    @Override
    public String toString() {
        return snapshot.toString();
    }

    private boolean isEmptyValue(Object val) {
        if (null == val || StringUtils.isBlank(String.valueOf(val)) || StringUtils.trimDotZero(val).equals("0")) {
            return true;
        }

        return false;
    }
}
