package com.olading.operate.labor.util;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.HashSet;
import java.util.Random;

public class GroupingBatchProcessorTest {



    @DisplayName("严格按照分组条件进行分组")
    @Test
    public void test01() throws Exception {

        Random random = new Random();
        try (var p = new GroupingBatchProcessor<Long>(
                batch -> {
                    var set = new HashSet<>(batch);
                    Assertions.assertThat(set).hasSize(1);
                    System.out.println("value=" + batch.get(0) + ", size=" + batch.size());
                },
                o -> o,
                1,
                Duration.ofSeconds(1),
                1000
        )) {

            for (int i = 0; i < 100000; i++) {
                p.accept(random.nextLong(10));
            }

        }

    }
}
