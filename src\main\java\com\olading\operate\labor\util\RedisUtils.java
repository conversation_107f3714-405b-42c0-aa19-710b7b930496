package com.olading.operate.labor.util;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 操作redis 的组件
 */
@Component
public class RedisUtils {

    private static final Logger log = LoggerFactory.getLogger(RedisUtils.class);

    private RedisTemplate<String, String> redisTemplate;

    private static final String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";


    public RedisUtils(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 保存redis 的值
     *
     * @param key     redis key
     * @param value   保存的对象
     * @param seconds 保存时间，小于等于0 将永久保存
     */
    public void set(String key, Object value, int seconds) {
        redisTemplate.opsForValue().set(key, JSONUtils.json(value), seconds, TimeUnit.SECONDS);
    }

    /**
     * setNx
     *
     * @param key     redis key
     * @param value   保存的对象
     * @param seconds 保存时间，小于等于0 将永久保存
     */
    public boolean setIfAbsent(String key, Object value, int seconds) {
        return redisTemplate.opsForValue().setIfAbsent(key, JSONUtils.json(value), seconds, TimeUnit.SECONDS);
    }

    /**
     * 保存redis 的值
     *
     * @param key     redis key
     * @param value   保存的对象
     * @param seconds 保存时间，小于等于0 将永久保存
     */
    public void setSpecial(String key, String value, int seconds) {
        redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
    }


    /**
     * 获取redis 的值
     *
     * @param key   redis key
     * @param clazz 反序列化需要的对象的Class
     * @param <T>   T结果返回的类型
     * @return T类型的value
     */
    public <T> T get(String key, Class<T> clazz) {
        String result = redisTemplate.opsForValue().get(key);
        T obj = null;
        if (StringUtils.isNotBlank(result)) {
            obj = JSONUtils.deserialize(result, clazz);
        }
        return obj;
    }

    public <T> T get(String key, TypeReference<T> jsonTypeReference) {
        String result = redisTemplate.opsForValue().get(key);
        T obj = null;
        if (StringUtils.isNotBlank(result)) {
            obj = JSONUtils.deserialize(result, jsonTypeReference);
        }
        return obj;
    }

    /**
     * 获取redis 的值
     *
     * @param key redis key
     * @return T类型的value
     */
    public String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除 redis 的值
     *
     * @param keys 删除的key
     */
    public boolean del(String keys) {
        return redisTemplate.delete(keys);
    }

    public void putAll(String hashName, Map<String,String> hashValue, int seconds) {
        redisTemplate.opsForHash().putAll(hashName, hashValue);
        if(seconds > 0){
            try {
                redisTemplate.expire(hashName,seconds,TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("设置过期时间失败,key:{}",hashName);
                redisTemplate.delete(hashName);
            }
        }
    }

    public String getHash(String hashName, String key) {
        return (String)redisTemplate.opsForHash().get(hashName,key);
    }

    /**
     * 向list右侧插入数据
     * @param value
     * @param key
     */
    public void pushListLeft(String value, String key) {
        redisTemplate.opsForList().leftPush(key, value);
    }
    /**
     * 从list左侧弹出数据
     * @param key
     */
    public String popListRight(String key) {
        return redisTemplate.boundListOps(key).rightPop();
    }
    /**
     * 从list中删除数据
     * @param key
     */
    public Long deleteListKeyLeft(String key, String value) {
        return redisTemplate.opsForList().remove(key, 0l, value);
    }
    /**
     * 获取list的值
     * @param key
     */
    public List getListAllValue(String key) {
        Long size = redisTemplate.boundListOps(key).size();
        return redisTemplate.boundListOps(key).range(0, size - 1);
    }

    /**
     * 按索引查询元素
     * @param key
     * @param index
     * @return
     */
    public String popListList(String key, int index) {
        return redisTemplate.boundListOps(key).index(index);
    }

    /**
     * 将list放入缓存
     * @param key
     */
    public void pushAllListLeft(String key, List list) {
        redisTemplate.opsForList().leftPushAll(key, list);
    }

    /**
     * 加锁
     * @param lockKey
     * @param requestId
     * @param expireTime MILLISECONDS
     * @return
     */
    public Boolean tryGetLock(String lockKey,String requestId,long expireTime){
        return redisTemplate.execute((RedisCallback<Boolean>) connection -> connection.set(lockKey.getBytes(), requestId.getBytes(), Expiration.from(expireTime, TimeUnit.MILLISECONDS), RedisStringCommands.SetOption.SET_IF_ABSENT));
    }

    /**
     * 释放锁
     * @param lockKey
     * @param requestId
     * @return
     */
    public Boolean tryReleaseLock(String lockKey,String requestId){
        if (!isSafeVar(lockKey) || !isSafeVar(requestId)) {
            log.error("锁参数格式错误:{}, {}", lockKey, requestId);
            throw new RuntimeException("参数格式错误");
        }

        return redisTemplate.execute((RedisCallback<Boolean>) connection -> connection.eval(script.getBytes(), ReturnType.BOOLEAN,1,lockKey.getBytes(),requestId.getBytes()));
    }

    /**
     * 计数器自增
     */
    public Long increment(String key, Long value) {
        if(value < 0){
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, value);
    }

    /**
     * 安全校验方式代码注入风险
     * @param var
     * @return
     */
    private Boolean isSafeVar(String var) {
        String regex = "^[a-zA-Z0-9\\-:_\\.]+$";
        return Pattern.matches(regex, var);
    }
}
