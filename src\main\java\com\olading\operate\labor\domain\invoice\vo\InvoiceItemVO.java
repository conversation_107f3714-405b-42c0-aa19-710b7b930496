package com.olading.operate.labor.domain.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "开票明细信息")
public class InvoiceItemVO {
    
    @Schema(description = "明细ID")
    private Long id;
    
    @Schema(description = "合同ID")
    private Long contractId;
    
    @Schema(description = "开票ID")
    private Long invoiceId;
    
    @Schema(description = "账单ID")
    private Long billId;
    
    @Schema(description = "账单编号")
    private String billNo;
    
    @Schema(description = "账单月份")
    private LocalDate billMonth;
    
    @Schema(description = "发票类目")
    private String invoiceCategory;
    
    @Schema(description = "开票金额")
    private BigDecimal fee;
}