package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

@Getter
@Setter
@Comment("税务个税申报明细表")
@Entity
@Table(name = "t_personal_income_tax_detail", schema = "olading_labor")
public class PersonalIncomeTaxDetailEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("申报ID")
    @Column(name = "tax_declare_id", nullable = false)
    private Long taxDeclareId;

    @Size(max = 50)
    @NotNull
    @Comment("姓名")
    @Column(name = "name", nullable = false, length = 50)
    private String name;

    @Size(max = 20)
    @NotNull
    @Comment("身份证号")
    @Column(name = "id_card", nullable = false, length = 20)
    private String idCard;

    @Comment("本期收入")
    @ColumnDefault("0.00")
    @Column(name = "current_income", precision = 22, scale = 2)
    private BigDecimal currentIncome;

    @Comment("累计收入")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_income", precision = 22, scale = 2)
    private BigDecimal accumulatedIncome;

    @Comment("累计费用")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_expenses", precision = 22, scale = 2)
    private BigDecimal accumulatedExpenses;

    @Comment("累计免税收入")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_free_income", precision = 22, scale = 2)
    private BigDecimal accumulatedTaxFreeIncome;

    @Comment("累计依法确定的其他扣除")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_other_deductions", precision = 22, scale = 2)
    private BigDecimal accumulatedOtherDeductions;

    @Comment("累计已预缴税额")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_prepaid_tax", precision = 22, scale = 2)
    private BigDecimal accumulatedPrepaidTax;

    @Comment("本期应预扣预缴税额")
    @ColumnDefault("0.00")
    @Column(name = "current_withholding_tax", precision = 22, scale = 2)
    private BigDecimal currentWithholdingTax;

    @Size(max = 20)
    @NotNull
    @Comment("税款所属期")
    @Column(name = "tax_period", nullable = false, length = 20)
    private String taxPeriod;

    @Size(max = 20)
    @NotNull
    @Comment("申报月份")
    @Column(name = "declare_month", nullable = false, length = 20)
    private String declareMonth;

    @Size(max = 20)
    @NotNull
    @Comment("手机号")
    @Column(name = "cellphone", nullable = false, length = 20)
    private String cellphone;

    @Comment("实发金额")
    @ColumnDefault("0.00")
    @Column(name = "actual_amount", precision = 22, scale = 2)
    private BigDecimal actualAmount;

    @Comment("累计减除费用")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_deduction_expenses", precision = 22, scale = 2)
    private BigDecimal accumulatedTaxDeductionExpenses;

    @Comment("累计减免税额")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_reductions", precision = 22, scale = 2)
    private BigDecimal accumulatedTaxReductions;

    @Comment("累计应纳税额")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_taxable_amount", precision = 22, scale = 2)
    private BigDecimal accumulatedTaxableAmount;

    public PersonalIncomeTaxDetailEntity() {
    }

    public PersonalIncomeTaxDetailEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }
}