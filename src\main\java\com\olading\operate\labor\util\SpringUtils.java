package com.olading.operate.labor.util;

import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

public class SpringUtils {

    public static void afterCommit(Runnable runnable) {

        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            // 如果开启了事务，在事务提交后真正发送消息
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    runnable.run();
                }
            });
        } else {
            runnable.run();
        }
    }
}
