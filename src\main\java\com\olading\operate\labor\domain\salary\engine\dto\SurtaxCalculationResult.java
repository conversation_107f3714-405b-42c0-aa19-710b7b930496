package com.olading.operate.labor.domain.salary.engine.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 附加税计算结果对象
 */
@Data
public class SurtaxCalculationResult {
    
    /**
     * 城市维护建设税
     */
    private BigDecimal urbanConstructionTax = BigDecimal.ZERO;
    
    /**
     * 教育费附加
     */
    private BigDecimal educationSurcharge = BigDecimal.ZERO;
    
    /**
     * 地方教育附加
     */
    private BigDecimal localEducationSurcharge = BigDecimal.ZERO;
    
    /**
     * 获取附加税总额
     */
    public BigDecimal getTotalSurtax() {
        return urbanConstructionTax.add(educationSurcharge).add(localEducationSurcharge);
    }
    
    /**
     * 创建零值附加税结果
     */
    public static SurtaxCalculationResult zero() {
        return new SurtaxCalculationResult();
    }
    
    /**
     * 构造方法
     */
    public SurtaxCalculationResult() {
        this.urbanConstructionTax = BigDecimal.ZERO;
        this.educationSurcharge = BigDecimal.ZERO;
        this.localEducationSurcharge = BigDecimal.ZERO;
    }
    
    /**
     * 构造方法
     */
    public SurtaxCalculationResult(BigDecimal urbanConstructionTax, 
                                  BigDecimal educationSurcharge, 
                                  BigDecimal localEducationSurcharge) {
        this.urbanConstructionTax = urbanConstructionTax != null ? urbanConstructionTax : BigDecimal.ZERO;
        this.educationSurcharge = educationSurcharge != null ? educationSurcharge : BigDecimal.ZERO;
        this.localEducationSurcharge = localEducationSurcharge != null ? localEducationSurcharge : BigDecimal.ZERO;
    }
    
    /**
     * 校验数据有效性
     */
    public void validate() {
        if (urbanConstructionTax.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("城市维护建设税不能为负数");
        }
        if (educationSurcharge.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("教育费附加不能为负数");
        }
        if (localEducationSurcharge.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("地方教育附加不能为负数");
        }
    }
    
    @Override
    public String toString() {
        return "SurtaxCalculationResult{" +
                "urbanConstructionTax=" + urbanConstructionTax +
                ", educationSurcharge=" + educationSurcharge +
                ", localEducationSurcharge=" + localEducationSurcharge +
                ", totalSurtax=" + getTotalSurtax() +
                '}';
    }
}