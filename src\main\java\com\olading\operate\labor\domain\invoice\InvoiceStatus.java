package com.olading.operate.labor.domain.invoice;

/**
 * 开票状态枚举
 */
public enum InvoiceStatus {
    PENDING("PENDING", "待开票"),
    INVOICED("INVOICED", "已开票"),
    RETURNED("RETURNED", "已退回");
    
    private final String code;
    private final String description;
    
    InvoiceStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() { 
        return code; 
    }
    
    public String getDescription() { 
        return description; 
    }
}