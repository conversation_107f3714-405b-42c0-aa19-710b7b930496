package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.app.web.biz.enums.TaxDeclareStatusEnum;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.service.ValueAddedTaxDeclareService;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDetailVo;
import com.olading.operate.labor.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class ValueAddedTaxDeclareServiceTest extends BaseTest {

    @Autowired
    private ValueAddedTaxDeclareService valueAddedTaxDeclareService;

    @Autowired
    private ValueAddedTaxDetailManager valueAddedTaxDetailManager;

    @Autowired
    private ValueAddedTaxDeclareManager valueAddedTaxDeclareManager;

    @Test
    public void testAddAndQueryValueAddedTaxDeclare() {
        // 准备测试数据
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");
        vo.setSupplierId(100l);

        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 测试新增
        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo);
        assertNotNull(entity);
        assertNotNull(entity.getId());
        assertEquals(vo.getSupplierCorporationId(), entity.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), entity.getTaxPaymentPeriod());
        assertEquals(vo.getIncomeTaxMonth(), entity.getIncomeTaxMonth());

        // 测试查询
        ValueAddedTaxDeclareVo queryResult = valueAddedTaxDeclareService.queryValueAddedTaxDeclare(entity.getId());
        assertNotNull(queryResult);
        assertEquals(entity.getId(), queryResult.getId());
        assertEquals(vo.getSupplierCorporationId(), queryResult.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), queryResult.getTaxPaymentPeriod());
    }

    @Test
    public void testUpdateValueAddedTaxDeclare() {
        // 先创建一个记录
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo);

        // 更新记录
        vo.setId(entity.getId());
        vo.setTaxpayersCount("15");
        vo.setCurrentIncome("150000.00");

        ValueAddedTaxDeclareEntity updatedEntity = valueAddedTaxDeclareService.updateValueAddedTaxDeclare(tenantInfo, vo);
        assertNotNull(updatedEntity);
        assertEquals("15", updatedEntity.getTaxpayersCount());
//        assertEquals("150000.00", updatedEntity.getCurrentIncome());
    }

    @Test
    public void testDeleteValueAddedTaxDeclare() {
        // 先创建一个记录
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo);

        // 删除记录
        valueAddedTaxDeclareService.deleteValueAddedTaxDeclare(tenantInfo, entity.getId());

        // 验证删除成功（应该抛出异常）
        assertThrows(IllegalStateException.class, () -> {
            valueAddedTaxDeclareService.queryValueAddedTaxDeclare(entity.getId());
        });
    }

    @Test
    public void testUpdateTaxStatusToDeclared() {
        // 先创建一个记录
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-01");
        vo.setIncomeTaxMonth("2025-02");
        vo.setTaxpayersCount("10");
        vo.setCurrentIncome("100000.00");
        vo.setSupplierId(100L);

        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);
        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo);

        // 验证初始状态为未申报
        ValueAddedTaxDeclareVo queryVo = valueAddedTaxDeclareService.queryValueAddedTaxDeclare(entity.getId());
        assertEquals(TaxDeclareStatusEnum.NOT_DECLARED.name(), queryVo.getTaxStatus());

        // 更新状态为已申报
        valueAddedTaxDeclareService.updateTaxStatusToDeclared(entity.getId());

        // 验证状态已更新
        ValueAddedTaxDeclareVo updatedVo = valueAddedTaxDeclareService.queryValueAddedTaxDeclare(entity.getId());
        assertEquals(TaxDeclareStatusEnum.DECLARED.name(), updatedVo.getTaxStatus());
    }

    @Test
    public void testAddValueAddedTaxDeclareWithDetails() {
        // 准备测试数据
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-07");
        vo.setIncomeTaxMonth("2025-08");
        vo.setSupplierId(100L);

        // 准备明细数据
        List<ValueAddedTaxDetailVo> details = new ArrayList<>();

        // 明细1
        ValueAddedTaxDetailVo detail1 = new ValueAddedTaxDetailVo();
        detail1.setName("张三");
        detail1.setIdCard("110101199001011234");
        detail1.setCertificateType("身份证");
        detail1.setCountryRegion("中国");
        detail1.setAddress("北京市朝阳区");
        detail1.setUserName("张三用户");
        detail1.setUserUniqueCode("USER001");
        detail1.setTaxBasis(new BigDecimal("10000.00"));
        detail1.setTaxItem("信息技术服务");
        detail1.setTaxRate("6%");
        detail1.setVatAmount(new BigDecimal("600.00"));
        detail1.setVatExemptionCode("001");
        detail1.setVatExemptionAmount(BigDecimal.ZERO);
        detail1.setVatPayable(new BigDecimal("600.00"));
        detail1.setUrbanTaxRate("7%");
        detail1.setUrbanTaxAmount(new BigDecimal("42.00"));
        detail1.setUrbanExemptionCode("002");
        detail1.setUrbanExemptionAmount(BigDecimal.ZERO);
        detail1.setUrbanTaxPayable(new BigDecimal("42.00"));
        detail1.setEduTaxRate("3%");
        detail1.setEduTaxAmount(new BigDecimal("18.00"));
        detail1.setEduExemptionCode("003");
        detail1.setEduExemptionAmount(BigDecimal.ZERO);
        detail1.setEduTaxPayable(new BigDecimal("18.00"));
        detail1.setLocalEduTaxRate("2%");
        detail1.setLocalEduTaxAmount(new BigDecimal("12.00"));
        detail1.setLocalEduExemptionCode("004");
        detail1.setLocalEduExemptionAmount(BigDecimal.ZERO);
        detail1.setLocalEduTaxPayable(new BigDecimal("12.00"));
        detail1.setTaxPeriod("2025-07");
        detail1.setDeclareMonth("2025-08");
        details.add(detail1);

        // 明细2
        ValueAddedTaxDetailVo detail2 = new ValueAddedTaxDetailVo();
        detail2.setName("李四");
        detail2.setIdCard("110101199002022345");
        detail2.setCertificateType("身份证");
        detail2.setCountryRegion("中国");
        detail2.setAddress("上海市浦东新区");
        detail2.setUserName("李四用户");
        detail2.setUserUniqueCode("USER002");
        detail2.setTaxBasis(new BigDecimal("15000.00"));
        detail2.setTaxItem("技术咨询服务");
        detail2.setTaxRate("6%");
        detail2.setVatAmount(new BigDecimal("900.00"));
        detail2.setVatExemptionCode("001");
        detail2.setVatExemptionAmount(BigDecimal.ZERO);
        detail2.setVatPayable(new BigDecimal("900.00"));
        detail2.setUrbanTaxRate("7%");
        detail2.setUrbanTaxAmount(new BigDecimal("63.00"));
        detail2.setUrbanExemptionCode("002");
        detail2.setUrbanExemptionAmount(BigDecimal.ZERO);
        detail2.setUrbanTaxPayable(new BigDecimal("63.00"));
        detail2.setEduTaxRate("3%");
        detail2.setEduTaxAmount(new BigDecimal("27.00"));
        detail2.setEduExemptionCode("003");
        detail2.setEduExemptionAmount(BigDecimal.ZERO);
        detail2.setEduTaxPayable(new BigDecimal("27.00"));
        detail2.setLocalEduTaxRate("2%");
        detail2.setLocalEduTaxAmount(new BigDecimal("18.00"));
        detail2.setLocalEduExemptionCode("004");
        detail2.setLocalEduExemptionAmount(BigDecimal.ZERO);
        detail2.setLocalEduTaxPayable(new BigDecimal("18.00"));
        detail2.setTaxPeriod("2025-07");
        detail2.setDeclareMonth("2025-08");
        details.add(detail2);

        vo.setDetails(details);

        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 测试新增
        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo);

        // 验证主记录
        assertNotNull(entity);
        assertNotNull(entity.getId());
        assertEquals(vo.getSupplierCorporationId(), entity.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), entity.getTaxPaymentPeriod());
        assertEquals(vo.getIncomeTaxMonth(), entity.getIncomeTaxMonth());

        // 等待异步处理完成
        waitForAsyncProcessing(entity.getId());

        // 重新查询获取最新数据
        ValueAddedTaxDeclareEntity updatedEntity = valueAddedTaxDeclareManager.getValueAddedTaxDeclareById(entity.getId());

        // 验证汇总数据
        assertEquals("2", updatedEntity.getTaxpayersCount()); // 2个人

        log.info("testAddValueAddedTaxDeclareWithDetails 主记录={}", JSONUtils.json(entity));

        // 验证明细记录
        List<ValueAddedTaxDetailEntity> savedDetails = valueAddedTaxDetailManager.queryDetailsByValueAddedTaxId(entity.getId());
        assertNotNull(savedDetails);
        assertEquals(2, savedDetails.size());

        // 验证明细数据
        ValueAddedTaxDetailEntity savedDetail1 = savedDetails.stream()
                .filter(d -> "张三".equals(d.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(savedDetail1);
        assertEquals(entity.getId(), savedDetail1.getValueAddedTaxId());
        assertEquals(new BigDecimal("10000.00"), savedDetail1.getTaxBasis());
        assertEquals(new BigDecimal("600.00"), savedDetail1.getVatAmount());
        assertEquals("2025-07", savedDetail1.getTaxPeriod());

        log.info("testAddValueAddedTaxDeclareWithDetails 明细记录={}", JSONUtils.json(savedDetails));
    }

    @Test
    public void testQueryValueAddedTaxDeclareWithDetails() {
        // 先创建一个带明细的申报记录
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        vo.setSupplierCorporationId(1L);
        vo.setTaxPaymentPeriod("2025-09");
        vo.setIncomeTaxMonth("2025-10");
        vo.setSupplierId(100L);

        // 准备明细数据
        List<ValueAddedTaxDetailVo> details = new ArrayList<>();

        // 明细1
        ValueAddedTaxDetailVo detail1 = new ValueAddedTaxDetailVo();
        detail1.setName("详情测试用户1");
        detail1.setIdCard("110101199001011111");
        detail1.setCertificateType("身份证");
        detail1.setTaxBasis(new BigDecimal("12000.00"));
        detail1.setTaxItem("软件开发服务");
        detail1.setTaxRate("6%");
        detail1.setVatAmount(new BigDecimal("720.00"));
        detail1.setVatPayable(new BigDecimal("720.00"));
        detail1.setUrbanTaxAmount(new BigDecimal("50.40"));
        detail1.setUrbanTaxPayable(new BigDecimal("50.40"));
        detail1.setEduTaxAmount(new BigDecimal("21.60"));
        detail1.setEduTaxPayable(new BigDecimal("21.60"));
        detail1.setLocalEduTaxAmount(new BigDecimal("14.40"));
        detail1.setLocalEduTaxPayable(new BigDecimal("14.40"));
        detail1.setTaxPeriod("2025-09");
        detail1.setDeclareMonth("2025-10");
        details.add(detail1);

        // 明细2
        ValueAddedTaxDetailVo detail2 = new ValueAddedTaxDetailVo();
        detail2.setName("详情测试用户2");
        detail2.setIdCard("110101199002022222");
        detail2.setCertificateType("身份证");
        detail2.setTaxBasis(new BigDecimal("20000.00"));
        detail2.setTaxItem("技术服务");
        detail2.setTaxRate("6%");
        detail2.setVatAmount(new BigDecimal("1200.00"));
        detail2.setVatPayable(new BigDecimal("1200.00"));
        detail2.setUrbanTaxAmount(new BigDecimal("84.00"));
        detail2.setUrbanTaxPayable(new BigDecimal("84.00"));
        detail2.setEduTaxAmount(new BigDecimal("36.00"));
        detail2.setEduTaxPayable(new BigDecimal("36.00"));
        detail2.setLocalEduTaxAmount(new BigDecimal("24.00"));
        detail2.setLocalEduTaxPayable(new BigDecimal("24.00"));
        detail2.setTaxPeriod("2025-09");
        detail2.setDeclareMonth("2025-10");
        details.add(detail2);

        vo.setDetails(details);

        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 新增申报记录
        ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo);
        assertNotNull(entity);
        assertNotNull(entity.getId());

        // 等待异步处理完成
        waitForAsyncProcessing(entity.getId());

        // 测试查询详情（包含明细）
        ValueAddedTaxDeclareVo queryResult = valueAddedTaxDeclareService.queryValueAddedTaxDeclare(entity.getId());

        // 验证主记录
        assertNotNull(queryResult);
        assertEquals(entity.getId(), queryResult.getId());
        assertEquals(vo.getSupplierCorporationId(), queryResult.getSupplierCorporationId());
        assertEquals(vo.getTaxPaymentPeriod(), queryResult.getTaxPaymentPeriod());
        assertEquals(vo.getIncomeTaxMonth(), queryResult.getIncomeTaxMonth());

        // 验证汇总数据
        assertEquals("2", queryResult.getTaxpayersCount()); // 2个人
//        assertEquals("32000.00", queryResult.getCurrentIncome()); // 12000 + 20000 = 32000
        assertEquals(new BigDecimal("1920.00"), queryResult.getTotalVatAmount()); // 720 + 1200 = 1920
        assertEquals(new BigDecimal("230.40"), queryResult.getTotalSurtaxAmount()); // (50.40+21.60+14.40) + (84.00+36.00+24.00) = 86.40 + 144.00 = 230.40

        // 验证明细数据
        assertNotNull(queryResult.getDetails());
        assertEquals(2, queryResult.getDetails().size());

        // 验证具体明细内容
        ValueAddedTaxDetailVo queryDetail1 = queryResult.getDetails().stream()
                .filter(d -> "详情测试用户1".equals(d.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(queryDetail1);
        assertEquals(new BigDecimal("12000.00"), queryDetail1.getTaxBasis());
        assertEquals(new BigDecimal("720.00"), queryDetail1.getVatAmount());

        log.info("testQueryValueAddedTaxDeclareWithDetails 查询结果={}", JSONUtils.json(queryResult));
        log.info("testQueryValueAddedTaxDeclareWithDetails 明细数量={}", queryResult.getDetails().size());
    }

    @Test
    public void testAddValueAddedTaxDeclareWithUniqueConstraint() {
        // 创建租户信息
        TenantInfo tenantInfo = TenantInfo.ofSupplier(1L);

        // 第一次添加申报记录
        ValueAddedTaxDeclareVo vo1 = new ValueAddedTaxDeclareVo();
        vo1.setSupplierCorporationId(2L);
        vo1.setTaxPaymentPeriod("2025-10");
        vo1.setIncomeTaxMonth("2025-11");
        vo1.setSupplierId(100L);

        // 准备第一次的明细数据
        List<ValueAddedTaxDetailVo> details1 = new ArrayList<>();
        ValueAddedTaxDetailVo detail1 = new ValueAddedTaxDetailVo();
        detail1.setName("第一次用户1");
        detail1.setIdCard("110101199001011111");
        detail1.setTaxBasis(new BigDecimal("8000.00"));
        detail1.setVatAmount(new BigDecimal("480.00"));
        detail1.setUrbanTaxAmount(new BigDecimal("33.60"));
        detail1.setEduTaxAmount(new BigDecimal("14.40"));
        detail1.setLocalEduTaxAmount(new BigDecimal("9.60"));
        detail1.setTaxPeriod("2025-10");
        detail1.setDeclareMonth("2025-11");
        details1.add(detail1);
        vo1.setDetails(details1);

        // 第一次新增
        ValueAddedTaxDeclareEntity entity1 = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo1);
        assertNotNull(entity1);
        assertNotNull(entity1.getId());

        // 等待异步处理完成
        waitForAsyncProcessing(entity1.getId());

        // 重新查询获取最新数据
        ValueAddedTaxDeclareEntity updatedEntity1 = valueAddedTaxDeclareManager.getValueAddedTaxDeclareById(entity1.getId());
        assertEquals("1", updatedEntity1.getTaxpayersCount());
//        assertEquals("8000.00", updatedEntity1.getCurrentIncome());

        Long firstEntityId = entity1.getId();
        log.info("第一次添加的申报记录ID: {}", firstEntityId);

        // 验证第一次的明细数据
        List<ValueAddedTaxDetailEntity> firstDetails = valueAddedTaxDetailManager.queryDetailsByValueAddedTaxId(firstEntityId);
        assertEquals(1, firstDetails.size());
        assertEquals("第一次用户1", firstDetails.get(0).getName());

        // 第二次添加相同的作业主体ID + 税款所属期的申报记录
        ValueAddedTaxDeclareVo vo2 = new ValueAddedTaxDeclareVo();
        vo2.setSupplierCorporationId(2L); // 相同的作业主体ID
        vo2.setTaxPaymentPeriod("2025-10"); // 相同的税款所属期
        vo2.setIncomeTaxMonth("2025-11");
        vo2.setSupplierId(100L);

        // 准备第二次的明细数据（不同的明细）
        List<ValueAddedTaxDetailVo> details2 = new ArrayList<>();
        ValueAddedTaxDetailVo detail2_1 = new ValueAddedTaxDetailVo();
        detail2_1.setName("第二次用户1");
        detail2_1.setIdCard("110101199002022222");
        detail2_1.setTaxBasis(new BigDecimal("10000.00"));
        detail2_1.setVatAmount(new BigDecimal("600.00"));
        detail2_1.setUrbanTaxAmount(new BigDecimal("42.00"));
        detail2_1.setEduTaxAmount(new BigDecimal("18.00"));
        detail2_1.setLocalEduTaxAmount(new BigDecimal("12.00"));
        detail2_1.setTaxPeriod("2025-10");
        detail2_1.setDeclareMonth("2025-11");
        details2.add(detail2_1);

        ValueAddedTaxDetailVo detail2_2 = new ValueAddedTaxDetailVo();
        detail2_2.setName("第二次用户2");
        detail2_2.setIdCard("110101199003033333");
        detail2_2.setTaxBasis(new BigDecimal("15000.00"));
        detail2_2.setVatAmount(new BigDecimal("900.00"));
        detail2_2.setUrbanTaxAmount(new BigDecimal("63.00"));
        detail2_2.setEduTaxAmount(new BigDecimal("27.00"));
        detail2_2.setLocalEduTaxAmount(new BigDecimal("18.00"));
        detail2_2.setTaxPeriod("2025-10");
        detail2_2.setDeclareMonth("2025-11");
        details2.add(detail2_2);

        vo2.setDetails(details2);

        // 第二次新增（应该删除第一次的记录并重新创建）
        ValueAddedTaxDeclareEntity entity2 = valueAddedTaxDeclareService.addValueAddedTaxDeclare(tenantInfo, vo2);
        assertNotNull(entity2);
        assertNotNull(entity2.getId());

        // 等待异步处理完成
        waitForAsyncProcessing(entity2.getId());

        // 重新查询获取最新数据
        ValueAddedTaxDeclareEntity updatedEntity2 = valueAddedTaxDeclareManager.getValueAddedTaxDeclareById(entity2.getId());

        // 验证新记录的统计数据
        assertEquals("2", updatedEntity2.getTaxpayersCount()); // 2个人
//        assertEquals("25000.00", updatedEntity2.getCurrentIncome()); // 10000 + 15000 = 25000

        Long secondEntityId = entity2.getId();
        log.info("第二次添加的申报记录ID: {}", secondEntityId);

        // 验证第一次的记录已被删除（查询应该抛出异常或返回null）
        try {
            ValueAddedTaxDeclareVo deletedVo = valueAddedTaxDeclareService.queryValueAddedTaxDeclare(firstEntityId);
            // 如果能查到，说明没有被删除，测试失败
            fail("第一次的申报记录应该已被删除");
        } catch (Exception e) {
            // 预期的异常，说明记录已被删除
            log.info("第一次的申报记录已被正确删除");
        }

        // 验证第一次的明细数据也已被删除
        List<ValueAddedTaxDetailEntity> deletedDetails = valueAddedTaxDetailManager.queryDetailsByValueAddedTaxId(firstEntityId);
        assertEquals(0, deletedDetails.size());

        // 验证第二次的明细数据
        List<ValueAddedTaxDetailEntity> secondDetails = valueAddedTaxDetailManager.queryDetailsByValueAddedTaxId(secondEntityId);
        assertEquals(2, secondDetails.size());

        // 验证明细数据内容
        boolean foundUser1 = secondDetails.stream().anyMatch(d -> "第二次用户1".equals(d.getName()));
        boolean foundUser2 = secondDetails.stream().anyMatch(d -> "第二次用户2".equals(d.getName()));
        assertTrue(foundUser1);
        assertTrue(foundUser2);

        log.info("testAddValueAddedTaxDeclareWithUniqueConstraint 测试通过");
    }

    /**
     * 等待异步处理完成
     */
    private void waitForAsyncProcessing(Long entityId) {
        int maxWaitTime = 10; // 最大等待10秒
        int waitInterval = 100; // 每100毫秒检查一次
        int maxAttempts = maxWaitTime * 1000 / waitInterval;

        for (int i = 0; i < maxAttempts; i++) {
            try {
                ValueAddedTaxDeclareEntity entity = valueAddedTaxDeclareManager.getValueAddedTaxDeclareById(entityId);
                if (PersonalIncomeTaxDeclareStatusEnum.GENERATED.name().equals(entity.getStatus())) {
                    log.info("异步处理完成，等待了 {} 毫秒", i * waitInterval);
                    return;
                }
                Thread.sleep(waitInterval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待异步处理被中断", e);
            } catch (Exception e) {
                log.warn("检查异步处理状态时出错: {}", e.getMessage());
            }
        }

        log.warn("异步处理超时，等待了 {} 秒", maxWaitTime);
        // 不抛出异常，让测试继续执行，可能异步处理只是比较慢
    }
}
