package com.olading.operate.labor.app.provider;

import cn.hutool.json.JSONUtil;
import com.lanmaoly.cloud.archive.ArchiveInfo;
import com.lanmaoly.cloud.archive.PersistParameter;
import com.olading.basic.rpc.api.BasicRpcService;
import com.olading.basic.rpc.api.ErrorCode;
import com.olading.basic.rpc.api.SmsSendRequest;
import com.olading.basic.rpc.api.SmsSendResponse;
import com.olading.boot.core.business.BusinessException;
import com.olading.boot.core.spi.FileStorageProvider;
import com.olading.operate.labor.AppProperties;
import com.olading.operate.labor.domain.share.otp.NSmsProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class OladingEnvironmentProvider implements FileStorageProvider, NSmsProvider {

    private final DefaultArchiveService archiveService;
    private final BasicRpcService basicRpcService;
    private final AppProperties properties;

    @Override
    public String writeFile(String id, String fileName, InputStream input) {

        ArchiveInfo info = new ArchiveInfo(fileName, "");

        PersistParameter persistParameter = new PersistParameter();
        persistParameter.setInputStream(input);
        persistParameter.setInfo(info);
        return archiveService.persist(persistParameter);
    }

    @Override
    public void readFile(String id, OutputStream output) {
        archiveService.open(id, output);
    }

    @Override
    public void deleteFile(String id) {
        archiveService.delete(id);
    }

    @Override
    public void sendOtp(String receiver, String challenge, String businessId, String sign) {
        Map<String, String> parameters = Map.of("verifyCode", challenge);
        sendSms(receiver, parameters, businessId, sign);
    }

    @Override
    public void sendSms(String cellphone, Map<String, String> parameters, String businessId, String sign) {
        if (properties.isMock()) {
            log.info("发送短信， cellphone={}, message={}, businessId={}, sign={}", cellphone, parameters, businessId, sign);
            return;
        }
        SmsSendRequest request = new SmsSendRequest();
        request.setBusinessId(businessId);
        request.setParameters(parameters);
        request.setReceiver(cellphone);
        request.setSign(sign);
        final SmsSendResponse smsSendResponse = basicRpcService.smsSend(request);
        if(smsSendResponse.getHeader().getErrorCode() != ErrorCode.OK){
            log.error("发送短信失败, resp={}", JSONUtil.toJsonStr(smsSendResponse));
            throw new BusinessException("发送短信失败"+smsSendResponse.getHeader().getErrorMessage());
        }
    }
}
