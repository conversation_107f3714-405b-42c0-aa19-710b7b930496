package com.olading.operate.labor.domain.share.signing.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Schema(description= "授权请求参数")
public class AuthorizeRequest extends BaseRequest<AuthorizeRequest> {


    @Schema(description = "需要访问的资源，例如：sig/tpl/12345", required = true)
    private String resource;
    @Schema(description = "客户自有系统的用户编号", required = true)
    private String user;
}
