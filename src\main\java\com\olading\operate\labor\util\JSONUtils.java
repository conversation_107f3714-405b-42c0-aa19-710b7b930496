package com.olading.operate.labor.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

public class JSONUtils {

    private static ObjectMapper mapper;

    static {
        mapper = new ObjectMapper();
        /*mapper.registerModules(new CustomModule());*/
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        JavaTimeModule timeModule = new JavaTimeModule();
        timeModule.addSerializer(LocalDateTime.class, new JsonSerializer<LocalDateTime>(){
            @Override
            public void serialize(LocalDateTime localDateTime, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                jsonGenerator.writeNumber(localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
            }
        });
        mapper.registerModule(timeModule);

        JavaTimeModule timeModule1 = new JavaTimeModule();
        timeModule.addDeserializer(LocalDateTime.class,  new JsonDeserializer<LocalDateTime>(){

            @Override
            public LocalDateTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
                Long timestamp = jsonParser.getLongValue();
                return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8));
            }
        });
        mapper.registerModule(timeModule1);
    }

    public static String json(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return mapper.writeValueAsString(object);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public static String jsonFormat(Object object) {
        try {
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T deserialize(String content, Class<?> c) {
        try {
            return mapper.readValue(content, constructType(c));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> deserializeList(String content, Class<T> classes) {
        try {

            return mapper.readValue(content, mapper.getTypeFactory().constructParametricType(ArrayList.class, classes));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T deserialize(String content, JavaType type) {
        try {
            return mapper.readValue(content, type);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T deserialize(String content, TypeReference<T> jsonTypeReference) {
        try {
            return mapper.readValue(content, jsonTypeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static JavaType constructType(Class<?> c) {
        return mapper.getTypeFactory().constructType(c);
    }

    public static JavaType constructParametricType(Class<?> c, Class<?>... classes) {
        return mapper.getTypeFactory().constructParametricType(c, classes);
    }

    public static JsonNode readTree(String content){
        try {
            return mapper.readTree(content);
        } catch (IOException e) {
           throw new RuntimeException(e);
        }
    }
}
