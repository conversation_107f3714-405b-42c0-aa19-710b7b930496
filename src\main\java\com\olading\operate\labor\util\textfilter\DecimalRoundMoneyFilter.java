package com.olading.operate.labor.util.textfilter;


import com.lanmaoly.util.lang.ValidateUtils;

import java.math.BigDecimal;

/**
 * 金额最多保留两位数，四舍五入
 * <AUTHOR>
 * @date 2022/2/22 11:36
 */
public class DecimalRoundMoneyFilter implements TextFilter {
    @Override
    public String filter(String text) {
        if (null == text) {
            return null;
        }

        try {
            ValidateUtils.checkMoney(text, false);
            BigDecimal bigDecimal = new BigDecimal(text.replace(",", ""));
            return bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        } catch (Exception e) {
            return text;
        }
    }
}
