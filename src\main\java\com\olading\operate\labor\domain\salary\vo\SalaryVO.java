package com.olading.operate.labor.domain.salary.vo;

import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SalaryVO {

    @Schema(description = "工资表ID")
    private Long id;

    @Schema(description = "客户名称ID")
    private Long customerId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "服务合同合同名称ID")
    private Long contractId;

    @Schema(description = "服务合同合同名称")
    private String contractName;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "税款所属期（格式：2025-06）")
    private String taxPeriod;

    @Schema(description = "总人数")
    private Long totalPeople;

    @Schema(description = "应发金额总计")
    private BigDecimal totalPayable;

    @Schema(description = "个税预缴额总计")
    private BigDecimal totalIncomeTax;

    @Schema(description = "增值税应纳税额总计")
    private BigDecimal totalVat;

    @Schema(description = "附加税应纳税额总计")
    private BigDecimal totalSurtax;

    @Schema(description = "实发金额总计")
    private BigDecimal netPaymentTotal;

    @Schema(description = "个税申报月（格式：2025-07）")
    private String taxDeclarationMonth;

    @Schema(description = "状态（CALCULATING=算税中,UNCONFIRMED=待确认,CONFIRMED=已确认）")
    private SalaryStatementStatus status;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "服务商名称ID")
    private Long supplierId;
}
