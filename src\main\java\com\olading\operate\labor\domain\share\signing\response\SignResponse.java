package com.olading.operate.labor.domain.share.signing.response;

import com.olading.operate.labor.domain.share.signing.enums.SignStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "签名响应参数")
public class SignResponse {

    @Schema(description = "签名状态")
    private SignStatus status;
}
