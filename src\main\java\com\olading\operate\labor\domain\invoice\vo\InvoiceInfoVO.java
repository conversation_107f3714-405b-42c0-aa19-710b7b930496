package com.olading.operate.labor.domain.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "发票信息")
public class InvoiceInfoVO {
    
    @Schema(description = "发票抬头")
    private String title;
    
    @Schema(description = "纳税识别号")
    private String taxNo;
    
    @Schema(description = "开户行")
    private String bankName;
    
    @Schema(description = "银行账号")
    private String bankAccount;
    
    @Schema(description = "注册地址")
    private String registerAddress;
    
    @Schema(description = "企业电话")
    private String companyTel;
    
    @Schema(description = "发票备注")
    private String remark;
    
    @Schema(description = "可用发票类目列表")
    private List<String> availableCategories;
}