package com.olading.operate.labor.app.web.biz.protocol.vo;

import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(description="模板填充域")
@Getter
@Setter
public class TemplateFiled {

    @Schema(description = "字段名（用户设置）")
    private String fieldName;

    @Schema(description = "关联项编码")
    private String relationCode;

    @Schema(description = "域类型 SEAL-企业签章 SIGN-个人签章 DATE-日期 FIELD-填充域")
    private String fieldType;

    @Schema(description = "关联项名")
    private String relationName;

    @Schema(description = "签署方类型 SEAL-企业签署方 SIGN-个人签署方")
    private EnumOperateType signatory;

    public TemplateFiled(String fieldName, String relationCode, String fieldType, EnumOperateType signatory) {
        this.fieldName = fieldName;
        this.relationCode = relationCode;
        this.fieldType = fieldType;
        this.signatory = signatory;
    }

    public TemplateFiled() {
    }
}
