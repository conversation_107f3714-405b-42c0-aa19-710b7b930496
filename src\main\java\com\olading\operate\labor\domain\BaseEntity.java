package com.olading.operate.labor.domain;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

@EntityListeners(EntityHook.class)
@MappedSuperclass
public abstract class BaseEntity {

    @Comment("创建时间")
    @Column(name = "create_time")
    protected LocalDateTime createTime;

    @Comment("更新时间")
    @Column(name = "modify_time")
    protected LocalDateTime modifyTime;

    @Comment("版本")
    @Version
    @Column(name = "version", nullable = false)
    private Integer version;

    @Comment("租户编号")
    @Column(name = "tenant_id", length = 20)
    private String tenantId;

    @Comment("是否已删除")
    @Column(name = "deleted")
    private Boolean deleted;

    @Comment("删除时间")
    @Column(name = "deleted_time")
    private LocalDateTime deletedTime;

    public BaseEntity() {
        createTime = LocalDateTime.now();
        modifyTime = createTime;
        deleted = false;
    }

    public Integer getVersion() {
        return version;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public TenantInfo getTenant() {
        if (tenantId == null) {
            return null;
        }
        return TenantInfo.parse(tenantId);
    }

    protected void setTenant(TenantInfo tenant) {
        this.tenantId = tenant.toTenantId();
    }

    public boolean isDeleted() {
        return deleted != null ? deleted : false;
    }

    public LocalDateTime getDeletedTime() {
        return deletedTime;
    }

    public void delete() {
        if (this.deleted == null || !this.deleted) {
            this.deleted = true;
            deletedTime = LocalDateTime.now();
        }
    }

}
