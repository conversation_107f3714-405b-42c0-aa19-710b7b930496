package com.olading.operate.labor.util.validation.constraints;

import com.olading.operate.labor.util.excel.ExcelConstraint;
import com.olading.operate.labor.util.textfilter.RemoveCommaFilter;
import com.olading.operate.labor.util.textfilter.RemoveEnterBlankFilter;
import com.olading.operate.labor.util.textfilter.RemoveMoneySymbolsFilter;
import com.olading.operate.labor.util.textfilter.TrimQuoteFilter;
import com.olading.operate.labor.util.validation.validator.MoneyValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数值校验
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        MoneyValidator.class
})
@ExcelConstraint(
        width = 12,
        filters = {
                RemoveEnterBlankFilter.class,
                TrimQuoteFilter.class,
                RemoveMoneySymbolsFilter.class
        },
        formatters = {
                RemoveCommaFilter.class
        }
)
public @interface Money {
    boolean required() default false;

    String label() default "";

    /**
     * 最大值
     * @return
     */
    double max() default 999999999;

    /**
     * 最小值
     * @return
     */
    double min() default 0;

    /**
     * 最多保留2位，多余的四舍五入
     * @return
     */
    boolean decimalRound() default true;

    /**
     * 严格模式（小数位后最多2位）
     * @return
     */
    boolean strictMode() default false;

    String message() default "";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
