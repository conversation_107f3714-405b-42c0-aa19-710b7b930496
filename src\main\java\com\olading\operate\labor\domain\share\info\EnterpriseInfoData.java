package com.olading.operate.labor.domain.share.info;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class EnterpriseInfoData {

    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "联系人")
    private String contacts;

    @Schema(description = "联系手机号")
    private String contactPhone;

    private String remark;

    private String socialCreditCode;

    @Schema(description = "营业执照图片")
    private String businessLicenseImage;

    @Schema(description = "注册地址")
    private String registerAddress;

    @Schema(description = "法定代表人证件正面照")
    private String certificateFrontImage;

    @Schema(description = "法定代表人证件背面照")
    private String certificateBackImage;

    @Schema(description = "法定代表人姓名")
    private String representativeName;

    @Schema(description = "法定代表人证件类型")
    private CertificateTypeEnum certificateType;

    @Schema(description = "法定代表人证件号")
    private String certificateNo;

    @Schema(description = "是否禁用")
    private Boolean disabled;

    public EnterpriseInfoEntity convert(){
        EnterpriseInfoEntity entity = new EnterpriseInfoEntity();
        entity.setName(name);
        entity.setSocialCreditCode(socialCreditCode);
        entity.setRegisteredAddress(registerAddress);
        entity.setCertificateBackImage(certificateBackImage);
        entity.setCertificateFrontImage(certificateFrontImage);
        entity.setRepresentativeName(representativeName);
        entity.setCertificateType(certificateType);
        entity.setCertificateNo(certificateNo);
        entity.setSocialCreditCode(socialCreditCode);
        entity.setBusinessLicenseImage(businessLicenseImage);
        entity.setAttachments(attachments);
        entity.setContacts(contacts);
        entity.setContactPhone(contactPhone);
        entity.setRemark(remark);
        return entity;

    }

    private List<String> attachments = new ArrayList<>();

    public EnterpriseInfoData(EnterpriseInfoEntity entity) {
        this.name = entity.getName();
        this.contacts = entity.getContacts();
        this.contactPhone = entity.getContactPhone();
        this.remark = entity.getRemark();
        this.attachments = entity.getAttachments();
        this.socialCreditCode = entity.getSocialCreditCode();
    }

    public EnterpriseInfoData() {
    }
}
