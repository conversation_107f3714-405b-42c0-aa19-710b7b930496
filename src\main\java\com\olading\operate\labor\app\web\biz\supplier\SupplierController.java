package com.olading.operate.labor.app.web.biz.supplier;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.boot.core.business.BusinessException;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.component.captcha.CaptchaGenerator;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.core.security.AuthorizationManager;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.PublicController;
import com.olading.operate.labor.app.web.biz.boss.BossController;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.otp.Otp;
import com.olading.operate.labor.domain.share.otp.OtpManager;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.domain.supplier.SmsBusinessType;
import com.olading.operate.labor.domain.supplier.SupplierData;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierManager;
import com.olading.operate.labor.domain.supplier.SupplierSmsTemplateEntity;
import com.olading.operate.labor.util.crypto.HashPassword;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "服务商接口")
@RestController
@RequestMapping("/api/supplier")
@RequiredArgsConstructor
@Slf4j
public class SupplierController extends BusinessController {

    private final OtpManager otpManager;
    private final SupplierService supplierService;
    private final AuthorizationManager authorizationManager;
    private final UserManager userManager;;
    private final SupplierManager supplierManager;
    private final CaptchaGenerator captchaGenerator;




    @Operation(summary = "更新token")
    @PostMapping(value = "renewToken")
    public WebApiResponse<RenewTokenResponse> renewToken(@RequestBody RenewTokenRequest request) {

        List<SupplierEntity> list = supplierService.getUserAvailableSupplier(currentUserId());
        if (list.isEmpty()) {
            throw new BusinessException("无可进入企业");
        }
        if (list.stream().noneMatch(o -> o.getId().equals(request.getSupplierId()))) {
            throw new BusinessException("不可切换");
        }

        supplierService.useSupplierMember(request.getSupplierId(), currentUserId());

        String token = authorizationManager.authorize(String.valueOf(currentUserId()), new TenantInfo(TenantInfo.TenantType.SUPPLIER, request.getSupplierId().toString()).toGeneral());
        RenewTokenResponse response = new RenewTokenResponse();
        response.setToken(token);
        return WebApiResponse.success(response);
    }

    @Operation(summary = "变更管理员")
    @PostMapping(value = "changeAdmin")
    public WebApiResponse<Void> changeAdmin(@RequestBody ChangeAdminRequest request) {

        if (!isAdmin()) {
            throw new BusinessException("不是管理员");
        }
        if (!otpManager.verify(currentUser().getCellphone(), request.getOtpToken(), request.getPassword())) {
            throw new BusinessException("短信验证码不正确");
        }
        if (supplierService.IsDisabled(currentSupplierId())) {
            throw new BusinessException("无权限");
        }

        supplierService.setAdmin(currentSupplierId(), request.getUserId());
        return WebApiQueryResponse.success();
    }

    @Operation(summary = "修改服务商")
    @PostMapping(value = "editSupplier")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_PROFILE)
    public WebApiResponse<Void> editSupplier(@Valid @RequestBody BossController.SupplierDetailVo request) {
        supplierService.editSupplier(SupplierData.toSupplierData(request));
        return WebApiResponse.success();
    }

    @Operation(summary = "查询服务商")
    @PostMapping(value = "supplierDetail")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_PROFILE)
    public WebApiResponse<SupplierData> supplierDetail() {
        final SupplierData supplierData = supplierService.requireSupplier(currentSupplierId());
        return WebApiResponse.success(supplierData);
    }

    @Operation(summary = "短信配置")
    @PostMapping(value = "setSmsConfig")
    public WebApiResponse<Void> setSmsConfig(@RequestBody @Valid  SetSmsConfigRequest request) {
        supplierService.setSmsConfig(currentSupplierId(), request.getBusinessType(), request.getTemplateCode());
        return WebApiResponse.success();
    }

    @Operation(summary = "短信配置查询")
    @PostMapping(value = "getSmsConfig")
    public WebApiResponse<List<SupplierSmsTemplateEntity>> getSmsConfig() {
        return WebApiResponse.success(supplierService.getSmsConfig(currentSupplierId()));
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_PROFILE)
    @Operation(summary = "重置用户登录密码")
    @PostMapping(value = "resetPassword")
    public WebApiResponse<Void> resetCustomerPassword(@RequestBody @Valid  ResetCustomerPasswordRequest request) {
        if (!request.isPasswordMatch()) {
            return WebApiResponse.fail("1", "两次输入密码不一致");
        }
        userManager.changePassword(currentUserId(), request.getOldPassword(), request.getConfirmPassword());
        return WebApiQueryResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_PROFILE)
    @Operation(summary = "设置用户登录密码")
    @PostMapping(value = "setPassword")
    public WebApiResponse<Void> setPassword(@RequestBody @Valid  SetCustomerPasswordRequest request) {
        if (!request.isPasswordMatch()) {
            return WebApiResponse.fail("1", "两次输入密码不一致");
        }
        final UserEntity user = currentUser();
        if(!otpManager.verify(user.getCellphone(), request.getOtpToken(), request.getCode())){
            return WebApiResponse.fail("1", "验证码不正确");
        }
        userManager.initPassword(currentUserId(), request.getConfirmPassword());
        return WebApiQueryResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_PROFILE)
    @Operation(summary = "换绑用户手机号-验证原手机号验证码")
    @PostMapping(value = "validateOldPhone")
    public WebApiResponse<String> validateOldPhone(@RequestBody @Valid validateOldPhoneRequest request) {
        final UserEntity user = userManager.getUser(currentUserId());
        if(!otpManager.verify(user.getCellphone(), request.getOtpToken(), request.getCode())){
            return WebApiResponse.fail("1", "验证码不正确");
        }
        return WebApiResponse.success(HashPassword.hashPassword(user.getCellphone()+user.getId()));
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_PROFILE)
    @Operation(summary = "换绑用户手机号-新手机号发送验证码")
    @PostMapping(value = "sendNewPhone")
    public WebApiResponse<PublicController.CreateOtpResponse> sendOtp(@Valid @RequestBody SupCreateOtpRequest request) {
        final SupplierEntity supplierEntity = currentSupplier();
        final UserEntity user = currentUser();
        final SupplierSmsTemplateEntity supplierSmsTemplateEntity = supplierManager.requireSupplierSmsTemplate(supplierEntity.getId(), SmsBusinessType.OLD_land_diy);
        captchaGenerator.verify(request.getCaptchaAnswer(), request.getCaptchaToken());
        if(!HashPassword.hashPassword(user.getCellphone()+user.getId()).equals(request.getAdditionalData())){
            return WebApiResponse.fail("1", "提交信息不正确");
        }
        Otp otp = otpManager.send(currentUser().getCellphone(),supplierSmsTemplateEntity.getTemplateCode(),supplierEntity.getSignatureCode());
        PublicController.CreateOtpResponse response = new PublicController.CreateOtpResponse();
        response.setToken(otp.getToken());
        return WebApiResponse.success(response);
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_PROFILE)
    @Operation(summary = "换绑用户手机号")
    @PostMapping(value = "changeUserPhone")
    public WebApiResponse<Void> changeUserPhone(@RequestBody @Valid ChangeUserPhoneRequest request) {
        final UserEntity user = userManager.getUser(currentUserId());
        if(!otpManager.verify(request.getPhone(), request.getOtpToken(), request.getCode())){
            return WebApiResponse.fail("1", "验证码不正确");
        }
        if(!HashPassword.hashPassword(user.getCellphone()+user.getId()).equals(request.getAdditionalData())){
            return WebApiResponse.fail("1", "提交信息不正确");
        }
        supplierService.changePhone(currentSupplier().getId(), currentUserId(), request.getPhone());
        return WebApiResponse.success();
    }




    private boolean isPhoneNumber(String keywords) {
        return keywords.matches("[0-9\\-\\+\\s]+");
    }



    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class AuthorityTree {
        private String title;
        private String authority;
        private List<AuthorityTree> children;
    }









    @Data
    public static class UserVo {
        private Long id;
        private String realName;
    }






    @Data
    public static class ChangeAdminRequest {

        @NotNull
        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "短信验证码内容")
        @NotEmpty
        private String password;

        @Schema(description = "验证码token")
        @NotEmpty
        private String otpToken;
    }


    @Data
    public static class RenewTokenRequest {
        @Schema(description = "切换到哪个服务商")
        private Long supplierId;
    }

    @Data
    public static class RenewTokenResponse {
        private String token;
    }


    @Data
    public static class validateOldPhoneRequest {
        @Schema(description = "短信验证码")
        @NotBlank(message = "短信验证码不能为空")
        private String code;

        @Schema(description = "验证码token")
        @NotBlank(message = "验证码token不能为空")
        private String otpToken;
    }

    @Data
    public static class SetSmsConfigRequest {

        @Schema(description = "短信业务类型")
        @NotNull(message = "短信业务类型不能为空")
        private SmsBusinessType businessType;

        @Schema(description = "短信模板编码")
        @NotBlank(message = "短信模板编码不能为空")
        private String templateCode;

    }

    @Data
    public static class ChangeUserPhoneRequest {
        @Schema(description = "短信验证码")
        @NotBlank(message = "短信验证码不能为空")
        private String code;

        @Schema(description = "验证码token")
        @NotBlank(message = "验证码token不能为空")
        private String otpToken;

        @Schema(description = "手机号")
        @NotBlank(message = "手机号不能为空")
        private String phone;

        @Schema(description = "附加信息")
        @NotBlank(message = "附加信息码不能为空")
        private String additionalData;
    }

    @Data
    public static class SetCustomerPasswordRequest {

        @Schema(description = "短信验证码")
        @NotBlank(message = "短信验证码不能为空")
        private String code;

        @Schema(description = "验证码token")
        @NotBlank(message = "验证码token不能为空")
        private String otpToken;

        @Schema(description = "密码", example = "Password123")
        @NotBlank(message = "密码不能为空")
        //包含字母、符号、数字 6-20位
        @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]).{6,20}$", message = "密码必须包含字母、符号、数字")
        private String password;

        @Schema(description = "确认密码", example = "Password123")
        @NotBlank(message = "确认密码不能为空")
        private String confirmPassword;

        public boolean isPasswordMatch() {
            return password != null && password.equals(confirmPassword);
        }

    }

    @Data
    public static class ResetCustomerPasswordRequest {

        @Schema(description = "原密码", example = "Password123")
        @NotBlank(message = "密码不能为空")
        @Size(min = 6, message = "密码长度至少为6个字符")
        private String oldPassword;

        @Schema(description = "密码", example = "Password123")
        @NotBlank(message = "密码不能为空")
        //包含字母、符号、数字 6-20位
        @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]).{6,20}$", message = "密码必须包含字母、符号、数字")
        private String password;

        @Schema(description = "确认密码", example = "Password123")
        @NotBlank(message = "确认密码不能为空")
        private String confirmPassword;

        public boolean isPasswordMatch() {
            return password != null && password.equals(confirmPassword);
        }

    }

    @Data
    public static class SupCreateOtpRequest {

        @NotNull
        @Schema(description = "图形验证码token")
        private String captchaToken;

        @NotNull
        @Schema(description = "图形验证码")
        private String captchaAnswer;

        @NotNull
        @Schema(description = "新手机号")
        private String cellphone;

        @Schema(description = "附加信息")
        @NotBlank(message = "附加信息码不能为空")
        private String additionalData;
    }

}
