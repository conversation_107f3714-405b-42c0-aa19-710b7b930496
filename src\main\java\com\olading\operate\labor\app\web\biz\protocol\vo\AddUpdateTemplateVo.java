package com.olading.operate.labor.app.web.biz.protocol.vo;

import com.olading.operate.labor.domain.ApiException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class AddUpdateTemplateVo {

    private static final int TEMPLATE_NAME_LENGTH_LIMIT = 64;

    @Schema(description = "文件ID")
    private String archiveId;
//    @Schema(description = "文档信息")
//    private FileInfo archiveInfo;

    @Schema(description = "模板id")
    private Long tempId;

    @Schema(description = "模板名")
    private String templateName;

    @Schema(description = "模板类型")
    private String templateType;

    @Schema(description = "使用范围（作业主体id列表）")
    private List<Long> corporationIds;

    @Schema(description = "模板步骤")
    private List<TemplateStep> steps;


    public void doValidate() {
        if (StringUtils.isBlank(templateName)) throw new ApiException("请输入模板名称", ApiException.SYSTEM_ERROR);
        if (templateName.length() > TEMPLATE_NAME_LENGTH_LIMIT) throw new ApiException("模板名称字数不超过64", ApiException.SYSTEM_ERROR);
        if (CollectionUtils.isEmpty(corporationIds)) throw new ApiException("请选择模板使用范围", ApiException.SYSTEM_ERROR);
    }

}
