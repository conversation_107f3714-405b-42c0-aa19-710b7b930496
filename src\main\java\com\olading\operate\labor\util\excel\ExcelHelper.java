package com.olading.operate.labor.util.excel;

import com.olading.operate.labor.util.textfilter.DecimalRoundMoneyFilter;
import com.olading.operate.labor.util.textfilter.FilterHelper;
import com.olading.operate.labor.util.textfilter.RemoveBlankFilter;
import com.olading.operate.labor.util.textfilter.TextFilter;
import com.olading.operate.labor.util.textfilter.TrimNameFilter;
import com.olading.operate.labor.util.textfilter.TrimQuoteFilter;
import com.olading.operate.labor.util.validation.constraints.Date;
import com.olading.operate.labor.util.validation.constraints.DateTime;
import com.olading.operate.labor.util.validation.constraints.Money;
import com.olading.operate.labor.util.validation.constraints.Time;
import com.olading.operate.labor.util.validation.validator.RequiredValidator;
import com.olading.operate.labor.util.validation.validator.Validator;
import com.lanmaoly.util.lang.ConvertUtils;
import com.lanmaoly.util.lang.ReflectionUtils;
import com.lanmaoly.util.lang.StringUtils;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import org.apache.commons.collections4.CollectionUtils;


import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/8 13:39
 */
public class ExcelHelper {

    public static List<ExcelColumnEntity> getColumnsInOrder(Class<? extends ExcelRow> pojoClass) {
        Map<String, ExcelColumnEntity> columnEntityMap = getColumnMap(pojoClass);
        return columnEntityMap.values()
                .stream()
                .sorted(Comparator.comparing(ExcelColumnEntity::getColIndex))
                .collect(Collectors.toList());
    }

    public static Map<String, ExcelColumnEntity> getColumnMap(Class<? extends ExcelRow> pojoClass) {
        List<Field> fields = ReflectionUtils.getDeclaredFieldsWithSuperclass(pojoClass);
        Map<String, ExcelColumnEntity> columnEntityMap = new HashMap<>();

        int sortNum = 0;
        Excel excel = pojoClass.getAnnotation(Excel.class);
        for (Field field : fields) {
            ExcelColumn column = field.getAnnotation(ExcelColumn.class);
            if (null != column) {
                Set<Annotation> annotations = new HashSet<>(Arrays.asList(field.getAnnotations()));
                String dateFormat = getDateFormat(annotations);

                ExcelColumnEntity excelColumn = new ExcelColumnEntity();
                excelColumn.setGroup(column.group());
                excelColumn.setName(column.name());
                excelColumn.setNullWriteValue(column.nullWriteValue());
                excelColumn.setSaveToOther(column.saveToOther());
                excelColumn.setField(field);
                excelColumn.setRequired(column.required());
                excelColumn.setUnique(column.unique());
                excelColumn.setOptions(column.options());
                excelColumn.setComment(column.comment());
                excelColumn.setValidationFormula(column.validationFormula());

                if (column.width() > 0) {
                    excelColumn.setWidth(column.width());
                } else {
                    excelColumn.setWidth(getDefaultColumnWidth(annotations));
                }

                if (null == excel || ExcelColumnOrder.ORDER_BY_FIELD == excel.writeColumnOrder()) {
                    excelColumn.setColIndex(sortNum);
                } else if (column.orderNum() > 0) {
                    excelColumn.setColIndex(column.orderNum() - 1);
                }

                if (null != dateFormat) {
                    excelColumn.setDateFormat(dateFormat);
                }

                if (column.filters().length > 0) {
                    excelColumn.setFilters(Arrays.asList(column.filters()));
                } else {
                    excelColumn.setFilters(getDefaultFilters(annotations));
                }

                if (column.formatters().length > 0) {
                    excelColumn.setFormatters(Arrays.asList(column.formatters()));
                } else {
                    excelColumn.setFormatters(getDefaultFormatters(annotations));
                }

                List<Validator> validators;
                if (column.validators().length > 0) {
                    // ExcelColumn 配置
                    validators = new ArrayList<>();
                    try {
                        for (Class<? extends Validator> validatorClass: column.validators()) {
                            Validator validator = validatorClass.newInstance();
                            validator.setName(column.name());
                            validator.setRequired(column.required());
                            validators.add(validator);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e.getMessage());
                    }
                } else {
                    // Date,Email,Mobile...配置
                    validators = getDefaultValidators(annotations, column);
                }

                if (column.required() && CollectionUtils.isEmpty(validators)) {
                    Validator requireValidator = new RequiredValidator(column.name());
                    requireValidator.setRequired(true);
                    validators = Arrays.asList(requireValidator);
                }

                excelColumn.setValidators(validators);

                columnEntityMap.put(excelColumn.getName(), excelColumn);

                sortNum++;
            }
        }

        return columnEntityMap;
    }

    /**
     * 创建默认的column
     * @return
     */
    public static ExcelColumnEntity createDefaultColumn(boolean enableFilter)
    {
        Set<Annotation> annotations = new HashSet<>();
        ExcelColumnEntity excelColumn = new ExcelColumnEntity();
        excelColumn.setWidth(getDefaultColumnWidth(annotations));
        if (enableFilter) {
            excelColumn.setFilters(getDefaultFilters(annotations));
        }

        //excelColumn.setFormatters(getDefaultFormatters(annotations));
        //excelColumn.setValidators(getDefaultValidators(annotations, false));

        return excelColumn;
    }

    /**
     * 对Excel输入文本进行过滤处理，对外单独提供的方法，目的是保持与Excel过滤规则一致，
     * 一般是业务系统单独需要对字段过滤时使用。
     * @param value
     * @param type
     * @return
     */
    public static String filterText(Object value, Class<? extends Annotation> type)
    {
        if (null == value) {
            return null;
        }

        String text = value.toString();
        if (StringUtils.isBlank(text)) {
            return "";
        }
        
        List<Class<? extends TextFilter>> filters = new ArrayList<>();
        if (null == type) {
            filters.addAll(Arrays.asList(RemoveBlankFilter.class));
        } else {
            ExcelConstraint constraint = type.getAnnotation(ExcelConstraint.class);
            if (null != constraint) {
                filters.addAll(Arrays.asList(constraint.filters()));
            } else {
                filters.addAll(Arrays.asList(RemoveBlankFilter.class));
            }
        }

        for (Class<? extends TextFilter> filter: filters) {
            text = FilterHelper.invokeTextFilter(filter, text);
        }

        if (Date.class.equals(type) || DateTime.class.equals(type)
            || Time.class.equals(type)) {
            // 时间日期，尝试规整为标准格式
            try {
                Object datetime = ConvertUtils.tryConvertStringToDate(text, true);
                text = String.valueOf(datetime).replace('T', ' ');
            } catch (Exception e) {
                // 非日期格式，不做处理
            }
        }

        return text;
    }

    private static String getDateFormat(Set<Annotation> annotations) {
        for (Annotation annotation: annotations) {
            Class<? extends Annotation> clazz = annotation.annotationType();
            if (DateTime.class.equals(clazz)) {
                return ((DateTime)annotation).format();
            } else if (Date.class.equals(clazz)) {
                return ((Date)annotation).format();
            } else if (Time.class.equals(clazz)) {
                return ((Time)annotation).format();
            }
        }

        return null;
    }

    private static int getDefaultColumnWidth(Set<Annotation> annotations) {
        for (Annotation annotation: annotations) {
            ExcelConstraint constraint = annotation.annotationType().getAnnotation(ExcelConstraint.class);
            if (null != constraint) {
                return constraint.width();
            }
        }

        return 12;
    }

    private static List<Class<? extends TextFilter>> getDefaultFilters(Set<Annotation> annotations) {
        for (Annotation annotation: annotations) {
            ExcelConstraint constraint = annotation.annotationType().getAnnotation(ExcelConstraint.class);
            if (null != constraint) {
                return Arrays.asList(constraint.filters());
            }
        }

        // 默认去掉换行、收尾空格、引号
        return Arrays.asList(TrimNameFilter.class,
                TrimQuoteFilter.class,
                TrimNameFilter.class);
    }

    private static List<Class<? extends TextFilter>> getDefaultFormatters(Set<Annotation> annotations) {
        for (Annotation annotation: annotations) {
            ExcelConstraint constraint = annotation.annotationType().getAnnotation(ExcelConstraint.class);
            if (null != constraint) {
                List<Class<? extends TextFilter>> filters = new ArrayList<>();
                filters.addAll(Arrays.asList(constraint.formatters()));

                Class<? extends Annotation> clazz = annotation.annotationType();
                if (Money.class.equals(clazz) && ((Money)annotation).decimalRound()) {
                    // 特殊处理，四舍五入
                    filters.add(DecimalRoundMoneyFilter.class);
                }

                return filters;
            }
        }

        return null;
    }

    private static List<Validator> getDefaultValidators(Set<Annotation> annotations, ExcelColumn column) {
        List<Validator> validators = new ArrayList<>();
        for (Annotation annotation: annotations) {
            Constraint constraint = annotation.annotationType().getAnnotation(Constraint.class);
            if (null == constraint) {
                continue;
            }

            Class<? extends ConstraintValidator<?, ?>>[] validatedBy = constraint.validatedBy();
            if (null != validatedBy && validatedBy.length > 0) {
                for (Class<? extends ConstraintValidator<?, ?>> validatorClass: validatedBy) {
                    try {
                        ConstraintValidator validator = validatorClass.newInstance();
                        validator.initialize(annotation);
                        if (validator instanceof Validator) {
                            // 自有自定义的注解才能生效
                            ((Validator) validator).setName(column.name());
                            ((Validator) validator).setRequired(column.required());

                            validators.add((Validator) validator);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e.getMessage());
                    }
                }
            }
        }

        return validators.isEmpty() ? null : validators;
    }
}
