package com.olading.operate.labor.domain.salary.engine.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 累计税务数据对象
 */
@Data
public class AccumulatedTaxData {
    
    /**
     * 累计收入
     */
    private BigDecimal accumulatedIncome = BigDecimal.ZERO;
    
    /**
     * 累计费用 (累计收入 * 20%)
     */
    private BigDecimal accumulatedExpenses = BigDecimal.ZERO;
    
    /**
     * 累计减除费用 (月数 * 5000)
     */
    private BigDecimal accumulatedDeductionExpenses = BigDecimal.ZERO;
    
    /**
     * 累计免税收入 (累加)
     */
    private BigDecimal accumulatedTaxFreeIncome = BigDecimal.ZERO;
    
    /**
     * 累计依法确定的其他扣除 (累加)
     */
    private BigDecimal accumulatedOtherDeductions = BigDecimal.ZERO;
    
    /**
     * 累计减免税额 (累加)
     */
    private BigDecimal accumulatedTaxRelief = BigDecimal.ZERO;
    
    /**
     * 累计应纳税所得额
     */
    private BigDecimal accumulatedTaxableAmount = BigDecimal.ZERO;
    
    /**
     * 累计应纳税额
     */
    private BigDecimal accumulatedTaxAmount = BigDecimal.ZERO;
    
    /**
     * 累计已预缴税额(截至上个月)
     */
    private BigDecimal accumulatedPrepaidTax = BigDecimal.ZERO;

    /**
     * 当期累计已缴纳税额
     */
    private BigDecimal accumulatedCurrentTaxAmount = BigDecimal.ZERO;

    /**
     * 创建零值累计数据
     */
    public static AccumulatedTaxData zero() {
        return new AccumulatedTaxData();
    }
    
    /**
     * 创建累计数据
     */
    public static AccumulatedTaxData of(BigDecimal accumulatedIncome, 
                                       BigDecimal accumulatedExpenses,
                                       BigDecimal accumulatedDeductionExpenses,
                                       BigDecimal accumulatedTaxFreeIncome,
                                       BigDecimal accumulatedOtherDeductions,
                                       BigDecimal accumulatedTaxRelief,
                                       BigDecimal accumulatedTaxableAmount,
                                       BigDecimal accumulatedTaxAmount,
                                       BigDecimal accumulatedPrepaidTax,
                                        BigDecimal accumulatedCurrentTaxAmount) {
        AccumulatedTaxData data = new AccumulatedTaxData();
        data.setAccumulatedIncome(accumulatedIncome != null ? accumulatedIncome : BigDecimal.ZERO);
        data.setAccumulatedExpenses(accumulatedExpenses != null ? accumulatedExpenses : BigDecimal.ZERO);
        data.setAccumulatedDeductionExpenses(accumulatedDeductionExpenses != null ? accumulatedDeductionExpenses : BigDecimal.ZERO);
        data.setAccumulatedTaxFreeIncome(accumulatedTaxFreeIncome != null ? accumulatedTaxFreeIncome : BigDecimal.ZERO);
        data.setAccumulatedOtherDeductions(accumulatedOtherDeductions != null ? accumulatedOtherDeductions : BigDecimal.ZERO);
        data.setAccumulatedTaxRelief(accumulatedTaxRelief != null ? accumulatedTaxRelief : BigDecimal.ZERO);
        data.setAccumulatedTaxableAmount(accumulatedTaxableAmount != null ? accumulatedTaxableAmount : BigDecimal.ZERO);
        data.setAccumulatedTaxAmount(accumulatedTaxAmount != null ? accumulatedTaxAmount : BigDecimal.ZERO);
        data.setAccumulatedPrepaidTax(accumulatedPrepaidTax != null ? accumulatedPrepaidTax : BigDecimal.ZERO);
        data.setAccumulatedCurrentTaxAmount(accumulatedCurrentTaxAmount != null ? accumulatedCurrentTaxAmount : BigDecimal.ZERO);
        return data;
    }
    
    /**
     * 复制累计数据
     */
    public AccumulatedTaxData copy() {
        AccumulatedTaxData copy = new AccumulatedTaxData();
        copy.setAccumulatedIncome(this.accumulatedIncome);
        copy.setAccumulatedExpenses(this.accumulatedExpenses);
        copy.setAccumulatedDeductionExpenses(this.accumulatedDeductionExpenses);
        copy.setAccumulatedTaxFreeIncome(this.accumulatedTaxFreeIncome);
        copy.setAccumulatedOtherDeductions(this.accumulatedOtherDeductions);
        copy.setAccumulatedTaxRelief(this.accumulatedTaxRelief);
        copy.setAccumulatedTaxableAmount(this.accumulatedTaxableAmount);
        copy.setAccumulatedTaxAmount(this.accumulatedTaxAmount);
        copy.setAccumulatedPrepaidTax(this.accumulatedPrepaidTax);
        copy.setAccumulatedCurrentTaxAmount(this.accumulatedCurrentTaxAmount);
        return copy;
    }
    
    /**
     * 判断是否为零值数据
     */
    public boolean isZero() {
        return accumulatedIncome.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedExpenses.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedTaxFreeIncome.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedOtherDeductions.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedTaxRelief.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedTaxableAmount.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedTaxAmount.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedPrepaidTax.compareTo(BigDecimal.ZERO) == 0 &&
               accumulatedCurrentTaxAmount.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * 校验数据有效性
     */
    public void validate() {
        if (accumulatedIncome.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计收入不能为负数");
        }
        if (accumulatedExpenses.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计减除费用不能为负数");
        }
        if (accumulatedTaxFreeIncome.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计免税收入不能为负数");
        }
        if (accumulatedOtherDeductions.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计其他扣除不能为负数");
        }
        if (accumulatedTaxRelief.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计减免税额不能为负数");
        }
        if (accumulatedTaxableAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计应纳税所得额不能为负数");
        }
        if (accumulatedTaxAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计应纳税额不能为负数");
        }
        if (accumulatedPrepaidTax.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计已预缴税额不能为负数");
        }
        if (accumulatedCurrentTaxAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("累计本期已预缴税额不能为负数");
        }
    }
}