package com.olading.operate.labor.app.provider;

import com.olading.boot.core.business.Tenant;
import com.olading.boot.core.spi.AuthorityProvider;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.CustomerManager;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierManager;
import com.olading.operate.labor.domain.share.authority.SupplierMemberEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

@Component
@RequiredArgsConstructor
public class CustomAuthorityProvider implements AuthorityProvider {

    private final SupplierManager supplierManager;
    private final AuthorityManager authorityManager;
    private final CustomerManager customerManager;


    @Override
    public Set<String> getUserAuthorities(String userId, Tenant tenant) {

        var user = Long.parseLong(userId);
        var t = TenantInfo.of(tenant);

        if (t.getType() == TenantInfo.TenantType.SUPPLIER) {
            return getSupplierMemberSession(user, t);
        } else if (t.getType() == TenantInfo.TenantType.BOSS) {
            return getBossSession(user, t);
        } else if (t.getType() == TenantInfo.TenantType.CUSTOMER) {
            return getCustomerMemberSession(user, t);
        } else if (t.getType() == TenantInfo.TenantType.PERSONAL) {
            return getPersonalSession(user, t);
        } else {
            throw new IllegalStateException();
        }
    }

    private Set<String> getPersonalSession(long user, TenantInfo t) {
        return Set.of(Authority.SYS_PERSONAL);
    }


    private Set<String> getSupplierMemberSession(long user, TenantInfo tenant) {

        if (!TenantInfo.isNobody(tenant)) {
            SupplierEntity supplier = supplierManager.getSupplierById(Long.parseLong(tenant.getId()));
            if (supplier == null || supplier.getDisabled()) {
                return Set.of(Authority.SYS_SUPPLIER);
            }
            SupplierMemberEntity supplierMember = supplierManager.getSupplierMemberByUserId(supplier.getId(), user);
            if (supplierMember == null || supplierMember.getDisabled()) {
                return Set.of(Authority.SYS_SUPPLIER);
            }

            Set<String> r = authorityManager.getSubjectAuthorities(tenant, supplierMember.getId());
            r.add(Authority.SYS_SUPPLIER);

            if (!TenantInfo.isNobody(tenant)) {
                // 如果是管理员，默认有全部权限
                if (Objects.equals(supplier.getAdminUserId(), user)) {
                    r.addAll(Authority.getSupplierAllAuthorities());
                }
            }

            return r;
        } else {
            return Set.of(Authority.SYS_SUPPLIER);
        }
    }

    private Set<String> getCustomerMemberSession(long user, TenantInfo tenant) {

        if (!TenantInfo.isNobody(tenant)) {
            CustomerEntity customer = customerManager.getCustomer(Long.parseLong(tenant.getId()));
            if (customer == null ) {
                return Set.of(Authority.SYS_CUSTOMER);
            }
            SupplierMemberEntity supplierMember = supplierManager.getCustomerMember(customer.getId(), user);
            if (supplierMember == null || supplierMember.getDisabled()) {
                return Set.of(Authority.SYS_CUSTOMER);
            }

            Set<String> r = authorityManager.getSubjectAuthorities(tenant, supplierMember.getId());
            r.add(Authority.SYS_CUSTOMER);

            if (!TenantInfo.isNobody(tenant)) {
                // 如果是管理员，默认有全部权限
                if (Objects.equals(customer.getAdminUserId(), user)) {
                    r.addAll(Authority.getAllCustomerAuthorities());
                }
            }

            return r;
        } else {
            return Set.of(Authority.SYS_CUSTOMER);
        }
    }

    private Set<String> getBossSession(long user, TenantInfo tenant) {
        return Set.of(Authority.SYS_BOSS);
    }

    private Set<String> getCustomerSession(long user, TenantInfo tenant) {
        return Authority.getAllCustomerAuthorities();
    }
}
