package com.olading.operate.labor.domain.share.signing.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.olading.operate.labor.domain.share.signing.enums.ControlType;
import com.olading.operate.labor.domain.share.signing.enums.SignWay;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/4
 * @description ：
 * @version: 1.0
 */
@Schema(description= "从页面上传过来的控件")
@Data
public class ApiControl {

    /**
     * 控件名称
     */
    private String name;

    /**
     * 类型
     */
    private ControlType type;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 横坐标(百分比坐标)
     */
    @JsonProperty("xAxis")
    private java.math.BigDecimal xAxis;

    /**
     * 纵坐标(百分比坐标)
     */
    @JsonProperty("yAxis")
    private java.math.BigDecimal yAxis;

    /**
     * 宽度(相对页面百分比)
     */
    private java.math.BigDecimal width;

    /**
     * 高度(相对页面百分比 )
     */
    private java.math.BigDecimal height;

    /**
     * 字体
     */
    private String fontFamily;

    /**
     * 字体大小(相对页面百分比)
     */
    private java.math.BigDecimal fontSize;

    /**
     * 时间格式
     */
    private String dateFormat;

    /**
     * 签署方式
     */
    private SignWay signWay;

    /**
     * 签署步骤(关键字或者签署步骤)
     */
    private String stepId;

    /**
     * 默认值
     */
    private String value;
}
