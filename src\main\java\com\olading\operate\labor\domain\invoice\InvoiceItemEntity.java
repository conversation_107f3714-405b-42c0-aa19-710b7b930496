package com.olading.operate.labor.domain.invoice;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

@Getter
@Setter
@Comment("开票明细")
@Entity
@Table(name = "t_invoice_item")
public class InvoiceItemEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    
    public InvoiceItemEntity() {}
    
    public InvoiceItemEntity(TenantInfo tenantInfo) {
        setTenant(tenantInfo);
    }

    @NotNull
    @Comment("服务合同id")
    @Column(name = "contract_id", nullable = false)
    private Long contractId;

    @NotNull
    @Comment("开票id")
    @Column(name = "invoice_id", nullable = false)
    private Long invoiceId;

    @NotNull
    @Comment("账单id")
    @Column(name = "bill_id", nullable = false)
    private Long billId;

    @Size(max = 64)
    @NotNull
    @Comment("发票类目")
    @Column(name = "invoice_category", nullable = false, length = 64)
    private String invoiceCategory;

    @NotNull
    @Comment("开票金额")
    @Column(name = "fee", nullable = false, precision = 12, scale = 2)
    private BigDecimal fee;

}