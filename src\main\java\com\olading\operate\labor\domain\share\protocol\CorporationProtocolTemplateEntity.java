package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.app.web.biz.enums.ProtocolTempEnum;
import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

@Getter
@Setter
@Comment("作业主体协议模版配置信息")
@Entity
@Table(name = "t_corporation_protocol_template")
public class CorporationProtocolTemplateEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @Size(max = 30)
    @NotNull
    @Comment("协议类型")
    @Column(name = "temp_type", nullable = false, length = 30)
    private String tempType;

    @Size(max = 64)
    @NotNull
    @Comment("协议模板名称")
    @Column(name = "temp_name", nullable = false, length = 30)
    private String tempName;

    @Size(max = 30)
    @Comment("云签协议模版id")
    @Column(name = "agent_template", length = 30)
    private String agentTemplate;

    @Size(max = 32)
    @Comment("模板文件id")
    @Column(name = "template_file_id", length = 32)
    private String templateFileId;

    @Size(max = 1024)
    @Comment("模板编辑url")
    @Column(name = "edit_url", length = 1024)
    private String editUrl;

    @Size(max = 1024)
    @Comment("模板上传url")
    @Column(name = "upload_url", length = 1024)
    private String uploadUrl;

    @NotNull
    @Comment("协议可用状态")
    @Column(name = "STATUS", length = 20)
    private String status;

    public CorporationProtocolTemplateEntity(TenantInfo tenant) {
        setTenant(tenant);
    }

    public CorporationProtocolTemplateEntity() {
    }


}