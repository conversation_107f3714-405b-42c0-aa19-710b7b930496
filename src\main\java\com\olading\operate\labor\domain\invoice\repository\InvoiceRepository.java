package com.olading.operate.labor.domain.invoice.repository;

import com.olading.operate.labor.domain.invoice.InvoiceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<InvoiceEntity, Long> {
    
    /**
     * 根据供应商ID和开票ID查询
     */
    Optional<InvoiceEntity> findByIdAndSupplierIdAndDeletedFalse(Long id, Long supplierId);
    
    /**
     * 根据供应商ID查询开票列表
     */
    List<InvoiceEntity> findBySupplierIdAndDeletedFalseOrderByCreateTimeDesc(Long supplierId);
}