package com.olading.operate.labor.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;

import java.io.IOException;

public class NullIfEmptyInEnumDeserializer extends JsonDeserializer<Enum<?>>
        implements ContextualDeserializer {

    private Class<? extends Enum<?>> enumClass;

    public NullIfEmptyInEnumDeserializer() {
    }

    public NullIfEmptyInEnumDeserializer(Class<? extends Enum<?>> enumClass) {
        this.enumClass = enumClass;
    }

    @Override
    public Enum<?> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();

        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        try {
            //关键修复：类型强转避免泛型不兼容错误
            @SuppressWarnings({ "rawtypes", "unchecked" })
            Enum<?> result = Enum.valueOf((Class<? extends Enum>) enumClass, value.trim());
            return result;
        } catch (IllegalArgumentException e) {
            throw new JsonMappingException(p, "Invalid enum value: " + value + " for enum class: " + enumClass.getName());
        }
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) {
        JavaType type = ctxt.getContextualType();
        if (type == null || !type.isEnumType()) {
            return this;
        }
        @SuppressWarnings("unchecked")
        Class<? extends Enum<?>> rawClass = (Class<? extends Enum<?>>) type.getRawClass();
        return new NullIfEmptyInEnumDeserializer(rawClass);
    }
}
