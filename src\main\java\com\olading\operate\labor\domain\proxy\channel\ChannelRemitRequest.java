package com.olading.operate.labor.domain.proxy.channel;

import com.olading.operate.labor.domain.proxy.order.ProxyOrderEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/10 19:03
 */
@Data
public class ChannelRemitRequest {
    @NotNull
    @Schema(description = "灵工平台id")
    private Long supplierId;

    @NotNull
    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @NotNull
    @Schema(description = "工资代发订单id")
    private Long proxyOrderId;

    @Size(max = 64)
    @NotNull
    @Schema(description = "通道编码")
    private String payChannel;

    @Size(max = 20)
    @NotNull
    @Schema(description = "姓名")
    private String name;

    @Size(max = 20)
    @NotNull
    @Schema(description = "身份证号")
    private String idCard;

    @Size(max = 11)
    @NotNull
    @Schema(description = "手机号")
    private String cellphone;

    @Size(max = 24)
    @NotNull
    @Schema(description = "银行卡号")
    private String bankCard;

    @Size(max = 20)
    @Schema(description = "出款银行编码")
    private String bankCode;

    @Size(max = 20)
    @Schema(description = "出款银行名称")
    private String bankName;

    @NotNull
    @Schema(description = "付款金额")
    private BigDecimal amount;

    private String remark;


    public static ChannelRemitRequest of(ProxyOrderEntity entity) {
        ChannelRemitRequest request = new ChannelRemitRequest();
        request.setSupplierId(entity.getSupplierId());
        request.setSupplierCorporationId(entity.getSupplierCorporationId());
        request.setProxyOrderId(entity.getId());
        request.setPayChannel(entity.getPayChannel());
        request.setName(entity.getName());
        request.setIdCard(entity.getIdCard());
        request.setCellphone(entity.getCellphone());
        request.setBankCard(entity.getBankCard());
        request.setAmount(entity.getAmount());
        request.setRemark(entity.getRemark());
        return request;
    }
}
