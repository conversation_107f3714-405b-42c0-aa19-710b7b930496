package com.olading.operate.labor.util.excel;

import com.olading.operate.labor.util.exception.ExcelIOException;
import com.lanmaoly.util.lang.StringUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/3/8 11:15
 */
public class ExcelHeaderMappingRule {
    /**
     * Excel的表头名称与业务定义(ExcelRow里面的定义）的表头名称的对应关系
     * Map<excelTitle, columnName>，可以多对一，不能一对多
     */
    private Map<String, String> titleNameMap = new HashMap<>();

    /**
     *
     * @param excelTitle
     * @param columnName
     * @throws ExcelIOException
     */
    public ExcelHeaderMappingRule put(String excelTitle, String columnName) throws ExcelIOException {
        excelTitle = parseText(excelTitle);
        columnName = parseText(columnName);
        if (StringUtils.isBlank(excelTitle) || StringUtils.isBlank(columnName)) {
            return this;
        }

        if (titleNameMap.containsKey(excelTitle) && !columnName.equals(titleNameMap.get(excelTitle))) {
            throw new ExcelIOException(String.format("表头'%s'不能同时匹配'%s'和'%s'", excelTitle, columnName,
                    titleNameMap.get(excelTitle)));
        }

        titleNameMap.put(excelTitle, columnName);
        return this;
    }

    public boolean isEmpty() {
        return titleNameMap.isEmpty();
    }

    /**
     * Excel表头名称转为ExcelRow里定义的业务名称
     * @param title
     * @return
     */
    public String title2name(String title) {
        title = parseText(title);
        if (titleNameMap.containsKey(title)) {
            return titleNameMap.get(title);
        }

        return null;
    }

    public boolean verify(Collection<String> excelTitles, Collection<String> columnNames){
        Set<String> mustMatchTitles = new HashSet<>();
        for (Map.Entry<String, String> entry: titleNameMap.entrySet()) {
            String title = entry.getKey();
            String name = entry.getValue();
            if (columnNames.contains(name) && excelTitles.contains(title)
                    && excelTitles.contains(name) && !title.equals(name)) {
                mustMatchTitles.add(name);
            }
        }

        if (!mustMatchTitles.isEmpty()) {
            String titles = StringUtils.join(mustMatchTitles, "、");
            throw new ExcelIOException(String.format("表头'%s'必须与系统'%s'匹配", titles, titles));
        }

        return true;
    }

    public static String parseText(String text) {
        text = StringUtils.RemoveEnterBlank(text);
        if (null != text) {
            // 特殊处理，过滤掉表头前面的"*"号（一般必填项会在表头加*)
            return text.replaceAll("^[\\s\\*]*", "");
        }

        return null;
    }
}
