package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.tax.vo.TaxPaymentVoucherVo;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;

@Transactional
@Component
@RequiredArgsConstructor
public class TaxPaymentVoucherManager {

    private final EntityManager em;
    private final CorporationManager corporationManager;

    /**
     * 新增税务缴纳凭证记录
     */
    public TaxPaymentVoucherEntity addTaxPaymentVoucher(TenantInfo tenantInfo, TaxPaymentVoucherVo vo) {
        TaxPaymentVoucherEntity entity = new TaxPaymentVoucherEntity(tenantInfo);
        copyToEntity(vo, entity);
        return em.merge(entity);
    }

    /**
     * 更新税务缴纳凭证记录
     */
    public TaxPaymentVoucherEntity updateTaxPaymentVoucher(TaxPaymentVoucherVo vo) {
        TaxPaymentVoucherEntity entity = getTaxPaymentVoucherById(vo.getId());
        if (entity == null) {
            throw new IllegalStateException("税务缴纳凭证记录不存在");
        }
        copyToEntityWithNullCheck(vo, entity);
        return em.merge(entity);
    }

    /**
     * 根据ID查询税务缴纳凭证记录
     */
    public TaxPaymentVoucherEntity getTaxPaymentVoucherById(Long id) {
        TaxPaymentVoucherEntity entity = em.find(TaxPaymentVoucherEntity.class, id);
        if (entity == null) {
            throw new IllegalStateException("税务缴纳凭证记录不存在");
        }
        return entity;
    }

    /**
     * 查询税务缴纳凭证记录详情
     */
    public TaxPaymentVoucherVo queryTaxPaymentVoucher(Long id) {
        TaxPaymentVoucherEntity entity = getTaxPaymentVoucherById(id);
        TaxPaymentVoucherVo vo = new TaxPaymentVoucherVo();
        copyFromEntity(entity, vo);
        
        // 设置作业主体名称
        if (entity.getSupplierCorporationId() != null) {
            try {
                vo.setSupplierCorporationName(corporationManager.requireCorporation(entity.getSupplierCorporationId()).getName());
            } catch (Exception e) {
                // 如果作业主体不存在，设置为空
                vo.setSupplierCorporationName(null);
            }
        }
        
        return vo;
    }

    /**
     * 根据作业主体ID查询税务缴纳凭证记录列表
     */
    public List<TaxPaymentVoucherEntity> queryTaxPaymentVoucherBySupplierCorporation(Long supplierCorporationId) {
        QTaxPaymentVoucherEntity t = QTaxPaymentVoucherEntity.taxPaymentVoucherEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.supplierCorporationId.eq(supplierCorporationId))
                .fetch();
    }

    /**
     * 根据作业主体ID和税款所属期查询税务缴纳凭证记录
     */
    public TaxPaymentVoucherEntity queryTaxPaymentVoucherBySupplierCorporationAndTaxPeriod(Long supplierCorporationId, String taxPaymentPeriod) {
        QTaxPaymentVoucherEntity t = QTaxPaymentVoucherEntity.taxPaymentVoucherEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.supplierCorporationId.eq(supplierCorporationId)
                        .and(t.taxPaymentPeriod.eq(taxPaymentPeriod)))
                .fetchFirst();
    }

    /**
     * 删除税务缴纳凭证记录（直接删除）
     */
    public void deleteTaxPaymentVoucher(Long id) {
        TaxPaymentVoucherEntity entity = getTaxPaymentVoucherById(id);
        em.remove(entity);
    }

    /**
     * 通用查询方法
     */
    public JPAQuery<TaxPaymentVoucherEntity> queryTaxPaymentVoucher(Function<QTaxPaymentVoucherEntity, Predicate> condition) {
        QTaxPaymentVoucherEntity t = QTaxPaymentVoucherEntity.taxPaymentVoucherEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    /**
     * 复制VO到Entity
     */
    private void copyToEntity(TaxPaymentVoucherVo vo, TaxPaymentVoucherEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getTaxPaymentPeriod() != null) {
            entity.setTaxPaymentPeriod(vo.getTaxPaymentPeriod());
        }
        if (vo.getFileIds() != null) {
            entity.setFileIds(vo.getFileIds());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
    }

    /**
     * 复制VO到Entity（空值检查）
     */
    private void copyToEntityWithNullCheck(TaxPaymentVoucherVo vo, TaxPaymentVoucherEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getTaxPaymentPeriod() != null) {
            entity.setTaxPaymentPeriod(vo.getTaxPaymentPeriod());
        }
        if (vo.getFileIds() != null) {
            entity.setFileIds(vo.getFileIds());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
    }

    /**
     * 复制Entity到VO
     */
    private void copyFromEntity(TaxPaymentVoucherEntity entity, TaxPaymentVoucherVo vo) {
        vo.setId(entity.getId());
        vo.setSupplierCorporationId(entity.getSupplierCorporationId());
        vo.setTaxPaymentPeriod(entity.getTaxPaymentPeriod());
        vo.setFileIds(entity.getFileIds());
        vo.setCreateTime(entity.getCreateTime());
        vo.setModifyTime(entity.getModifyTime());
        vo.setSupplierId(entity.getSupplierId());
    }
}
