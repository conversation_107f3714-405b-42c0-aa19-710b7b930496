package com.olading.operate.labor.domain.share.contract;

import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.domain.query.BusinessContractQuery;
import com.olading.operate.labor.domain.service.BusinessContractService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Random;

@Slf4j
public class BusinessContractTest extends BaseTest {

    @Autowired
    private BusinessContractService businessContractService;

    @Autowired
    private QueryService queryService;

    /**
     * 按雇员人数计算（EMPLOYEE_COUNT）
     */
    @Test
    public void addContract_EmployeeCount() {
        ContractVo vo = buildBasicContractVo();
        vo.setManageCalculationRule("EMPLOYEE_COUNT");
        vo.setManageAmount(BigDecimal.valueOf(5000));  // 雇员人数计算，设置金额
        vo.setManageRate(null);
        vo.setSupplierId(23423l);
        businessContractService.addContract(null, vo);
        log.info("新增合同【雇员人数】成功：{}", JSONUtils.json(vo));
    }

    /**
     * 按应发金额比例计算（PAYABLE_AMOUNT_RATE）
     */
    @Test
    public void addContract_PayableAmountRate() {
        ContractVo vo = buildBasicContractVo();
        vo.setManageCalculationRule("PAYABLE_AMOUNT_RATE");
        vo.setManageRate(BigDecimal.valueOf(0.05));  // 应发金额比例计算，设置费率
        vo.setManageAmount(null);
        vo.setSupplierId(23423l);
        businessContractService.addContract(null, vo);
        log.info("新增合同【应发金额比例】成功：{}", JSONUtils.json(vo));
    }

    /**
     * 更新合同测试（随机规则）
     */
    @Test
    public void updateContract() {
        ContractVo vo = buildBasicContractVo();
        vo.setId(8L);
        vo.setName("修改合同" + new Random().nextInt(1000));
        vo.setManageCalculationRule("EMPLOYEE_COUNT");
        vo.setManageAmount(BigDecimal.valueOf(8000));
        vo.setManageRate(null);

        businessContractService.updateContract(null, vo);
        log.info("更新合同成功：{}", JSONUtils.json(vo));
    }

    /**
     * 查询合同测试
     */
    @Test
    public void queryContract() {
        Long contractId = 1L;
        ContractVo contract = businessContractService.queryContract(contractId);
        log.info("查询合同成功：{}", JSONUtils.json(contract));
    }

    @Test
    public void queryContractBysupplier() {
        Long supplierId = 23423l;
        List<ContractVo> contractVos = businessContractService.queryContractBySupplier(supplierId);
        log.info("查询合同成功：queryContractBysupplier:{}", JSONUtils.json(contractVos));
    }

    /**
     * 构建基础合同VO（通用方法）
     */
    private ContractVo buildBasicContractVo() {
        ContractVo vo = new ContractVo();
        vo.setCustomerId(1L);
        vo.setSupplierCorporationId(2L);
        vo.setName("测试服务合同" + System.currentTimeMillis() % 10000);
        vo.setSn("HT" + System.currentTimeMillis() % 10000);
        vo.setTimeFixed(true);
        vo.setStartDate(LocalDate.now());
        vo.setEndDate(LocalDate.now().plusYears(1));
        vo.setStopped(false);
        vo.setBusinessType("DEMO");
        vo.setRemark("测试合同");
        vo.setFileIds("file1,file2");

        vo.setInvoiceTitle("测试公司抬头");
        vo.setInvoiceTaxNo("91310000MA1KXXXXXX");
        vo.setInvoiceBankName("招商银行");
        vo.setInvoiceBankAccount("622202020002XXXXXX");
        vo.setInvoiceRegisterAddress("上海XX路");
        vo.setInvoiceCompanyTel("021-********");
        vo.setInvoiceRemark("测试发票");

        return vo;
    }

    @Test
    public void queryContractPage() {
        BusinessContractQuery.Filters filters = new BusinessContractQuery.Filters();
        filters.setName("测试");
        filters.setCustomerName("22");
        filters.setStatus(null);
        filters.setCreateTimeStart(null);
        filters.setCreateTimeEnd(null);

        QueryFilter<BusinessContractQuery.Filters> queryFilter = new QueryFilter<>();
        queryFilter.setFilters(filters);
        queryFilter.setLimit(10L);  // 每页10条
        queryFilter.setOffset(0L);  // 第1页
        queryFilter.sort("id", Direction.DESCENDING);

        DataSet<ContractVo> result = queryService.queryContract(queryFilter);

        log.info("分页查询结果：{}", JSONUtils.json(result));
    }

    @Test
    public void testTerminateContract() {
        // 测试提前终止合同
        Long contractId = 1L; // 使用已存在的合同ID
        String stopReason = "客户要求提前终止合同";
        Long updaterId = 1L;

        try {
            businessContractService.terminateContract(null, contractId, stopReason, updaterId);
            log.info("提前终止合同成功！合同ID: {}, 终止原因: {}", contractId, stopReason);

            // 查询验证合同状态是否更新成功
            ContractVo contractVo = businessContractService.queryContract(contractId);
            log.info("终止后的合同状态 - stopped: {}, stopReason: {}, status: {}",
                    contractVo.getStopped(), contractVo.getStopReason(), contractVo.getStatus());
        } catch (Exception e) {
            log.error("提前终止合同失败", e);
        }
    }

    @Test
    public void testTerminateAlreadyStoppedContract() {
        // 测试终止已经终止的合同
        Long contractId = 1L;
        String stopReason = "重复终止测试";
        Long updaterId = 1L;

        try {
            // 先终止一次
            businessContractService.terminateContract(null, contractId, stopReason, updaterId);
            // 再次尝试终止
            businessContractService.terminateContract(null, contractId, stopReason, updaterId);
            log.error("应该抛出异常，但没有抛出");
        } catch (Exception e) {
            log.info("正确捕获到异常: {}", e.getMessage());
        }
    }
}
