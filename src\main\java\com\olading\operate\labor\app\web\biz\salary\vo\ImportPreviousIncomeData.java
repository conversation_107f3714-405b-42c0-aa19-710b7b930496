package com.olading.operate.labor.app.web.biz.salary.vo;

import com.olading.operate.labor.util.excel.ExcelColumn;
import com.olading.operate.labor.util.excel.ExcelRow;
import com.olading.operate.labor.util.validation.constraints.Name;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

@Data
public class ImportPreviousIncomeData extends ExcelRow {
    @ExcelColumn(name = "姓名", required = true)
    @Name(required = true)
    private String name;

    @ExcelColumn(name = "身份证号", required = true)
    @Name(required = true, maxLength = 18)
    private String idCard;

    @ExcelColumn(name = "累计收入", required = true)
    @ColumnDefault("0.00")
    private String totalIncome;

    @ExcelColumn(name = "累计费用", required = true)
    @ColumnDefault("0.00")
    private String totalExpenses;

    @ExcelColumn(name = "累计免税收入", required = true)
    @ColumnDefault("0.00")
    private String totalTaxFreeIncome;

    @ExcelColumn(name = "累计减除费用", required = true)
    @ColumnDefault("0.00")
    private String totalDeductions;

    @ExcelColumn(name = "累计依法确定的其他扣除", required = true)
    @ColumnDefault("0.00")
    private String totalOtherDeductions;

    @ExcelColumn(name = "累计已预缴税额", required = true)
    @ColumnDefault("0.00")
    private String totalPrepaidTaxes;

    @ExcelColumn(name = "累计减免税额", required = true)
    @ColumnDefault("0.00")
    private String totalTaxReductions;


    @ExcelColumn(name = "反馈信息")
    @Name(required = true, maxLength = 60)
    private String errorMsg;
}
