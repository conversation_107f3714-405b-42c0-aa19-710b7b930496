package com.olading.operate.labor.domain.invoice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "开票明细请求")
public class InvoiceItemRequest {
    
    @NotNull(message = "账单ID不能为空")
    @Schema(description = "账单ID")
    private Long billId;
    
    @NotBlank(message = "发票类目不能为空")
    @Schema(description = "发票类目")
    private String invoiceCategory;
    
    @NotNull(message = "开票金额不能为空")
    @DecimalMin(value = "0.01", message = "开票金额必须大于0")
    @Schema(description = "开票金额")
    private BigDecimal fee;
}