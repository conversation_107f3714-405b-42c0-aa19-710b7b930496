package com.olading.operate.labor.domain.share.seq;


import com.olading.operate.labor.AppProperties;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
@RequiredArgsConstructor
public class SequenceManager {

    private final SequenceRepository repository;
    private final AppProperties properties;

    @PostConstruct
    public void post() {
        properties.getSequenceDeclare().forEach(this::declare);
    }

    public void declare(String name, long start) {
        repository.declare(name, start);
    }

    public Sequence getSequence(String name, long step) {
        var r = request(name, step);
        return new SequenceImpl(name, r.getRight(), r.getLeft());
    }

    private Pair<Long, Long> request(String name, long step) {
        return repository.request(name, step);
    }

    class SequenceImpl implements Sequence {

        private final Object lock = new Object();
        private final String name;
        private AtomicLong end;
        private AtomicLong current;

        public SequenceImpl(String name, long end, long current) {
            this.name = name;
            this.end = new AtomicLong(end);
            this.current = new AtomicLong(current);
        }

        @Override
        public long next() {
            long value = current.getAndIncrement();
            if (value >= end.get()) {
                return syncNext();
            }
            return value;
        }

        private long syncNext() {
            synchronized (lock) {
                long value = current.getAndIncrement();
                if (value >= end.get()) {
                    var r = request(name, 10000);
                    log.info("request: name={}, {}", name, r);
                    current.set(r.getLeft());
                    end.set(r.getRight());
                    value = current.getAndIncrement();
                }
                return value;
            }
        }
    }
}
