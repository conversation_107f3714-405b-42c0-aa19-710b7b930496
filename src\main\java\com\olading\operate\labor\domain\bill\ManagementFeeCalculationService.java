package com.olading.operate.labor.domain.bill;

import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractConfigEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 管理费计算服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ManagementFeeCalculationService {


    private final List<PersonManagementFee> personManagementFees;

    /**
     * 计算单个人员的管理费
     */
    public BigDecimal calculatePersonManagementFee(BusinessContractConfigEntity contractConfig, BillManager.PersonManagementFeeInfo personInfo) {

        final Optional<PersonManagementFee> any = personManagementFees.stream().filter(personManagementFee -> personManagementFee.getFeeItem().equals(contractConfig.getManageCalculationRule())).findAny();
        if (any.isEmpty()) {
            log.error("找不到匹配的管理费计算规则");
            return BigDecimal.ZERO;
        }
        return any.get().calculatePersonManagementFee(personInfo,contractConfig.getManageRate(),contractConfig.getManageAmount());


    }
}