package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoData;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.info.QEnterpriseInfoEntity;
import com.olading.operate.labor.domain.supplier.QSupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierData;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

public class SupplierQuery implements EntityQuery<QueryFilter<SupplierQuery.Filters>, SupplierData> {

    private final QSupplierEntity t1 = QSupplierEntity.supplierEntity;
    private final QEnterpriseInfoEntity t2 = QEnterpriseInfoEntity.enterpriseInfoEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();

        if (CollectionUtils.isNotEmpty(filters.getFilters().getId())) {
            criteria.and(t1.id.in(filters.getFilters().getId()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getSupplierNo())) {
            criteria.and(t1.supplierNo.eq(filters.getFilters().getSupplierNo()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getName())) {
            criteria.and(t2.name.like(JpaUtils.fullLike(filters.getFilters().getName())));
        }
        if (filters.getFilters().getBusinessCreateTimeBegin() != null) {
            criteria.and(t1.businessCreateTime.goe(filters.getFilters().getBusinessCreateTimeBegin()));
        }
        if (filters.getFilters().getBusinessCreateTimeEnd() != null) {
            criteria.and(t1.businessCreateTime.lt(filters.getFilters().getBusinessCreateTimeEnd()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getContacts())) {
            criteria.and(t2.contacts.like(JpaUtils.fullLike(filters.getFilters().getContacts())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getContactPhone())) {
            criteria.and(t2.contactPhone.like(JpaUtils.fullLike(filters.getFilters().getContactPhone())));
        }

        query.select(t1, t2)
                .from(t1)
                .innerJoin(t2).on(t1.enterpriseInfoId.eq(t2.id));

        query.where(criteria);
    }

    @Override
    public SupplierData transform(Object v) {

        Tuple tuple = (Tuple) v;
        SupplierEntity supplier = tuple.get(t1);
        EnterpriseInfoEntity enterpriseInfo = tuple.get(t2);

        if (supplier == null) {
            throw new NullPointerException();
        }

        SupplierData data = new SupplierData(supplier);
        if (enterpriseInfo != null) {
            data.setInfo(new EnterpriseInfoData(enterpriseInfo));
        }

        return data;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    public static class Filters {

        private String supplierNo;

        private List<Long> id;

        private String name;

        private LocalDateTime businessCreateTimeBegin;

        private LocalDateTime businessCreateTimeEnd;

        private String contacts;

        private String contactPhone;
    }

}
