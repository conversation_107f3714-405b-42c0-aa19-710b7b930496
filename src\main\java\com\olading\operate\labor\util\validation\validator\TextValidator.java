package com.olading.operate.labor.util.validation.validator;

import com.lanmaoly.util.lang.StringUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Text;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class TextValidator extends BaseValidator<Text> {
    public static final int MAX_LENGTH = 99999;
    public static final int MIN_LENGTH = 1;

    private int maxLength = MAX_LENGTH;
    private int minLength = MIN_LENGTH;

    private String message;
    private String regex;
    private List<String> options;

    public TextValidator() {}

    public TextValidator(String name) {
        setName(name);
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    public void setMinLength(int minLength) {
        this.minLength = minLength;
    }

    @Override
    public void initialize(Text constraintAnnotation) {
        maxLength = constraintAnnotation.maxLength();
        minLength = constraintAnnotation.minLength();
        regex = constraintAnnotation.regex();
        message = constraintAnnotation.message();
        options = Arrays.asList(constraintAnnotation.options());

        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        super.initialize(constraintAnnotation);
    }

    @Override
    protected boolean constraintCheck(Object o) {
        if (CollectionUtils.isNotEmpty(options) && !options.contains(String.valueOf(o))) {
            throw new ValidationException(getName(), "输入不合法");
        }

        int len = String.valueOf(o).length();
        if (len < minLength) {
            throw new ValidationException(getName(), String.format("不能少于%s位", minLength));
        } else if (len > maxLength) {
            throw new ValidationException(getName(), String.format("不能多于%s位", maxLength));
        }

        if (!StringUtils.isBlank(regex) && !Pattern.matches(regex, String.valueOf(o))) {
            String errMessage = message;
            if (StringUtils.isBlank(errMessage)) {
                errMessage = "数据格式错误";
            }

            throw new ValidationException(getName(), errMessage);
        }

        return true;
    }
}
