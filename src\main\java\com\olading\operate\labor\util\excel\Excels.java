package com.olading.operate.labor.util.excel;


import org.apache.poi.ss.usermodel.Workbook;

import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2022/3/14 22:05
 */
public class Excels {
    public static String ENTER_BLANK_CHAR = "\n";

    /**
     * 创建空的实例（主要用于导出文件用）
     * @param pojoClass
     * @return
     */
    public static ExcelResult createWriteResult(Class<? extends ExcelRow> pojoClass) {
        return ExcelResult.newInstance(pojoClass);
    }

    public static ExcelReaderBuilder reader(Class<? extends ExcelRow> pojoClass) {
        return ExcelReader.builder(pojoClass);
    }

    public static ExcelWriterBuilder writer(ExcelResult excelResult) {
        return ExcelWriter.builder(excelResult);
    }

    public static ExcelWriterBuilder writer(ExcelReader reader) {
        return ExcelWriter.builder(reader.getExcelResult())
                .sheetName(reader.getSheetName())
                .headerGroupRow(reader.getHeaderGroupRow())
                .headerRow(reader.getHeaderRow());
    }

    public static InputStream writeToInputStream(Workbook workbook) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        return new ByteArrayInputStream(os.toByteArray());
    }

    /**
     * 下载Excel文件
     * @param workbook
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void download(Workbook workbook, String fileName, HttpServletResponse response) throws IOException{
        try {
            response.setCharacterEncoding("UTF-8");
            // 前端解析需要这个设置
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            throw new IOException(e);
        }
    }
}
