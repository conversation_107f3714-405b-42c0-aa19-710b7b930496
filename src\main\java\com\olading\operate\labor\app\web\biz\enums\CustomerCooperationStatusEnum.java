package com.olading.operate.labor.app.web.biz.enums;

public enum CustomerCooperationStatusEnum {

    NOT("未合作"),
    NEGOTIATION("洽谈中"),
    ONGOING("合作中"),
    STOPPED("停止合作");

    private final String name;

    CustomerCooperationStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameByKey(String key) {
        for (CustomerCooperationStatusEnum e : values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getName();
            }
        }
        return "";
    }
}
