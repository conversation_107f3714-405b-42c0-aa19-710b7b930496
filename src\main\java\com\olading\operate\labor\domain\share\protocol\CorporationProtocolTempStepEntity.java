package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "t_corporation_protocol_temp_step")
public class CorporationProtocolTempStepEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Size(max = 20)
    @Comment("终端id")
    @Column(name = "tenant_id", length = 20)
    private String tenantId;

    @Column(name = "version")
    private Integer version;

    @Comment("平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("协议模板id")
    @Column(name = "template_id")
    private Long templateId;

    @Comment("操作类型 公章签署、个人签署、抄送")
    @Column(name = "operate", length = 32)
    private EnumOperateType operate;

    @Comment("步骤排序")
    @Column(name = "sort_by")
    private Integer sortBy;

    @Size(max = 32)
    @Comment("步骤名")
    @Column(name = "step_name", length = 32)
    private String stepName;

    @Comment("创建时间")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Comment("更新时间")
    @Column(name = "modify_time")
    private LocalDateTime modifyTime;

}