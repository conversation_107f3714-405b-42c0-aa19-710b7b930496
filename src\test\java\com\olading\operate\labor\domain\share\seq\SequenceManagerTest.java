package com.olading.operate.labor.domain.share.seq;

import com.olading.boot.util.Misc;
import com.olading.operate.labor.BaseTest;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class SequenceManagerTest extends BaseTest {

    @Autowired
    private SequenceManager sequenceManager;

    @Test
    public void test01() throws InterruptedException {

        var name = Misc.uuid();
        sequenceManager.declare(name, 1);
        var sequence = sequenceManager.getSequence(name, 10000);

        long prev = 0;
        for (int i = 0; i < 1000000; i++) {
            var v = sequence.next();
            if (prev + 1 != v) {
                Assertions.fail("不连续: " + prev + " -> " + v);
            }
            prev = v;
        }
    }

    @Test
    public void test02() throws InterruptedException {
        int threads = 5;
        int loop = 100000;

        var name = Misc.uuid();
        sequenceManager.declare(name, 1);
        var sequence = sequenceManager.getSequence(name, 100);

        HashSet<Long> set = new HashSet<>(threads * loop);
        ExecutorService executor = Executors.newFixedThreadPool(threads);
        AtomicBoolean fail = new AtomicBoolean(false);
        for (int i = 0; i < threads; i++) {
            executor.execute(() -> {
                for (int j = 0; j < loop; j++) {
                    var v = sequence.next();
                    if (!set.add(v)) {
                        fail.set(true);
                        System.out.println("fail: " + v);
                    }
                }
            });
        }
        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.MINUTES);

        Assertions.assertThat(fail).isFalse();
    }
}
