package com.olading.operate.labor.domain.share.signing.response;


import com.olading.operate.labor.domain.share.signing.common.FieldValue;
import com.olading.operate.labor.domain.share.signing.common.SignStep;
import com.olading.operate.labor.domain.share.signing.enums.FileStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "查询文件响应参数")
public class QueryFileResponse {


    @Schema(description = "模板编号", required = true)
    private Long id;
    @Schema(description = "待签名文件名称", required = true)
    private String name;
    @Schema(description = "文件大小", required = true)
    private Long fileSize;
    @Schema(description = "文件sha hash值", required = true)
    private String fileHash;
    @Schema(description = "文件状态", required = true)
    private FileStatus status;
    @Schema(description = "文件下载地址")
    private String downloadUrl;

    @Schema(description = "模板域填充数据", required = true)
    private List<FieldValue> fields;

    @Schema(description = "签名步骤列表", required = true)
    private List<SignStep> steps;
}
