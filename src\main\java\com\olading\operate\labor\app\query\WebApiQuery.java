package com.olading.operate.labor.app.query;

import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.ExcelExporter;
import com.olading.boot.util.jpa.querydsl.Query;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import lombok.Data;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

public class WebApiQuery<T, F, R> {

    private final Query<QueryFilter<F>, R> query;

    private final Class<T> beanClass;

    private final Function<R, T> mapping;


    public WebApiQuery(Query<QueryFilter<F>, R> query, Class<T> beanClass, Function<R, T> mapping) {
        this.query = query;
        this.beanClass = beanClass;
        this.mapping = mapping;
    }

    public DataSet<T> dataSet(QueryFilter<F> request) {
        return query.execute(request).convert(mapping);
    }

    public ExcelResult<T> excel(QueryFilter<F> request, OutputStream output, int returnLimit) {
        QueryFilter<F> copy = request.convert(o -> o);
        copy.setStart(null);
        copy.setOffset(null);
        copy.setLimit(null);


        AtomicLong total = new AtomicLong();
        List<T> list = new ArrayList<>();
        new ExcelExporter<>(beanClass).write(query, copy, output, o -> {
            var r = mapping.apply(o);
            total.incrementAndGet();
            if (list.size() < returnLimit) {
                list.add(r);
            }
            return r;
        });
        ExcelResult<T> result = new ExcelResult<>();
        result.setTotal(total.get());
        result.setFirst(list);
        return result;
    }

    public void excel(QueryFilter<F> request, OutputStream output, String zipFileName) {
        QueryFilter<F> copy = request.convert(o -> o);
        copy.setStart(null);
        copy.setOffset(null);
        copy.setLimit(null);
        new ExcelExporter<>(beanClass).writeZip(query, copy, output, mapping, 100000, index -> {
            String baseName = zipFileName.substring(0, zipFileName.lastIndexOf(".zip"));
            return baseName + "-" + index + ".xlsx";
        });
    }

    @Data
    public static class ExcelResult<T> {

        /**
         * 写入excel的总行数
         */
        private long total;

        private List<T> first = new ArrayList<>();
    }
}
