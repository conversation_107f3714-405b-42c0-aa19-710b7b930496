package com.olading.operate.labor.domain.share.identity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 人脸识别结果
 */
@Data
@Schema(description = "人脸识别结果")
public class FaceAuthResult {
    
    @Schema(description = "是否识别成功")
    private boolean success;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    @Schema(description = "相似度")
    private Float similarity;
    
    @Schema(description = "活体检测率")
    private Float liveRate;
    
    @Schema(description = "流水号")
    private String flowNo;
    
    @Schema(description = "是否通过验证（相似度和活体率都达标）")
    private boolean passed;
    
    /**
     * 创建成功结果
     */
    public static FaceAuthResult success(String flowNo, Float similarity, Float liveRate) {
        FaceAuthResult result = new FaceAuthResult();
        result.setSuccess(true);
        result.setFlowNo(flowNo);
        result.setSimilarity(similarity);
        result.setLiveRate(liveRate);
        // 设置验证通过的阈值，可以根据业务需求调整
        result.setPassed(similarity != null && similarity >= 0.8f && 
                        liveRate != null && liveRate >= 0.8f);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static FaceAuthResult failure(String errorMessage, String flowNo) {
        FaceAuthResult result = new FaceAuthResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setFlowNo(flowNo);
        result.setPassed(false);
        return result;
    }
}