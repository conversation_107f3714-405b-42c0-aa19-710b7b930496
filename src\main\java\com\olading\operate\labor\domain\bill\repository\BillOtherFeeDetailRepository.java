package com.olading.operate.labor.domain.bill.repository;

import com.olading.operate.labor.domain.bill.BillOtherFeeDetailEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 账单其他费用明细仓储接口
 */
@Repository
public interface BillOtherFeeDetailRepository extends JpaRepository<BillOtherFeeDetailEntity, Long> {

    /**
     * 根据账单主表ID删除明细
     */
    void deleteByBillMasterId(Long billMasterId);

    /**
     * 根据账单分类ID查找明细列表
     */
    List<BillOtherFeeDetailEntity> findByBillCategoryIdAndDeletedFalse(Long billCategoryId);

    /**
     * 根据导入批次号查找明细列表
     */
    List<BillOtherFeeDetailEntity> findByImportBatchNoAndDeletedFalse(String importBatchNo);

}