package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.compress.utils.Sets;
import org.hibernate.annotations.Comment;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Comment("协议模板域信息")
@Entity
@Table(name = "t_corporation_protocol_temp_filed")
public class CorporationProtocolTempFiledEntity {

    public static final EnumOperateType COMPANY_SIGNATORY = EnumOperateType.SEAL;

    public static final EnumOperateType PERSONAL_SIGNATORY = EnumOperateType.SIGN;

    public static final Set<EnumOperateType> SIGNATORIES = Sets.newHashSet(COMPANY_SIGNATORY, PERSONAL_SIGNATORY);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Size(max = 20)
    @NotNull
    @Comment("终端id")
    @Column(name = "tenant_id", nullable = false, length = 20)
    private String tenantId;

    @Column(name = "version")
    private Integer version;

    @Comment("平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @NotNull
    @Comment("协议模板id")
    @Column(name = "template_id", nullable = false)
    private Long templateId;

    @NotNull
    @Comment("模板步骤id")
    @Column(name = "template_step_id", nullable = false)
    private Long templateStepId;

    @Size(max = 32)
    @Comment("模板步骤名")
    @Column(name = "template_step_name", length = 32)
    private String templateStepName;

    @Size(max = 32)
    @Comment("字段名")
    @Column(name = "field_name", length = 32)
    private String fieldName;

    @Size(max = 32)
    @Comment("域编码")
    @Column(name = "field_code", length = 32)
    private String fieldCode;

    @Enumerated(EnumType.STRING)
    @Comment("签署方(SEAL-企业签署方 SIGN-个人签署方)")
    @Column(name = "signatory", length = 32)
    private EnumOperateType signatory;

//    @Size(max = 32)
//    @Comment("关联项")
//    @Column(name = "relation_code", length = 32)
//    private String relationCode;

    @Size(max = 32)
    @Comment("域默认值")
    @Column(name = "default_value", length = 1024)
    private String defaultValue;

    @Size(max = 128)
    @Comment("关联项名称")
    @Column(name = "relation_name", length = 128)
    private String relationName;

    @Size(max = 32)
    @Comment("关联项所属分组")
    @Column(name = "relation_group", length = 32)
    private String relationGroup;

    @Comment("创建时间")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Comment("更新时间")
    @Column(name = "modify_time")
    private LocalDateTime modifyTime;

    @Size(max = 32)
    @Comment("域类型 SEAL-企业签章 SIGN-个人签章 DATE-日期 FIELD-填充域")
    @Column(name = "operate", length = 32)
    private String operate;

}