package com.olading.operate.labor.util.excel;

/**
 * <AUTHOR>
 * @date 2022/2/22 15:53
 */
abstract public class BaseExcel {

    /**
     * 读取的Sheet位置
     */
    protected Integer sheetIndex = 0;

    /**
     * Sheet的名字
     */
    protected String sheetName;

    /**
     * 标题栏所属行号（从1开始数，0表示无）
     */
    protected int titleRow = 0;

    /**
     * 头部分组所属行号（从1开始数，0表示无）
     */
    protected int headerGroupRow = 0;

    /**
     * 头部所属行号（从1开始数，0表示无）
     */
    protected int headerRow = 1;

    public Integer getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(Integer sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public int getTitleRow() {
        return titleRow;
    }

    public void setTitleRow(int titleRow) {
        this.titleRow = titleRow;
    }

    public int getHeaderGroupRow() {
        return headerGroupRow;
    }

    public void setHeaderGroupRow(int headerGroupRow) {
        this.headerGroupRow = headerGroupRow;
    }

    public int getHeaderRow() {
        return headerRow;
    }

    public void setHeaderRow(int headerRow) {
        this.headerRow = headerRow;
    }
}
