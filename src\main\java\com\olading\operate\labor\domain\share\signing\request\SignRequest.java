package com.olading.operate.labor.domain.share.signing.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "签名请求参数")
public class SignRequest extends BaseRequest<SignRequest> {


    @Schema(description = "签约流水号", required = true)
    private String flowNo;

    @Schema(description = "控件大小是否根据签名改变", required = false)
    private Boolean isZoom;

    @Schema(description = "签署关键字")
    private java.util.List<String> keyWords;
}
