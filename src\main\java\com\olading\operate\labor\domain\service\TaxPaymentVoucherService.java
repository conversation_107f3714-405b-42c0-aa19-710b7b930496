package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.tax.TaxPaymentVoucherEntity;
import com.olading.operate.labor.domain.share.tax.TaxPaymentVoucherManager;
import com.olading.operate.labor.domain.share.tax.vo.TaxPaymentVoucherVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class TaxPaymentVoucherService {

    private final TaxPaymentVoucherManager taxPaymentVoucherManager;

    /**
     * 新增税务缴纳凭证记录
     */
    public TaxPaymentVoucherEntity addTaxPaymentVoucher(TenantInfo tenantInfo, TaxPaymentVoucherVo vo) {
        // 校验必填字段
        validateTaxPaymentVoucherVo(vo);

        // 检查是否已存在相同的supplier_corporation_id和tax_payment_period的记录
        TaxPaymentVoucherEntity existingEntity = taxPaymentVoucherManager
                .queryTaxPaymentVoucherBySupplierCorporationAndTaxPeriod(
                        vo.getSupplierCorporationId(), vo.getTaxPaymentPeriod());

        if (existingEntity != null) {
            // 如果存在记录，拼接文件ID
            String existingFileIds = existingEntity.getFileIds();
            String newFileIds = vo.getFileIds();

            if (newFileIds != null && !newFileIds.trim().isEmpty()) {
                if (existingFileIds != null && !existingFileIds.trim().isEmpty()) {
                    existingEntity.setFileIds(existingFileIds + "," + newFileIds);
                } else {
                    existingEntity.setFileIds(newFileIds);
                }
                return taxPaymentVoucherManager.updateTaxPaymentVoucher(convertEntityToVo(existingEntity));
            } else {
                return existingEntity;
            }
        } else {
            // 如果不存在记录，新增记录
            return taxPaymentVoucherManager.addTaxPaymentVoucher(tenantInfo, vo);
        }
    }

    /**
     * 更新税务缴纳凭证记录
     */
    public TaxPaymentVoucherEntity updateTaxPaymentVoucher(TenantInfo tenantInfo, TaxPaymentVoucherVo vo) {
        TaxPaymentVoucherEntity entity = taxPaymentVoucherManager.updateTaxPaymentVoucher(vo);

        return entity;
    }

    /**
     * 查询税务缴纳凭证记录详情
     */
    public TaxPaymentVoucherVo queryTaxPaymentVoucher(Long id) {
        TaxPaymentVoucherVo vo = taxPaymentVoucherManager.queryTaxPaymentVoucher(id);
        
        return vo;
    }

    /**
     * 删除税务缴纳凭证记录
     */
    public void deleteTaxPaymentVoucher(TenantInfo tenantInfo, Long id) {
        // 删除记录
        taxPaymentVoucherManager.deleteTaxPaymentVoucher(id);
    }

    /**
     * 校验税务缴纳凭证记录VO
     */
    private void validateTaxPaymentVoucherVo(TaxPaymentVoucherVo vo) {
        if (vo.getSupplierCorporationId() == null) {
            throw new BusinessException("作业主体ID不能为空");
        }
    }

    /**
     * 将Entity转换为Vo
     */
    private TaxPaymentVoucherVo convertEntityToVo(TaxPaymentVoucherEntity entity) {
        TaxPaymentVoucherVo vo = new TaxPaymentVoucherVo();
        vo.setId(entity.getId());
        vo.setSupplierCorporationId(entity.getSupplierCorporationId());
        vo.setTaxPaymentPeriod(entity.getTaxPaymentPeriod());
        vo.setFileIds(entity.getFileIds());
        vo.setSupplierId(entity.getSupplierId());
        return vo;
    }
}
