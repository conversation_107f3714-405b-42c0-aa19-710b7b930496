package com.olading.operate.labor.app.aspect;

import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.share.info.OwnerType;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.EvaluationException;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParseException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据权限切面类，用于处理基于 SpEL 表达式的字段权限校验、自动注入。
 */
@Aspect
@Component
@Slf4j
public class AuthorityDataScopAspect {

    private final ExpressionParser parser = new SpelExpressionParser();

    @Around("@annotation(AuthorityDataScopGuard)")
    public Object checkDataPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取数据权限
        Map<OwnerType, Set<Long>> dataScopes = Map.of();
        if (joinPoint.getTarget() instanceof BusinessController controller){
            dataScopes = controller.currentDataScope();
        } else {
            // 非业务控制器放行
            return joinPoint.proceed();
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();

        // 构建 SpEL 上下文
        EvaluationContext context = new StandardEvaluationContext();
        String[] paramNames = signature.getParameterNames();

        for (int i = 0; i < args.length; i++) {
            if (args[i] == null || args[i] instanceof HttpServletRequest) {
                continue;
            }
            if (paramNames != null && i < paramNames.length) {
                context.setVariable(paramNames[i], args[i]);
            }
            context.setVariable("arg" + i, args[i]);
        }

        AuthorityDataScopGuard guard = getGuard(joinPoint);

        // 校验 value() - 权限字段必须在当前用户权限范围内
        for (AuthorityDataScopGuard.Mapping mapping : guard.value()) {
            OwnerType ownerType = mapping.type();
            String spel = mapping.spel();

            Object result = parser.parseExpression(spel).getValue(context, Object.class);
            List<Long> dataIds = parseDataIds(result);
            Set<Long> allowedScopes = dataScopes.get(ownerType);

            if(mapping.allowNull() && dataIds.isEmpty()){
                continue;
            }

            if (allowedScopes == null || !allowedScopes.containsAll(dataIds)) {
                log.warn("无权操作该数据, {}: {}", ownerType, dataIds);
                throw new SecurityException("无权操作该数据");
            }
        }

        // 处理 query_value() - 自动注入或校验字段
        for (AuthorityDataScopGuard.QueryMapping mapping : guard.query_value()) {
            OwnerType ownerType = mapping.type();
            String spel = mapping.spel();

            // 解析表达式路径
            String targetPath = spel.substring(0, spel.lastIndexOf('.'));
            String fieldName = spel.substring(spel.lastIndexOf('.') + 1);

            Object target = null;
            try {
                target = parser.parseExpression(targetPath).getValue(context);
            } catch (Exception e) {
                log.warn("字段 {} 获取失败", targetPath);
            }
            if (target == null && !mapping.allowNull()) {
                log.warn("字段 {} 必须为 Set<Long> 类型", spel);
                throw new IllegalArgumentException("无权限");
            }

            if (target == null) {
                continue;
            }

            // 获取字段值
            Object fieldValue = null;
            try {
                fieldValue = parser.parseExpression(fieldName).getValue(context, target);
            } catch (Exception e) {
                log.warn("字段 {} 获取值失败", fieldName);
            }

            Set<Long> allowedScopes = dataScopes.get(ownerType);

            if(fieldValue == null && !mapping.allowNull()){
                throw new IllegalArgumentException("无权限");
            }

            // 只有字段不为 null 时才进行类型校验
            if (fieldValue != null) {
                if (!(fieldValue instanceof Collection<?> collection)) {
                    log.warn("字段 {} 必须为 Collection<Long> 类型", spel);
                    throw new IllegalArgumentException("字段 " + spel + " 必须为 Collection<Long> 类型");
                }

                // 确保集合中所有元素都是 Long 类型
                if (!collection.isEmpty() && !collection.stream().allMatch(o -> o instanceof Long)) {
                    log.warn("字段 {} 的元素必须全部为 Long 类型", spel);
                    throw new IllegalArgumentException("字段 " + spel + " 的元素必须全部为 Long 类型");
                }
            }

            //noinspection unchecked
            Collection<Long> dataIds = fieldValue == null ? Set.of() : (Collection<Long>) fieldValue;

            if (dataIds.isEmpty()) {
                // 字段为空，注入默认权限值（Set 类型）
                if (allowedScopes != null && !allowedScopes.isEmpty()) {
                    log.info("自动注入权限范围: {}: {}", ownerType, allowedScopes);
                    parser.parseExpression(fieldName).setValue(context, target, allowedScopes);
                }
            } else {
                // 校验权限：所有 ID 是否都在允许范围内
                if (allowedScopes == null || !allowedScopes.containsAll(dataIds)) {
                    log.warn("无权操作该数据, {}: {}", ownerType, dataIds);
                    throw new SecurityException("部分数据无权限");
                }
            }
        }

        // 先执行目标方法获取返回值
        Object result = joinPoint.proceed();

        if(controller.isAdmin()){
            // 管理员权限
            return result;
        }

        // 处理 return_value() - 校验返回值中的字段权限
        for (AuthorityDataScopGuard.ReturnMapping mapping : guard.return_value()) {
            OwnerType ownerType = mapping.type();
            String spel = mapping.spel();

            Object fieldValue = null;
            try {
                fieldValue = parser.parseExpression(spel).getValue(context, result);
            } catch (Exception e) {
                log.warn("返回值字段 {} 获取失败", spel);
            }
            if (fieldValue == null) {
                continue;
            }
            List<Long> dataIds = parseDataIds(fieldValue);
            Set<Long> allowedScopes = dataScopes.get(ownerType);

            if (allowedScopes == null || !allowedScopes.containsAll(dataIds)) {
                log.warn("返回值无权访问的数据 ID: {}: {}", ownerType, dataIds);
                throw new SecurityException("部分返回值数据无权限");
            }
        }

        return result;
    }

    /**
     * 将表达式结果转换为 List<Long> 类型的 ID 集合
     */
    private List<Long> parseDataIds(Object result) {
        if (result == null) {
            return List.of();
        }

        if (result instanceof Collection<?>) {
            return ((Collection<?>) result).stream()
                    .map(this::convertToLong)
                    .collect(Collectors.toList());
        } else if (result.getClass().isArray()) {
            return Arrays.stream((Object[]) result)
                    .map(this::convertToLong)
                    .collect(Collectors.toList());
        } else {
            return List.of(convertToLong(result));
        }
    }

    private Long convertToLong(Object value) {
        if (value instanceof Number) {
            return ((Number) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        } else {
            return null;
        }
    }

    private AuthorityDataScopGuard getGuard(ProceedingJoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature methodSignature) {
            Method method = methodSignature.getMethod();
            return method.getAnnotation(AuthorityDataScopGuard.class);
        }
        return null;
    }
}
