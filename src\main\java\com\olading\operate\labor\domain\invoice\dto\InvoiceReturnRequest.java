package com.olading.operate.labor.domain.invoice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "开票退回请求")
public class InvoiceReturnRequest {
    
    @NotBlank(message = "退回原因不能为空")
    @Schema(description = "退回原因")
    private String reason;

    @NotNull(message = "发票ID不能为空")
    @Schema(description = "发票ID")
    private Long invoiceId;
}