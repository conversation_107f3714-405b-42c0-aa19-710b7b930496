package com.olading.operate.labor.domain.bill.repository;

import com.olading.operate.labor.domain.bill.BillMasterEntity;
import com.olading.operate.labor.domain.bill.BillMasterStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 账单主表仓储接口
 */
@Repository
public interface BillMasterRepository extends JpaRepository<BillMasterEntity, Long>, JpaSpecificationExecutor<BillMasterEntity> {

    /**
     * 根据账单编号查找账单
     */
    Optional<BillMasterEntity> findByBillNoAndDeletedFalse(String billNo);

    /**
     * 查找指定条件的账单
     */
    @Query("SELECT b FROM BillMasterEntity b WHERE b.supplierId = :supplierId " +
           "AND b.customerId = :customerId AND b.contractId = :contractId " +
           "AND b.billMonth = :billMonth AND b.deleted = false")
    Optional<BillMasterEntity> findExistingBill(@Param("supplierId") Long supplierId,
                                               @Param("customerId") Long customerId,
                                               @Param("contractId") Long contractId,
                                               @Param("billMonth") LocalDate billMonth);

    /**
     * 根据供应商ID查找账单
     */
    Page<BillMasterEntity> findBySupplierIdAndDeletedFalse(Long supplierId, Pageable pageable);

    /**
     * 根据客户ID查找账单
     */
    Page<BillMasterEntity> findByCustomerIdAndDeletedFalse(Long customerId, Pageable pageable);

    /**
     * 根据账单状态查找账单
     */
    List<BillMasterEntity> findByBillStatusAndDeletedFalse(BillMasterStatus billStatus);

    /**
     * 根据账单月份范围查找账单
     */
    @Query("SELECT b FROM BillMasterEntity b WHERE b.billMonth >= :startMonth " +
           "AND b.billMonth <= :endMonth AND b.deleted = false ORDER BY b.billMonth DESC")
    List<BillMasterEntity> findByBillMonthBetween(@Param("startMonth") LocalDate startMonth,
                                                 @Param("endMonth") LocalDate endMonth);

    /**
     * 统计指定条件的账单数量
     */
    @Query("SELECT COUNT(b) FROM BillMasterEntity b WHERE b.supplierId = :supplierId " +
           "AND b.customerId = :customerId AND b.billStatus = :billStatus AND b.deleted = false")
    long countByCondition(@Param("supplierId") Long supplierId,
                         @Param("customerId") Long customerId,
                         @Param("billStatus") BillMasterStatus billStatus);
}