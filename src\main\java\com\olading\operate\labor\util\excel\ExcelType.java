package com.olading.operate.labor.util.excel;

/**
 * <AUTHOR>
 * @date 2022/3/27 16:09
 */
public enum ExcelType {
    HSSF(".xls"),
    XSSF(".xlsx");

    private String extension;

    public String getExtension() {
        return extension;
    }

    public String toRealFileName(String filename) {
        if (null == filename) {
            return "null" + extension;
        }

        return filename.replaceAll("\\.(?i)xlsx?\\s*$", "") + extension;
    }

    private ExcelType(String extension) {
        this.extension = extension;
    }
}
