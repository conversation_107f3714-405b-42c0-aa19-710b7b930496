package com.olading.operate.labor.app.web.biz.protocol.vo;


import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolTempFiledEntity;
import com.olading.operate.labor.domain.share.signing.common.Field;
import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import com.olading.operate.labor.domain.share.signing.enums.FieldType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

@Schema(description="模板字段")
@Data
public class TemplateFieldParam {

    @Schema(description = "字段ID 编辑需要传")
    private Long id;

    @Schema(description = "签署方 SEAL-企业签署方 SIGN-个人签署方")
    private EnumOperateType signatory;

    @Schema(description = "字段名")
    private String name;

    @Schema(description = "域默认值")
    private String value;

    @Schema(description = "关联项编码")
    private String relationCode;

    @Schema(description = "关联项组编码")
    private String relationGroup;

    @Schema(description = "关联项名")
    private String relationName;

    @Schema(description = "域类型 SEAL-企业签章 SIGN-个人签章 DATE-日期 FIELD-填充域")
    private String fieldType;


    public void doValidate() {
        if (StringUtils.isBlank(name)) throw new ApiException("请填写名称", ApiException.API_PARAM_ERROR);
        if (!CorporationProtocolTempFiledEntity.SIGNATORIES.contains(signatory)) throw new ApiException("请选择签署方",  ApiException.API_PARAM_ERROR);
    }

    public boolean add() {
        return id == null;
    }


    public void checkValid(Set<String> fieldNames) {
        if (StringUtils.isBlank(name)) throw new ApiException("请填写名称", ApiException.API_PARAM_ERROR);
        if (fieldNames.contains(name)) throw new ApiException(String.format("%s已选，不用重复添加了哦", name), ApiException.API_PARAM_ERROR);
        fieldNames.add(name);
    }

    public Field to() {
        Field field = new Field();
        field.setName(name);
        field.setType(FieldType.TEXT);
        field.setValue(StringUtils.isBlank(relationName) ? value : "");
        return field;
    }

//    public CorporationProtocolTempFiledEntity toTempFieldEntity(TemplateController.SupplierAndUser supplierAndUser) {
//        CorporationProtocolTempFiledEntity companyField = new CorporationProtocolTempFiledEntity();
//        companyField.setTenantId(supplierAndUser.getTenantId());
//        companyField.setSupplierId(supplierAndUser.getSupplier().getId());
//        companyField.setFieldName(name);
//        companyField.setFieldCode(value);
//        companyField.setTemplateStepId();
////        companyField.setRelationCode(relationCode);
////        companyField.setRelationGroup(relationGroup);
////        companyField.setRelationName(relationName);
//        companyField.setSignatory(signatory);
//        return companyField;
//    }
}
