package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.share.authority.QRoleEntity;
import com.olading.operate.labor.domain.share.authority.QRoleMemberEntity;
import com.olading.operate.labor.domain.share.authority.QSupplierMemberEntity;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.olading.operate.labor.domain.share.authority.SupplierMemberEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.swing.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

public class SupplierCorporationQuery implements EntityQuery<QueryFilter<SupplierCorporationQuery.Filters>, SupplierCorporationEntity> {

    private final QSupplierCorporationEntity t1 = QSupplierCorporationEntity.supplierCorporationEntity;


    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }


    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getName() != null) {
            criteria.and(t1.name.like(JpaUtils.fullLike(filters.getFilters().getName())));
        }
        if (filters.getFilters().getSocialCreditCode() != null) {
            criteria.and(t1.socialCreditCode.eq(filters.getFilters().getSocialCreditCode()));
        }
        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t1.supplierId.goe(filters.getFilters().getSupplierId()));
        }
        if (filters.getFilters().getCreateTimeEnd() != null && filters.getFilters().getCreateTimeBegin() != null) {
            criteria.and(t1.createTime.between(filters.getFilters().getCreateTimeBegin(), filters.getFilters().getCreateTimeEnd()));
        }
        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.eq(filters.getFilters().getId()));
        }else if (filters.getFilters().getCorporationIds() != null){
            criteria.and(t1.id.in(filters.getFilters().getCorporationIds()));
        }

        query.select(t1)
                .from(t1).where(criteria);
    }

    @Override
    public SupplierCorporationEntity transform(Object v) {
        return (SupplierCorporationEntity) v;
    }

    @Data
    public static class Filters {

        private String name;
        private String socialCreditCode;
        private Long id;
        private Long supplierId;
        private LocalDateTime createTimeBegin;
        private LocalDateTime createTimeEnd;
        private Set<Long> corporationIds;
    }

}
