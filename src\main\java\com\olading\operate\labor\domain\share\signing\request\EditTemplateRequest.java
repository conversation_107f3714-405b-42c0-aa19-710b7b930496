package com.olading.operate.labor.domain.share.signing.request;

import com.olading.operate.labor.domain.share.signing.common.Control;
import com.olading.operate.labor.domain.share.signing.common.Field;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/30
 * @description ：
 * @version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@Schema(description= "编辑模板请求参数")
public class EditTemplateRequest extends BaseRequest<EditTemplateRequest> {

    @Schema(description = "模板编号", required = true)
    private Long templateId;
    @Schema(description = "模板域")
    private java.util.List<Field> fields;
    @Schema(description = "模板控件", required = true)
    private java.util.List<Control> controls;

}
