package com.olading.operate.labor.util.crypto;

import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

public class Aes {

    private static final Charset CHARSET = StandardCharsets.UTF_8;
    private static final String AES_ECB = "AES/ECB/PKCS5Padding";

    public static Crypto makeCrypto(String key) {
        return makeCrypto(toKey(key));
    }

    public static Crypto makeCrypto(byte[] key) {
        return new AesCrypto(key);
    }

    public static String encrypt(String plaintext, String key) {
        return encrypt(plaintext, toKey(key));
    }

    public static String encrypt(String plaintext, byte[] key) {
        return Base64.getEncoder().encodeToString(encrypt(plaintext.getBytes(CHARSET), key));
    }

    public static byte[] encrypt(byte[] plaintext, byte[] key) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            Cipher cipher = Cipher.getInstance(AES_ECB);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            return cipher.doFinal(plaintext);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | IllegalBlockSizeException | BadPaddingException |
                 InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }


    public static String decrypt(String ciphertext, String key) {
        return decrypt(ciphertext, toKey(key));
    }

    public static String decrypt(String ciphertext, byte[] key) {
        return new String(decrypt(Base64.getDecoder().decode(ciphertext), key), CHARSET);
    }

    public static byte[] decrypt(byte[] ciphertext, byte[] key) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            Cipher cipher = Cipher.getInstance(AES_ECB);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            return cipher.doFinal(ciphertext);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | IllegalBlockSizeException | BadPaddingException |
                 InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }

    private static byte[] toKey(String key) {
        return Arrays.copyOf(DigestUtils.getSha1Digest().digest(key.getBytes()), 32);
    }

    static class AesCrypto implements Crypto {

        private final byte[] key;

        public AesCrypto(byte[] ciphertext) {
            this.key = Arrays.copyOf(ciphertext, ciphertext.length);
        }

        @Override
        public String encrypt(String plaintext) {
            return Aes.encrypt(plaintext, key);
        }

        @Override
        public byte[] encrypt(byte[] plaintext) {
            return Aes.encrypt(plaintext, key);
        }

        @Override
        public String decrypt(String ciphertext) {
            return Aes.decrypt(ciphertext, key);
        }

        @Override
        public byte[] decrypt(byte[] ciphertext) {
            return Aes.decrypt(ciphertext, key);
        }
    }
}
