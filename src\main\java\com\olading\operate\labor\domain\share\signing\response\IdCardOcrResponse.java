package com.olading.operate.labor.domain.share.signing.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "身份证OCR响应参数")
public class IdCardOcrResponse {

    @Schema(description = "身份证号码", required = true)
    private String idCardNo;
    @Schema(description = "姓名", required = true)
    private String name;
    @Schema(description = "地址", required = true)
    private String address;
    @Schema(description = "性别", required = true)
    private String gender;
    @Schema(description = "民族", required = true)
    private String nation;
    @Schema(description = "出生日期", required = true)
    private String birthday;

}
