package com.olading.operate.labor.domain.bill.dto;

import com.olading.operate.labor.domain.bill.vo.BillMasterVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 其他费用导入预览结果
 */
@Data
public class BillOtherFeeImportPreviewResult {
    
    /**
     * 导入是否成功
     */
    private boolean success;
    
    /**
     * 总行数
     */
    private int totalRows;
    
    /**
     * 有效数据行数
     */
    private int validRows;
    
    /**
     * 错误行数
     */
    private int errorRows;
    
    /**
     * 费用总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 涉及人数
     */
    private int personCount;

    /**
     * 预览数据（前10条）
     */
    private List<BillOtherFeeImportRow> excelData;
    
    /**
     * 预览数据（前10条）
     */
    private List<BillOtherFeeImportRow> previewData;
    
    /**
     * 验证错误信息
     */
    private List<ImportValidationError> errors;
    
    /**
     * 账单信息
     */
    private BillMasterVO billInfo;



}