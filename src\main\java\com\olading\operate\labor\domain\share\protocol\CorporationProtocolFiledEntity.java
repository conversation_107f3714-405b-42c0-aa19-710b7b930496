package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "t_corporation_protocol_filed")
public class CorporationProtocolFiledEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Comment("平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Size(max = 20)
    @Comment("租户")
    @Column(name = "tenant_id", length = 20)
    private String tenantId;

    @Comment("合同id")
    @Column(name = "protocol_id")
    private Long protocolId;

    @Comment("步骤id")
    @Column(name = "step_id")
    private Long stepId;

    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id")
    private Long supplierCorporationId;

    @Size(max = 32)
    @Comment("字段名")
    @Column(name = "field_name", length = 32)
    private String fieldName;

    @Size(max = 32)
    @Comment("字段编码")
    @Column(name = "relation_code", length = 32)
    private String relationCode;

    @Size(max = 32)
    @Comment("字段值")
    @Column(name = "field_value", length = 32)
    private String fieldValue;

    @Comment("version")
    @Column(name = "version")
    private Integer version;

    @Comment("创建时间")
    @Column(name = "create_time")
    private Instant createTime;

    @Comment("更新时间")
    @Column(name = "modify_time")
    private Instant modifyTime;

    public CorporationProtocolFiledEntity(TenantInfo tenant) {
        setTenant(tenant);
    }

    public CorporationProtocolFiledEntity() {
    }

    public TenantInfo getTenant() {
        if (tenantId == null) {
            return null;
        }
        return TenantInfo.parse(tenantId);
    }

    protected void setTenant(TenantInfo tenant) {
        this.tenantId = tenant.toTenantId();
    }

}