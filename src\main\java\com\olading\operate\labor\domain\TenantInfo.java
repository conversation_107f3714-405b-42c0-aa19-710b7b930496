package com.olading.operate.labor.domain;

import java.util.Objects;

/**
 * 表示一个租户
 */
public class TenantInfo {

    private static final String NO_BODY = "-1";
    private final TenantType type;
    private final String id;

    public TenantInfo(TenantType type, String id) {
        this.type = Objects.requireNonNull(type);
        this.id = Objects.requireNonNull(id);

        if (this.id.length() > 16) {
            throw new IllegalArgumentException("tenant id长度最多16个字符");
        }
        if (this.id.contains(":")) {
            throw new IllegalArgumentException("tenant id不能包含冒号");
        }
    }

    public static TenantInfo parse(String s) {
        String[] arr = s.split(":");
        return new TenantInfo(TenantType.values()[Integer.parseInt(arr[0])], arr[1]);
    }

    public static TenantInfo of(com.olading.boot.core.business.Tenant tenant) {
        return parse(tenant.getId());
    }

    public static TenantInfo nobody(TenantType type) {
        return new TenantInfo(type, NO_BODY);
    }

    public static TenantInfo ofPersonal(long supplierId) {
        return new TenantInfo(TenantType.PERSONAL, String.valueOf(supplierId));
    }

    public static TenantInfo ofCustomer(long customerId) {
        return new TenantInfo(TenantType.CUSTOMER, String.valueOf(customerId));
    }


    public static TenantInfo ofSupplier(long supplierId) {
        return new TenantInfo(TenantType.SUPPLIER, String.valueOf(supplierId));
    }

    public static boolean isNobody(TenantInfo tenant) {
        return NO_BODY.equals(tenant.getId());
    }

    public TenantType getType() {
        return type;
    }

    public String getId() {
        return id;
    }

    public String toTenantId() {
        return type.ordinal() + ":" + id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TenantInfo tenant = (TenantInfo) o;
        return type == tenant.type && Objects.equals(id, tenant.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, id);
    }

    public com.olading.boot.core.business.Tenant toGeneral() {
        return com.olading.boot.core.business.Tenant.of(toTenantId());
    }

    public enum TenantType {

        /**
         * 管理后台
         */
        BOSS,

        /**
         * 服务商
         */
        SUPPLIER,

        /**
         * 客户
         */
        CUSTOMER,

        /**
         * 个人端
         */
        PERSONAL,

    }
}
