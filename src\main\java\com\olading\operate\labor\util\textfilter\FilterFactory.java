package com.olading.operate.labor.util.textfilter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/25 13:16
 */
public class FilterFactory {
    private final static Map<Class<? extends TextFilter>, TextFilter> filterMap = new HashMap<>();

    public static TextFilter createFilter(Class<? extends TextFilter> clazz) {
        if (!filterMap.containsKey(clazz))  {
            try {
                TextFilter filter = clazz.newInstance();
                filterMap.put(clazz, filter);
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }

        }

        return filterMap.get(clazz);
    }
}
