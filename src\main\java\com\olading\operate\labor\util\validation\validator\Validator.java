package com.olading.operate.labor.util.validation.validator;


import com.lanmaoly.util.lang.exception.ValidationException;

/**
 * <AUTHOR>
 * @date 2022/3/14 09:48
 */
public interface Validator {

    /**
     *
     * @param name
     */
    void setName(String name);

    /**
     *
     * @return
     */
    String getName();

    /**
     *
     * @return
     */
    boolean isRequired();

    /**
     *
     * @param required
     */
    void setRequired(boolean required);

    /**
     * 数据校验
     * @param o
     * @return
     * @throws ValidationException
     */
    boolean validate(Object o) throws ValidationException;
}
