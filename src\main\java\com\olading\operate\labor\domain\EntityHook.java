package com.olading.operate.labor.domain;

import jakarta.persistence.PostLoad;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreRemove;
import jakarta.persistence.PreUpdate;

import java.time.LocalDateTime;
import java.util.concurrent.Callable;

public class EntityHook {

    private static <T, R> R access(ThreadLocal<T> local, T v, Callable<R> function) {
        T old = local.get();
        try {
            local.set(v);
            return function.call();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            local.set(old);
        }
    }

    @PrePersist
    void onPrePersist(Object o) {

    }

    @PreUpdate
    void onPreUpdate(Object o) {
        if (o instanceof BaseEntity) {
            BaseEntity entity = (BaseEntity) o;
            entity.modifyTime = LocalDateTime.now();
        }
    }

    @PreRemove
    void onPreRemove(Object o) {

    }

    @PostLoad
    void onPostLoad(Object o) {

    }
}
