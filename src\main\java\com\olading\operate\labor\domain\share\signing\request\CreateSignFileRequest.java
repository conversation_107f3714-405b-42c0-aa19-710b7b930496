package com.olading.operate.labor.domain.share.signing.request;

import com.olading.operate.labor.domain.share.signing.common.FieldValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "创建待签名文件请求参数")
public class CreateSignFileRequest extends BaseRequest<CreateSignFileRequest> {


    @Schema(description = "模板ID", required = true)
    private Long templateId;
    @Schema(description = "文件名称", required = true)
    private String name;
    @Schema(description = "模板域填充数据", required = true)
    private List<FieldValue> fields;

}
