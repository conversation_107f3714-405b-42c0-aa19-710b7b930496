package com.olading.operate.labor.domain.share.user;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.Comment;

/**
 * 用户
 */
@Table(name = "t_user")
@Entity
public class UserEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Comment("用户名")
    @Column(name = "name", length = 20)
    private String name;

    @Comment("密码")
    @Column(name = "password", length = 128)
    private String password;

    @Size(max = 11)
    @Comment("手机号")
    @Column(name = "cellphone", length = 11)
    private String cellphone;

    @Comment("账户")
    @Column(name = "account_no", length = 20)
    private String accountNo;

    public UserEntity(TenantInfo tenant) {
        setTenant(tenant);
    }

    protected UserEntity() {
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    void setPassword(String password) {
        this.password = password;
    }

    public String getCellphone() {
        return cellphone;
    }

    void setCellphone(String cellphone) {
        this.cellphone = cellphone;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
}
