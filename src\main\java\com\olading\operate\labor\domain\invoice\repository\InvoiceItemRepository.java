package com.olading.operate.labor.domain.invoice.repository;

import com.olading.operate.labor.domain.invoice.InvoiceItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InvoiceItemRepository extends JpaRepository<InvoiceItemEntity, Long> {
    
    /**
     * 根据开票ID查询明细列表
     */
    List<InvoiceItemEntity> findByInvoiceIdAndDeletedFalse(Long invoiceId);
    
    /**
     * 根据账单ID查询开票明细
     */
    List<InvoiceItemEntity> findByBillIdAndDeletedFalse(Long billId);
}