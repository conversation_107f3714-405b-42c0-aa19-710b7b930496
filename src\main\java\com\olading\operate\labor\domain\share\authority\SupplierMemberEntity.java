package com.olading.operate.labor.domain.share.authority;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 灵工平台服务运营方成员
 */
@Table(name = "t_supplier_member")
@Entity
@Getter
@Setter
public class SupplierMemberEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @NotNull
    @Comment("服务运营方编号")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @Comment("用户ID")
    @Column(name = "user_id")
    private Long userId;

    @Comment("姓名")
    @Column(name = "name", length = 20)
    private String name;

    @Comment("手机号")
    @Column(name = "cellphone", length = 11)
    private String cellphone;

    @Comment("上一次使用的时间")
    @Column(name = "last_used_time")
    private LocalDateTime lastUsedTime;

    @Comment("是否禁用")
    @Column(name = "disabled")
    private Boolean disabled;

    @Embedded
    private OwnedByFragment ownedBy;

    public SupplierMemberEntity (Long supplierId, OwnerType ownerType, Long ownerId, Long userId){
        super();
        this.supplierId = supplierId;
        this.userId = userId;
        this.ownedBy = new OwnedByFragment(ownerType, ownerId);
        setTenant(TenantInfo.ofSupplier(supplierId));
    }

    protected SupplierMemberEntity() {
    }
}
