package com.olading.operate.labor.domain.share.signing.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "创建模板响应参数")
public class CreateTemplateResponse {

    @Schema(description = "模板ID")
    private Long id;
    @Schema(description = "模板编辑url，访问此URL来编辑模板")
    private String url;
    @Schema(description = "通过此url来上传模板文件数据")
    private String uploadUrl;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
