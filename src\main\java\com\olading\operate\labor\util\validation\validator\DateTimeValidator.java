package com.olading.operate.labor.util.validation.validator;


import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.DateTime;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class DateTimeValidator extends BaseValidator<DateTime> {
    private String format = "yyyy-MM-dd HH:mm:ss";

    public DateTimeValidator() {}

    public DateTimeValidator(String name) {
        setName(name);
    }

    public void setFormat(String format) {
        this.format = format;
    }

    @Override
    public void initialize(DateTime constraintAnnotation) {
        format = constraintAnnotation.format();
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        super.initialize(constraintAnnotation);
    }

    /**
     * 一般用于传参，严格校验
     * @param o
     * @return
     */
    @Override
    protected boolean constraintCheck(Object o) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        try {
            LocalDateTime.parse(String.valueOf(o), df);
        } catch (Exception e) {
            throw new ValidationException(getName(), String.format("的格式错误，标准格式为：%s", format));
        }

        return true;
    }
}
