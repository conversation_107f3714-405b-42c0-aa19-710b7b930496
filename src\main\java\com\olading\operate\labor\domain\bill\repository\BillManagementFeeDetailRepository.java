package com.olading.operate.labor.domain.bill.repository;

import com.olading.operate.labor.domain.bill.BillManagementFeeDetailEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 账单管理费明细仓储接口
 */
@Repository
public interface BillManagementFeeDetailRepository extends JpaRepository<BillManagementFeeDetailEntity, Long> {
    /**
     * 根据账单主表ID分页查询管理费明细
     */
    Page<BillManagementFeeDetailEntity> findByBillMasterIdAndDeletedFalseOrderByCreateTimeDesc(Long billMasterId, Pageable pageable);

    /**
     * 根据账单分类ID查询管理费明细
     */
    List<BillManagementFeeDetailEntity> findByBillCategoryIdAndDeletedFalse(Long billCategoryId);

    /**
     * 根据身份证号和账单月份查询管理明细
     */
    @Query("SELECT d FROM BillManagementFeeDetailEntity d WHERE d.idCard = :idCard " +
           "AND d.billMonth >= :startMonth AND d.billMonth <= :endMonth AND d.deleted = false")
    List<BillManagementFeeDetailEntity> findByIdCardAndBillMonthBetween(@Param("idCard") String idCard,
                                                                @Param("startMonth") LocalDate startMonth,
                                                                @Param("endMonth") LocalDate endMonth);

    Long deleteByBillMasterId(Long billId);
}