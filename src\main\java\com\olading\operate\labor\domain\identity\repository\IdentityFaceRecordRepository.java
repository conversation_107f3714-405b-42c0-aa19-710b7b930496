package com.olading.operate.labor.domain.identity.repository;

import com.olading.operate.labor.domain.identity.IdentityFaceRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 人脸识别记录仓储接口
 */
@Repository
public interface IdentityFaceRecordRepository extends JpaRepository<IdentityFaceRecordEntity, Long>, JpaSpecificationExecutor<IdentityFaceRecordEntity> {

    /**
     * 根据记录编号查找记录
     */
    Optional<IdentityFaceRecordEntity> findByRecordNoAndDeletedFalse(String recordNo);

    /**
     * 根据用户ID查找人脸识别记录
     */
    Page<IdentityFaceRecordEntity> findByUserIdAndDeletedFalse(Long userId, Pageable pageable);

    /**
     * 根据供应商ID查找人脸识别记录
     */
    Page<IdentityFaceRecordEntity> findBySupplierIdAndDeletedFalse(Long supplierId, Pageable pageable);

    /**
     * 根据作业主体ID查找人脸识别记录
     */
    Page<IdentityFaceRecordEntity> findByCorporationIdAndDeletedFalse(Long corporationId, Pageable pageable);

    /**
     * 根据身份证号查找人脸识别记录
     */
    List<IdentityFaceRecordEntity> findByIdCardAndDeletedFalse(String idCard);

    /**
     * 根据认证场景查找记录
     */
    List<IdentityFaceRecordEntity> findByAuthSceneAndDeletedFalse(String authScene);
}