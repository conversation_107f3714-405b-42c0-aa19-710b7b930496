package com.olading.operate.labor.util.crypto;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class HashPassword {


    /**
     * 使用SHA-256对密码进行加密
     *
     * @param password 用户输入的密码
     * @return 加密后的密码
     */
    public static String hashPassword(String password) {
        if (password == null || password.isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(password.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 校验密码是否与加密后的密码匹配
     *
     * @param rawPassword     用户输入的原始密码
     * @param encodedPassword 存储在数据库中的加密密码
     * @return 密码是否匹配
     */
    public static boolean checkPassword(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            throw new IllegalArgumentException("密码不能为空");
        }
        String hashedRawPassword = hashPassword(rawPassword);
        return hashedRawPassword.equals(encodedPassword);
    }


    public static void main(String[] args) {
        String password = "12345678";
        String hashedPassword = hashPassword(password);
        String storedHashedPassword = hashPassword(password);
        System.out.println("加密后的密码: " + hashedPassword);
        System.out.println("密码匹配: " + checkPassword("12345678", storedHashedPassword)); // true
        System.out.println("密码匹配: " + checkPassword("wrongPassword", storedHashedPassword)); // false

    }
}
