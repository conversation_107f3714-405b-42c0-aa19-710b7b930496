package com.olading.operate.labor.domain.proxy.channel;

import cn.hutool.core.lang.UUID;
import com.olading.operate.labor.domain.share.info.InfoManager;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/10 19:15
 */
@Component
@RequiredArgsConstructor
@Transactional
public class ChannelRemitOrderRepo {
    private final EntityManager em;

    public ChannelRemitOrderEntity createRemitOrder(ChannelRemitRequest  request){
        final ChannelRemitOrderEntity channelRemitOrderEntity = new ChannelRemitOrderEntity();
        channelRemitOrderEntity.setPayChannel(request.getPayChannel());
        channelRemitOrderEntity.setStatus(RemitStatusEnum.INIT);
        channelRemitOrderEntity.setSupplierId(request.getSupplierId());
        channelRemitOrderEntity.setSupplierCorporationId(request.getSupplierCorporationId());
        channelRemitOrderEntity.setProxyOrderId(request.getProxyOrderId());
        channelRemitOrderEntity.setRequestNo(UUID.randomUUID(true).toString());
        channelRemitOrderEntity.setName(request.getName());
        channelRemitOrderEntity.setIdCard(request.getIdCard());
        channelRemitOrderEntity.setCellphone(request.getCellphone());
        channelRemitOrderEntity.setBankCard(request.getBankCard());
        channelRemitOrderEntity.setBankCode(request.getBankCode());
        channelRemitOrderEntity.setBankName(request.getBankName());
        channelRemitOrderEntity.setAmount(request.getAmount());
        channelRemitOrderEntity.setRemark(request.getRemark());
        em.persist(channelRemitOrderEntity);
        return channelRemitOrderEntity;
    }

    public void processRemitAcceptFail(ChannelRemitOrderEntity order,String errorCode, String errorMessage) {
        order.setStatus(RemitStatusEnum.FAIL);
        order.setErrorCode(errorCode);
        order.setErrorReason(errorMessage);
        order.setFinishTime(LocalDateTime.now());
        em.merge(order);
    }

    public ChannelRemitOrderEntity getRemitOrder(Long channelRemitOrderId) {
        ChannelRemitOrderEntity order = em.find(ChannelRemitOrderEntity.class, channelRemitOrderId);
        if (order == null) {
            throw new IllegalStateException("代付订单不存在");
        }
        return order;
    }

    public void saveRemitOrder(ChannelRemitOrderEntity order) {
        em.merge(order);
    }

    public void saveVoucher(Long remitId, String fileId) {
        ChannelRemitOrderEntity order = em.find(ChannelRemitOrderEntity.class, remitId);
        if (order == null){
            throw new IllegalStateException("代付订单不存在");
        }
        order.setVoucher(fileId);
        em.merge(order);
    }
}
