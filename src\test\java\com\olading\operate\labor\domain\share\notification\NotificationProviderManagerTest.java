package com.olading.operate.labor.domain.share.notification;

import com.olading.operate.labor.BaseTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class NotificationProviderManagerTest extends BaseTest {

    @Autowired
    private NotificationManager notificationManager;

    @Disabled
    @Test
    public void test() throws InterruptedException {

        Map<String, String> form = new HashMap<>();
        form.put("req", "中文哈哈");
        form.put("sign", "sign");


        for (int i = 0; i < 1; i++) {
            notificationManager.send("a", "https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/fund/request-io/receive/notify", form);
        }

        Thread.sleep(1000);
    }


    @Disabled
    @Test
    public void fail() throws InterruptedException {

        Map<String, String> req = new HashMap<>();
        req.put("req", "abc");
        req.put("sign", "sign");

        for (int i = 0; i < 1; i++) {
            notificationManager.send("a", "http://172.19.60.157", req);
        }

        Thread.sleep(1000000);
    }
}
