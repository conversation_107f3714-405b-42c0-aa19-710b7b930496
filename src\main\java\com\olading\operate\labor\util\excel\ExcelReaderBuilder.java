package com.olading.operate.labor.util.excel;


import com.olading.operate.labor.util.exception.ExcelIOException;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.InputStream;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2022/2/22 16:16
 */
public class ExcelReaderBuilder {
    private ExcelReader excelReader;

    public ExcelReaderBuilder(Class<? extends ExcelRow> pojoClass) {
        this.excelReader = new ExcelReader(pojoClass);
    }

    public ExcelReaderBuilder titleRow(int titleRow) {
        excelReader.setTitleRow(titleRow);
        return this;
    }

    public ExcelReaderBuilder headerGroupRow(int headerGroupRow) {
        excelReader.setHeaderGroupRow(headerGroupRow);
        return this;
    }

    public ExcelReaderBuilder headerRow(int headerRow) {
        excelReader.setHeaderRow(headerRow);
        return this;
    }

    public ExcelReaderBuilder needVerify(boolean needVerify) {
        excelReader.setNeedVerify(needVerify);
        return this;
    }

    public ExcelReaderBuilder autoFilterOtherText(boolean autoFilterOtherText) {
        excelReader.setAutoFilterOtherText(autoFilterOtherText);
        return this;
    }

    public ExcelReaderBuilder removeCustomColumn(boolean removeCustomColumn) {
        excelReader.setRemoveCustomColumn(removeCustomColumn);
        return this;
    }

    public ExcelReaderBuilder sheetIndex(int sheetIndex) {
        excelReader.setSheetIndex(sheetIndex);
        return this;
    }

    public ExcelReaderBuilder maxDataRows(int maxDataRows) {
        excelReader.setMaxDataRows(maxDataRows);
        return this;
    }

    public ExcelReaderBuilder headerMapping(ExcelHeaderMappingRule headerMapping) {
        excelReader.setHeaderMapping(headerMapping);
        return this;
    }


    public ExcelReaderBuilder rowHandler(Consumer<ExcelRow> rowHandler) {
        excelReader.setRowHandler(rowHandler);
        return this;
    }


    public ExcelReaderBuilder headerHandler(Consumer<ExcelColumnEntity> headerHandler) {
        excelReader.setHeaderHandler(headerHandler);
        return this;
    }

    public ExcelReader build() {
        return excelReader;
    }

    public ExcelResult read(InputStream inputStream) throws ExcelIOException {
        return excelReader.read(inputStream);
    }

    public ExcelResult read(Workbook workbook)  throws ExcelIOException{
        return excelReader.read(workbook);
    }

    public ExcelResult readResourceFile(String filePath) {
        return excelReader.readResourceFile(filePath);
    }

    public ExcelHeader readHeader(InputStream inputStream) throws ExcelIOException {
        return excelReader.readHeader(inputStream);
    }
}
