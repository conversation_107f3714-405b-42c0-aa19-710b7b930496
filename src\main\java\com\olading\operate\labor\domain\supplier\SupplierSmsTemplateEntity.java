package com.olading.operate.labor.domain.supplier;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("平台短信配置")
@Entity
@Table(name = "t_supplier_sms_template")
public class SupplierSmsTemplateEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;


    @Comment("短信业务类型")
    @Column(name = "BUSINESS_TYPE", length = 64)
    @Enumerated(EnumType.STRING)
    private SmsBusinessType businessType;

    @Size(max = 256)
    @Comment("短信模板编码")
    @Column(name = "TEMPLATE_CODE", length = 256)
    private String templateCode;

}