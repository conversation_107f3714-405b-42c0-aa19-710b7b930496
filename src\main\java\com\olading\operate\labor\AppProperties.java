package com.olading.operate.labor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties(prefix = "app")
@Data
public class AppProperties {

    private boolean mock = false;

    /**
     * 管理员手机号
     */
    private String adminCellphone = "";

    /**
     * 系统部署的baseUrl
     */
    private String apiBaseUrl = "/api/";

    /**
     * 系统的主密钥
     */
    private String masterKey;

    private Map<String, Long> sequenceDeclare = Map.of();


    /**
     * 云签接口请求地址
     */
    private String signingApiUrl;

    /**
     * 云签使用的租户ID
     */
    private String signingTenantId;

    /**
     * 云签商户clientKey
     */
    private String signingClientKey;

    /**
     * 云签商户秘钥
     */
    private String signingSecretKey;
    /**
     * 云签模板上传地址
     */
    private String signingTempUploadUrl;

}
