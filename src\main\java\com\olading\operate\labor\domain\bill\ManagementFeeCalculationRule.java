package com.olading.operate.labor.domain.bill;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 管理费计算规则枚举
 */
@Getter
@RequiredArgsConstructor
public enum ManagementFeeCalculationRule {
    
    /**
     * 按发放人数计算
     */
    BY_PERSON_COUNT("BY_PERSON_COUNT", "按发放人数计算"),
    
    /**
     * 按应发金额比例计算
     */
    BY_GROSS_SALARY_RATE("BY_GROSS_SALARY_RATE", "按应发金额比例计算"),
    
    /**
     * 按实发金额比例计算
     */
    BY_REAL_SALARY_RATE("BY_REAL_SALARY_RATE", "按实发金额比例计算"),
    
    /**
     * 固定金额
     */
    FIXED_AMOUNT("FIXED_AMOUNT", "固定金额");
    
    private final String code;
    private final String description;
    
    public static ManagementFeeCalculationRule fromCode(String code) {
        for (ManagementFeeCalculationRule rule : values()) {
            if (rule.code.equals(code)) {
                return rule;
            }
        }
        throw new IllegalArgumentException("Unknown management fee calculation rule: " + code);
    }
}