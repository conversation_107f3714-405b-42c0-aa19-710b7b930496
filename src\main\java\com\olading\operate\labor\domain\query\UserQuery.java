package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.domain.share.info.PersonInfoEntity;
import com.olading.operate.labor.domain.share.info.QPersonInfoEntity;
import com.olading.operate.labor.domain.share.user.QUserEntity;
import com.olading.operate.labor.domain.share.user.UserData;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;

public class UserQuery implements EntityQuery<QueryFilter<UserQuery.Filters>, UserData> {

    private final QUserEntity t1 = QUserEntity.userEntity;
    private final QPersonInfoEntity t2 = QPersonInfoEntity.personInfoEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getTenant() != null) {
            criteria.and(t1.tenantId.eq(filters.getFilters().getTenant().toTenantId()));
        }

        query.select(t1, t2)
                .from(t1)
                .leftJoin(t2).on(t1.id.eq(t2.ownedBy.ownerId).and(t2.ownedBy.ownerType.eq(OwnerType.USER)));

        query.where(criteria);
    }

    @Override
    public UserData transform(Object v) {

        Tuple tuple = (Tuple) v;
        UserEntity user = tuple.get(t1);
        PersonInfoEntity person = tuple.get(t2);

        if (user == null) {
            throw new NullPointerException();
        }

        UserData data = new UserData(user);
        if (person != null) {
            data.setInfo(new PersonInfoData(person));
        }

        return data;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    public static class Filters {

        private TenantInfo tenant;

    }

}
