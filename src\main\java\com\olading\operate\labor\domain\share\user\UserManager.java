package com.olading.operate.labor.domain.share.user;


import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.info.InfoManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.util.crypto.HashPassword;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.function.Function;
import java.util.stream.Collectors;

@Transactional
@Component
@RequiredArgsConstructor
public class UserManager {

    private final EntityManager em;
    private final InfoManager infoManager;


    public UserEntity addUser(TenantInfo tenant, String cellphone) {
        UserEntity user = getUserByCellphone(tenant, cellphone);
        if (user == null) {
            user = new UserEntity(tenant);
            user.setCellphone(cellphone);
            user = em.merge(user);
        }
        return user;
    }

    public void initPassword(Long userId, String confirmPassword) {
        UserEntity user = getUser(userId);
        if (user == null) {
            throw new IllegalStateException("用户不存在");
        }
        user.setPassword(HashPassword.hashPassword(confirmPassword));
        em.merge(user);
    }

    public void changePassword(Long userId, String oladPassword, String confirmPassword) {
        UserEntity user = getUser(userId);
        if (user == null) {
            throw new IllegalStateException("用户不存在");
        }
        if (!HashPassword.checkPassword(oladPassword, user.getPassword())) {
            throw new IllegalStateException("密码错误");
        }
        user.setPassword(HashPassword.hashPassword(confirmPassword));
        em.merge(user);
    }

    public UserEntity addUser(TenantInfo tenant, String cellphone, String name) {
        UserEntity user = getUserByCellphone(tenant, cellphone);
        if (user == null) {
            user = new UserEntity(tenant);
            user.setCellphone(cellphone);
            user.setName(name);
            user = em.merge(user);
        }
        if ( StringUtils.isNotBlank(user.getName()) && StringUtils.equals(user.getName(), name) ){
            throw new BusinessException("用户已存在,且信息不一致,请联系管理员");
        }

        if(StringUtils.isBlank(user.getName()) ){
            user.setName(name);
            user = em.merge(user);
        }

        return user;
    }

    public UserEntity getUser(TenantInfo tenant, long userId) {
        UserEntity user = em.find(UserEntity.class, userId);
        if (user == null || !user.getTenantId().equals(tenant.toTenantId())) {
            return null;
        }
        return user;
    }

    public void deleteUser(TenantInfo tenant, long userId) {
        UserEntity user = getUser(tenant, userId);
        if (user != null) {
            em.remove(user);
        }
    }

    public UserEntity getUserByCellphone(TenantInfo tenant, String cellphone) {
        return queryUser(t -> t.cellphone.eq(cellphone).and(t.tenantId.eq(tenant.toTenantId()))).fetchOne();
    }

    public void identifyUser(long userId, String idCard, String name) {
        final UserEntity user = getUser(userId);
        if (user == null) {
            throw new IllegalStateException("用户不存在");
        }
        user.setName(name);
        em.merge(user);
        PersonInfoData info = new PersonInfoData();
        info.setCellphone(user.getCellphone());
        info.setIdCard(idCard);
        info.setName(name);
        this.setUserInfo(userId, info);
    }



    public void setUserInfo(long userId, PersonInfoData info) {
        infoManager.save(OwnerType.USER, userId, info);
    }

    public PersonInfoData getUserInfo(long userId) {
        return infoManager.getPersonInfo(OwnerType.USER, userId);
    }

    public UserEntity requireUser(long userId) {
        UserEntity user = getUser(userId);
        if (user == null) {
            throw new IllegalStateException("用户不存在");
        }
        return user;
    }

    public UserEntity getUser(long userId) {
        return em.find(UserEntity.class, userId);
    }


    private JPAQuery<UserEntity> queryUser(Function<QUserEntity, Predicate> condition) {
        QUserEntity t = QUserEntity.userEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }


    public UserEntity getUserByAccount(String accontNo) {
        return queryUser(t -> t.accountNo.eq(accontNo)).fetchOne();
    }

    public void changePhone(Long userId,TenantInfo tenantInfo, String cellphone) {
        UserEntity user = getUser(userId);
        if (user == null) {
            throw new IllegalStateException("用户不存在");
        }
        if (!TenantInfo.isNobody(tenantInfo) && !user.getTenantId().equals(tenantInfo.toTenantId())) {
            throw new IllegalStateException("用户不属于当前租户");
        }
        if (getUserByCellphone(tenantInfo, cellphone) != null) {
            throw new IllegalStateException("手机号已存在");
        }
        user.setCellphone(cellphone);
        final PersonInfoData personInfoData = new PersonInfoData();
        personInfoData.setCellphone(cellphone);
        infoManager.save(OwnerType.USER, user.getId(),personInfoData);
        em.merge(user);
    }

    public UserEntity login(@NotEmpty String account, String password, long supplierId, boolean isCheck) {
        UserEntity user = this.getUserByCellphone(TenantInfo.nobody(TenantInfo.TenantType.PERSONAL), account);

        if ( isCheck &&  (user == null || !HashPassword.checkPassword(password, user.getPassword()))) {
            throw new BusinessException("密码错误或用户不存在");
        }

        if (user == null) {
            // 个人端用户在不存在时自动创建
            user = this.addUser(TenantInfo.ofPersonal(supplierId), account);
        }
        return user;
    }
}
