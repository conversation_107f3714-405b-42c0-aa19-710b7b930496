package com.olading.operate.labor.app.rpc;

import com.lanmaoly.cloud.archive.ArchiveService;
import com.lanmaoly.cloud.archive.DefaultArchiveService;
import com.lanmaoly.cloud.archive.rpc3.Archive3RpcService;
import com.lanmaoly.cloud.psalary.mybank.RemitService;
import com.lanmaoly.cloud.rpc.DefaultRpcClient;
import com.lanmaoly.cloud.rpc.RpcClient;
import com.lanmaoly.cloud.rpc.RpcProperties;
import com.lanmaoly.cloud.rpc.spring.RestTemplateRpcClientProvider;
import com.olading.basic.rpc.api.BasicRpcService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RpcConfig {

    @Bean
    public RpcClient rpcClient(RestOperations restOperations,
                               Environment environment) {
        return new DefaultRpcClient(
                new RestTemplateRpcClientProvider(restOperations),
                environment.getProperty("spring.application.name"));
    }

    @Bean
    public Archive3RpcService archiveRpcService(RpcClient client) {
        return client.create(Archive3RpcService.class, new RpcProperties("lanmaoly-cloud-archive-service", "archive3"));
    }

    @Bean
    public ArchiveService archiveService(Archive3RpcService archiveRpcService, RestTemplate restTemplate) {

        return new DefaultArchiveService(restTemplate.getRequestFactory(), archiveRpcService);
    }

    @Bean
    public BasicRpcService basicRpcService(RpcClient client) {
        return client.create(BasicRpcService.class, new RpcProperties("olading-basic-info-service", "basic"));
    }

    @Bean
    public RemitService remitService(RpcClient customerRpcClient) {
        return customerRpcClient.create(RemitService.class, new RpcProperties("olading-psalary-di", "remit-service"));
    }
}
