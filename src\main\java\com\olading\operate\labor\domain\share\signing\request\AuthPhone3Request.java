package com.olading.operate.labor.domain.share.signing.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "运营商三要素认证请求参数")
public class AuthPhone3Request extends BaseRequest<AuthPhone3Request> {


    @Schema(description = "身份证号码", required = true)
    private String idCardNo;
    @Schema(description = "流水号", required = true)
    private String flowNo;
    @Schema(description = "姓名", required = true)
    private String realName;
    @Schema(description = "手机号", required = true)
    private String phone;
}
