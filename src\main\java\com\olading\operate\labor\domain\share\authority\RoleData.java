package com.olading.operate.labor.domain.share.authority;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RoleData {

    private Long id;

    private String code;

    private String name;

    private String remark;

    private boolean disabled;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新时间")
    private LocalDateTime modifyTime;

    public RoleData() {
    }

    public RoleData(RoleEntity entity) {
        this.id = entity.getId();
        this.name = entity.getName();
        this.code = entity.getCode();
        this.remark = entity.getRemark();
        this.createTime = entity.getCreateTime();
        this.modifyTime = entity.getModifyTime();
        this.disabled = entity.getDisabled();
    }
}
