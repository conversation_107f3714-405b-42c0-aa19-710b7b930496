package com.olading.operate.labor.domain.share.signing.common;

import com.olading.operate.labor.domain.share.signing.enums.FieldType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description= "模板域")
@Data
public class Field {

    @Schema(description = "模板域的名称", required = true)
    private String name;
    @Schema(description = "模板域的类型，目前只支持TEXT", required = true)
    private FieldType type;
    @Schema(description = "模板域的默认值")
    private String value;
}
