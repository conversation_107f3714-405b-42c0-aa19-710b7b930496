package com.olading.operate.labor.util;

import com.olading.boot.util.ConcurrentUtils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SynchronousSink;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class GroupingBatchProcessor<T> implements AutoCloseable {

    private final Signal eof = new Signal(SignalType.EOF, null);

    private final BlockingQueue<Signal> queue = new ArrayBlockingQueue<>(3000, true);
    private final Scheduler single = Schedulers.newSingle("GroupingBatchProcessor-subscribe");

    private final CountDownLatch latch = new CountDownLatch(1);

    private final AtomicLong emitCount = new AtomicLong(0);
    private final AtomicLong emitSuccessCount = new AtomicLong(0);
    private final AtomicLong emitErrorCount = new AtomicLong(0);
    private final AtomicLong emitOverflowCount = new AtomicLong(0);

    private final AtomicBoolean closed = new AtomicBoolean(false);

    private final Function<? super T, Object> keyMapper;
    private final Consumer<List<T>> batchHandler;
    private final int maxSize;
    private final int parallelism;

    public GroupingBatchProcessor(
            Consumer<List<T>> batchHandler,
            Function<? super T, Object> keyMapper,
            int parallelism,
            Duration maxTime,
            int maxSize) {
        this.keyMapper = keyMapper;
        this.batchHandler = batchHandler;
        this.maxSize = maxSize;
        this.parallelism = parallelism;

        subscribe();

        new Thread(() -> {
            try {
                while (latch.getCount() > 0) {
                    queue.put(new Signal(SignalType.SIGNAL, null));
                    Thread.sleep(maxTime.toMillis());
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();
    }


    public void printState() {
        log.info("emitCount: {}, emitSuccessCount: {}, emitErrorCount: {}, overflow={}", emitCount.get(), emitSuccessCount.get(), emitErrorCount.get(), emitOverflowCount.get());
    }

    public void accept(T order) {
        try {
            queue.put(new Signal(SignalType.DATA, order));
        } catch (InterruptedException ignored) {

        }
    }

    @Override
    public void close() throws Exception {
        if (!closed.getAndSet(true)) {
            queue.put(eof);
        }
        latch.await();
        single.dispose();
    }

    private Flux<List<Signal>> buffer(Flux<Signal> source,
                                     int maxSize) {
        return Flux.defer(() -> {
            AtomicLong currentSize = new AtomicLong();
            return source.bufferUntil(p -> {
                if (currentSize.incrementAndGet() >= maxSize) {
                    currentSize.set(0);
                    return true;
                } else return p.type == SignalType.SIGNAL;
            });
        }).filter(o -> !o.stream().allMatch(z -> z.type == SignalType.SIGNAL));
    }

    private void subscribe() {

        buffer(Flux.generate(this::emit), maxSize)
                .doOnNext(this::nextHandler)
                .subscribeOn(single)
                .doOnError(ex -> {
                    log.error(ex.getMessage(), ex);
                    // 如果不是正常关闭，重新订阅
                    if (!closed.get()) {
                        subscribe();
                    }
                })
                .doOnTerminate(() -> {
                    if (closed.get()) {
                        latch.countDown();
                    }
                })
                .subscribe();

    }

    private void nextHandler(List<Signal> batch) {
        batchHandler(batch, false);
    }

    private void batchHandler(List<Signal> batch, boolean overflow) {

        var groups = batch.stream()
                .filter(o -> o.type == SignalType.DATA)
                .map(o -> o.data).collect(Collectors.groupingBy(keyMapper));

        ConcurrentUtils.concurrentExecute(groups.values(), parallelism, g -> {
            try {
                if (overflow) {
                    emitOverflowCount.addAndGet(g.size());
                }
                this.batchHandler.accept(g);

                emitSuccessCount.addAndGet(g.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private void emit(SynchronousSink<Signal> sink) {
        try {
            var detail = queue.take();
            if (detail.type == SignalType.EOF) {
                sink.complete();
            } else {
                sink.next(detail);
                if (detail.type == SignalType.DATA) {
                    emitCount.incrementAndGet();
                }
            }
        } catch (InterruptedException e) {
            sink.complete();
        }
    }

    class Signal {

        private final SignalType type;
        private final T data;

        public Signal(SignalType type, T data) {
            this.type = type;
            this.data = data;
        }
    }

    enum SignalType {
        EOF, DATA, SIGNAL
    }

}
