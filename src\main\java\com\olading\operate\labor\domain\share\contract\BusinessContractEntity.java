package com.olading.operate.labor.domain.share.contract;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Comment("客户服务合同")
@Entity
@Table(name = "t_business_contract", schema = "olading_labor")
public class BusinessContractEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Comment("灵工平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @NotNull
    @Comment("客户id")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 150)
    @NotNull
    @Comment("合同名称")
    @Column(name = "name", nullable = false, length = 150)
    private String name;

    @Size(max = 64)
    @NotNull
    @Comment("编号")
    @Column(name = "sn", nullable = false, length = 64)
    private String sn;

    @NotNull
    @Comment("合同期限是否固定")
    @Column(name = "time_fixed", nullable = false)
    private Boolean timeFixed = false;

    @Comment("开始日期")
    @Column(name = "start_date")
    private LocalDate startDate;

    @Comment("结束日期")
    @Column(name = "end_date")
    private LocalDate endDate;

    @Comment("是否已提前中止:0,1")
    @Column(name = "stopped")
    private Boolean stopped;

    @Comment("提前中止时间")
    @Column(name = "stop_time")
    private Instant stopTime;

    @Size(max = 500)
    @Comment("提前中止原因")
    @Column(name = "stop_reason", length = 500)
    private String stopReason;

    @Size(max = 16)
    @Comment("业务类型")
    @Column(name = "business_type", length = 16)
    private String businessType;

    @Size(max = 500)
    @Comment("备注")
    @Column(name = "remark", length = 500)
    private String remark;

    @Size(max = 5000)
    @Comment("附件id,逗号分隔")
    @Column(name = "file_ids", length = 5000)
    private String fileIds;

    @Comment("原合同id")
    @Column(name = "pre_contract_id")
    private Long preContractId;

    @Comment("续签合同id")
    @Column(name = "rear_contract_id")
    private Long rearContractId;

    @Comment("创建者id")
    @Column(name = "creator_id")
    private Long creatorId;

    @Comment("更新者id")
    @Column(name = "updater_id")
    private Long updaterId;

    @Size(max = 20)
    @NotNull
    @Comment("合同状态")
    @Column(name = "status", nullable = false, length = 20)
    private String status;

    public BusinessContractEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }

    public BusinessContractEntity() {
    }
}