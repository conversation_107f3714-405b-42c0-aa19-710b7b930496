package com.olading.operate.labor.domain.salary.engine;

import com.olading.operate.labor.domain.salary.PreviousIncomeDeductionEntity;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;

import static com.olading.operate.labor.domain.salary.QPreviousIncomeDeductionEntity.previousIncomeDeductionEntity;

/**
 * 上期收入减除导入管理器
 */
@Component
@RequiredArgsConstructor
@Transactional
public class PreviousIncomeDeductionManager {
    
    private final EntityManager em;
    
    /**
     * 根据身份证号、作业主体ID和税期查询上期收入减除记录
     */
    public PreviousIncomeDeductionEntity queryByIdCardAndCorporationAndPeriod(String idCard, 
            Long supplierCorporationId, String taxPeriod) {
        
        return queryPreviousIncomeDeduction(t -> 
            t.idNumber.eq(idCard)
                .and(t.supplierCorporationId.eq(supplierCorporationId))
                .and(t.taxPeriod.eq(taxPeriod))
                .and(t.deleted.eq(false))
        ).fetchOne();
    }

    
    /**
     * 通用查询方法
     */
    private JPAQuery<PreviousIncomeDeductionEntity> queryPreviousIncomeDeduction(
            Function<com.olading.operate.labor.domain.salary.QPreviousIncomeDeductionEntity, Predicate> condition) {
        
        return new JPAQueryFactory(em)
                .select(previousIncomeDeductionEntity)
                .from(previousIncomeDeductionEntity)
                .where(condition.apply(previousIncomeDeductionEntity));
    }
}