package com.olading.operate.labor.app.web.biz.bill;

import com.olading.operate.labor.util.excel.ExcelColumn;
import com.olading.operate.labor.util.excel.ExcelRow;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 导入验证错误信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportValidationErrorData{
    @ExcelColumn(name = "费用名称", required = true, orderNum = 1)
    private String feeName;

    @ExcelColumn(name = "费用金额", required = true, orderNum = 2)
    private BigDecimal feeAmount;

    @ExcelColumn(name = "产生时间", required = true, orderNum = 3)
    private LocalDate occurDate;

    @ExcelColumn(name = "产生人", required = true, orderNum = 4)
    private String laborName;

    @ExcelColumn(name = "身份证号", required = false, orderNum = 5)
    private String idCard;

    @ExcelColumn(name = "费用用途", required = false, orderNum = 6)
    private String feePurpose;

    @ExcelColumn(name = "错误信息", required = false, orderNum = 6)
    private String errorMessage;

}