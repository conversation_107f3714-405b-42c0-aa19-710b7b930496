package com.olading.operate.labor.util.validation.validator;

import com.lanmaoly.util.lang.ValidateUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Mobile;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class MobileValidator extends BaseValidator<Mobile> {
    public MobileValidator() {}

    public MobileValidator(String name) {
        setName(name);
    }

    @Override
    public void initialize(Mobile constraintAnnotation) {
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        super.initialize(constraintAnnotation);
    }

    @Override
    protected boolean constraintCheck(Object o) {
        try {
            return ValidateUtils.checkMobile(String.valueOf(o));
        } catch (ValidationException e) {
            e.setName(getName());
            throw e;
        }
    }
}
