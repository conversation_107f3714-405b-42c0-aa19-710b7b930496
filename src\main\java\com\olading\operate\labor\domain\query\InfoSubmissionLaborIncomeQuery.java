package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionLaborIncomeEntity;
import com.olading.operate.labor.domain.share.submission.QInfoSubmissionLaborIncomeEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborIncomeVo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.Set;

public class InfoSubmissionLaborIncomeQuery implements EntityQuery<QueryFilter<InfoSubmissionLaborIncomeQuery.Filters>, InfoSubmissionLaborIncomeVo> {

    private final QInfoSubmissionLaborIncomeEntity t1 = QInfoSubmissionLaborIncomeEntity.infoSubmissionLaborIncomeEntity;
    private final QSupplierCorporationEntity t2 = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.eq(filters.getFilters().getId()));
        }
        if (filters.getFilters().getSupplierCorporationId() != null) {
            criteria.and(t1.supplierCorporationId.eq(filters.getFilters().getSupplierCorporationId()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getStartDate())) {
            criteria.and(t1.startDate.eq(filters.getFilters().getStartDate()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getEndDate())) {
            criteria.and(t1.endDate.eq(filters.getFilters().getEndDate()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getStatus())) {
            criteria.and(t1.status.eq(filters.getFilters().getStatus()));
        }

        if (StringUtils.isNotBlank(filters.getFilters().getSupplierCorporationName())) {
            criteria.and(t2.name.like(JpaUtils.fullLike(filters.getFilters().getSupplierCorporationName())));
        }

        if (filters.getFilters().getCreateTimeStart() != null) {
            criteria.and(t1.createTime.goe(filters.getFilters().getCreateTimeStart()));
        }

        if (filters.getFilters().getCreateTimeEnd() != null) {
            criteria.and(t1.createTime.lt(filters.getFilters().getCreateTimeEnd()));
        }

        if (filters.getFilters().getCorporationIds() != null){
            criteria.and(t1.supplierCorporationId.in(filters.getFilters().getCorporationIds()));
        }

        query.select(t1, t2)
                .from(t1)
                .leftJoin(t2).on(t1.supplierCorporationId.eq(t2.id))
                .where(criteria);
    }

    @Override
    public InfoSubmissionLaborIncomeVo transform(Object v) {
        Tuple tuple = (Tuple) v;
        InfoSubmissionLaborIncomeEntity entity = tuple.get(t1);
        SupplierCorporationEntity corporation = tuple.get(t2);
        
        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        BeanUtils.copyProperties(entity, vo);
        
        if (corporation != null) {
            vo.setSupplierCorporationName(corporation.getName());
        }
        
        // 设置状态描述
        if (StringUtils.isNotBlank(entity.getStatus())) {
            vo.setStatusDesc(PersonalIncomeTaxDeclareStatusEnum.getNameByKey(entity.getStatus()));
        }
        
        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        if ("createTime".equals(column)) {
            return t1.createTime;
        }
        return null;
    }

    @Data
    public static class Filters {
        private Long id;
        private Long supplierCorporationId;
        private String startDate;
        private String endDate;
        private String status;
        private String supplierCorporationName;
        private LocalDateTime createTimeStart;
        private LocalDateTime createTimeEnd;
        private Set<Long> corporationIds;
    }
}
