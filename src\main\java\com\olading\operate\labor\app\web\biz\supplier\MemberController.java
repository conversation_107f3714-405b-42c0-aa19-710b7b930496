package com.olading.operate.labor.app.web.biz.supplier;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.Json;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.menu.Menu;
import com.olading.operate.labor.app.menu.MenuManager;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.RoleMemberQuery;
import com.olading.operate.labor.domain.query.RoleQuery;
import com.olading.operate.labor.domain.query.SupplierMemberQuery;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.util.Utils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/8 19:13
 */
@Tag(name = "用户权限管理")
@RestController
@RequestMapping("/api/supplier")
@RequiredArgsConstructor
@Slf4j
public class MemberController extends BusinessController {

    private final SupplierService supplierService;
    private final AuthorityManager authorityManager;
    private final MenuManager menuManager;
    private final QueryService queryService;

    @Operation(summary = "获取管理后台登录用户的信息")
    @PostMapping(value = "profile")
    @AuthorityGuard(any = {Authority.SYS_SUPPLIER})
    public WebApiResponse<SupplierProfileVo> profile() {
        SupplierProfileVo profile = new SupplierProfileVo();
        if (currentTenant().getType() == TenantInfo.TenantType.SUPPLIER && !TenantInfo.isNobody(currentTenant())) {
            profile.setCurrentSupplierId(Long.parseLong(currentTenant().getId()));
        }
        final UserEntity userEntity = currentUser();
        if (profile.getCurrentSupplierId() != null) {
            var member = supplierService.requireMemberByUserId(currentSupplierId(), userEntity.getId());
            profile.setUserPersonName(member.getName());
            profile.setAdmin(isAdmin());
            profile.setAuthorities(new ArrayList<>(currentAuthorities()));
            profile.setCreateTime(member.getCreateTime());
        }
        profile.setUserId(userEntity.getId());
        profile.setIsPwd(userEntity.getPassword() != null);
        profile.setCellphone(userEntity.getCellphone());

        return WebApiResponse.success(profile);
    }



    @Operation(summary = "权限树")
    @PostMapping(value = "getAuthorityTree")
    public WebApiResponse<SupplierController.AuthorityTree> getAuthorityTree() {
        var tree = Json.fromJson(Utils.loadResource("/authority/supplier.json"), SupplierController.AuthorityTree.class);
        return WebApiResponse.success(tree);
    }


    @Operation(summary = "菜单")
    @PostMapping(value = "getMenu")
    public WebApiResponse<Menu> menu() {
        Menu m = menuManager.load(currentAuthorities());
        return WebApiResponse.success(m);
    }


    @Operation(summary = "查询角色")
    @PostMapping(value = "listRole")
    public WebApiQueryResponse<RoleVo> listRole(@Valid @RequestBody QueryFilter<ListRoleFilters> request) {

        QueryFilter<RoleQuery.Filters> filter = request.convert(ListRoleFilters::convert);
        filter.getFilters().setTenant(currentTenant(TenantInfo.TenantType.SUPPLIER));
        filter.sort("id", Direction.DESCENDING);
        DataSet<RoleData> ds = queryService.queryRole(filter);

        var f2 = new RoleMemberQuery.Filters();
        f2.setSupplierId(currentSupplierId());
        var grouped = queryService.queryRoleMember(new QueryFilter<>(f2)).getData()
                .stream().collect(Collectors.groupingBy(o -> o.getRoleMember().getRoleId()));

        return WebApiQueryResponse.success(ds.getData().stream()
                .map(o -> {
                    RoleVo vo = new RoleVo();
                    vo.setId(o.getId());
                    vo.setCode(o.getCode());
                    vo.setName(o.getName());
                    vo.setRemark(o.getRemark());
                    vo.setCreateTime(o.getCreateTime());
                    vo.setModifyTime(o.getModifyTime());
                    vo.setDisabled(o.isDisabled());
                    var members = grouped.get(vo.getId());
                    if (members != null) {
                        vo.setMembers(members.stream().map(m -> {
                            MemberVo u = new MemberVo();
                            u.setMemberId(m.getSupplierMember().getId());
                            u.setName(m.getSupplierMember().getName());
                            u.setCellphone(m.getSupplierMember().getCellphone());
                            u.setCreateTime(m.getSupplierMember().getCreateTime());
                            u.setModifyTime(m.getSupplierMember().getModifyTime());
                            return u;
                        }).collect(Collectors.toList()));
                    }

                    return vo;
                })
                .collect(Collectors.toList()), ds.getTotal());
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "添加角色")
    @PostMapping(value = "addRole")
    public WebApiResponse<Void> addRole(@Valid @RequestBody AddRoleRequest request) {
        supplierService.addRole(currentSupplierId(), request.getName(),request.getCode(), request.getRemark(), request.getAuthorities());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "禁用/开启角色")
    @PostMapping(value = "disableRole")
    public WebApiResponse<Void> disableRole(@Valid @RequestBody DisableRequest request) {
        supplierService.disableRole(currentSupplierId(),request.getId(), request.isDisabled());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "编辑角色")
    @PostMapping(value = "editRole")
    public WebApiResponse<Void> editRole(@Valid @RequestBody EditRoleRequest request) {
        RoleData data = new RoleData();
        data.setId(request.getRoleId());
        data.setName(request.getName());
        data.setRemark(request.getRemark());
        supplierService.editRole(currentTenant(), currentSupplierId(), data, request.getAuthorities());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "删除角色")
    @PostMapping(value = "deleteRole")
    public WebApiResponse<Void> deleteRole(@Valid @RequestBody DeleteRoleRequest request) {
        supplierService.deleteRole(currentTenant(),currentSupplierId(), request.getRoleId());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "角色详情")
    @PostMapping(value = "roleDetail")
    public WebApiResponse<RoleDetailResponse> roleDetail(@Valid @RequestBody RoleDetailRequest request) {
        RoleEntity role = supplierService.requireRole(currentSupplierId(), request.getRoleId());
        final Map<OwnerType, Map<Long, String>> roleDataScopes = authorityManager.getRoleDataScopes(currentTenant(), request.getRoleId());
        var data = new RoleDetailResponse();
        data.setId(role.getId());
        data.setName(role.getName());
        data.setCode(role.getCode());
        data.setRemark(role.getRemark());
        data.setAuthorities(role.getAuthorities());
        data.setDataScopes(roleDataScopes);
        return WebApiResponse.success(data);
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "设置角色成员")
    @PostMapping(value = "setRoleMembers")
    public WebApiResponse<Void> setRoleMembers(@Valid @RequestBody SetRoleMembersRequest request) {
        supplierService.setRoleMembers(currentSupplierId(), request.getRoleId(), request.getMemberId());
        return WebApiResponse.success();
    }

    @Operation(summary = "获取角色成员")
    @PostMapping(value = "getRoleMembers")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    public WebApiResponse<List<SupplierController.UserVo>> getRoleMembers(@Valid @RequestBody GetRoleMembersRequest request) {

        var filters = new RoleMemberQuery.Filters();
        filters.setSupplierId(currentSupplierId());
        filters.setRoleId(request.getRoleId());
        var ds = queryService.queryRoleMember(new QueryFilter<>(filters));

        return WebApiResponse.success(ds.getData().stream().map(o -> {
            SupplierController.UserVo vo = new SupplierController.UserVo();
            vo.setId(o.getSupplierMember().getId());
            vo.setRealName(o.getSupplierMember().getName());
            return vo;
        }).collect(Collectors.toList()));
    }

    @Operation(summary = "获取成员")
    @PostMapping(value = "getMembers")
    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    public WebApiQueryResponse<MemberVo> getMembers(@Valid @RequestBody QueryFilter<GetMembers> request) {
        QueryFilter<SupplierMemberQuery.Filters> filter = request.convert(GetMembers::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        var ds = queryService.queryMember(filter);
        ds.getData().forEach(o -> {
                    o.setRoles(Lists.newArrayList());
                    final RoleMemberQuery.Filters roleMemberFilters = new RoleMemberQuery.Filters();
                    roleMemberFilters.setSubjectId(Set.of(o.getId()));
                    final DataSet<RoleMemberQuery.Record> dataSet = queryService.queryRoleMember(new QueryFilter<>(roleMemberFilters));
                    dataSet.getData().forEach(roleMember -> {
                        o.getRoles().add(roleMember.getRole());
                    });
                }
        );
        final List<MemberVo> collect = ds.getData().stream().map(o -> {
            MemberVo vo = new MemberVo();
            vo.setMemberId(o.getId());
            vo.setName(o.getName());
            vo.setCellphone(o.getCellphone());
            vo.setCreateTime(o.getCreateTime());
            vo.setModifyTime(o.getModifyTime());
            vo.setDisabled(o.isDisabled());
            if (CollectionUtil.isNotEmpty(o.getRoles())) {
                vo.setRoles(o.getRoles().stream().map(RoleVo::transform).collect(Collectors.toSet()));
            }else{
                vo.setRoles(Set.of());
            }
            return vo;
        }).collect(Collectors.toList());
        return WebApiQueryResponse.success(collect,ds.getTotal());
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "禁用/开启成员")
    @PostMapping(value = "disableMember")
    public WebApiResponse<Void> disableMember(@Valid @RequestBody DisableRequest request) {
        supplierService.disableMember(currentSupplierId(),request.getId(), request.isDisabled());
        return WebApiResponse.success();
    }


    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "删除成员")
    @PostMapping(value = "removeMember")
    public WebApiResponse<Void> removeMember(@Valid @RequestBody DisableRequest request) {
        supplierService.removeMember(currentSupplierId(),request.getId(), request.isDisabled());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "添加成员")
    @PostMapping(value = "addMember")
    public WebApiResponse<Void> addMember(@Valid @RequestBody AddMemberRequest request) {
        supplierService.addMember(currentSupplierId(),request.getName(),request.getCellphone(),request.getRoleIds());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_SETTING_ROLE_PERMISSION)
    @Operation(summary = "编辑成员")
    @PostMapping(value = "editMember")
    public WebApiResponse<Void> editMember(@Valid @RequestBody EditMemberRequest request) {
        supplierService.editSupplierMember(currentSupplierId(),request.getMerberId(),request.getCellphone(),request.getName(),request.getRoleIds());
        return WebApiResponse.success();
    }


    @Data
    public static class AddMemberRequest {

        @Schema(description = "姓名")
        @NotBlank(message = "请输入姓名")
        private String name;

        @Schema(description = "手机号")
        @NotBlank(message = "请输入手机号")
        @Size(max = 11, message = "手机号长度不能超过11位")
        private String cellphone;

        @Schema(description = "角色ID列表")
        private Set<Long> roleIds;
    }

    @Data
    public static class EditMemberRequest {

        @Schema(description = "会员ID")
        @NotNull(message = "请输入会员ID")
        private Long merberId;

        @Schema(description = "姓名")
        @NotBlank(message = "请输入姓名")
        private String name;

        @Schema(description = "手机号")
        @NotBlank(message = "请输入手机号")
        private String cellphone;

        @Schema(description = "角色ID列表")
        private Set<Long> roleIds;
    }




    @Data
    public static class DisableRequest {
        private Long id;
        @Schema(description = "禁用/启用")
        private boolean disabled;
    }

    @Data
    public static class EditRoleRequest {

        private Long roleId;

        @Schema(description = "角色名称")
        @NotBlank(message = "请输入角色名称")
        @Size(max = 20, message = "角色名称长度不能超过20")
        private String name;

        @Schema(description = "角色描述")
        @Size(max = 200, message = "角色描述长度不能超过200")
        private String remark;

        @Schema(description = "权限")
        private List<String> authorities;
    }

    @Data
    public static class DeleteRoleRequest {

        private Long roleId;

    }

    @Data
    public static class GetRoleMembersRequest {

        @NotNull
        @Schema(description = "角色ID")
        private Long roleId;
    }

    @Data
    public static class GetMembers {

        @Schema(description = "角色ID")
        private Long roleId;

        @Schema(description = "成员ID")
        private Long memberId;

        @Schema(description = "姓名手机号")
        private String nameOrCellphone;

        public SupplierMemberQuery.Filters convert() {
            return Beans.copyBean(this, SupplierMemberQuery.Filters.class);
        }

    }

    @Data
    public static class SetRoleMembersRequest {

        @NotNull
        @Schema(description = "角色ID")
        private Long roleId;

        @NotNull
        @Schema(description = "用户ID")
        private List<Long> memberId;
    }

    @Data
    public static class RoleDetailRequest {
        @NotNull
        private Long roleId;
    }

    @Data
    public static class RoleDetailResponse {

        private Long id;

        private String name;

        private String code;

        private String remark;

        @Schema(description = "权限")
        private List<String> authorities;
        @Schema(description = "数据权限")
        private Map<OwnerType, Map<Long,String>> dataScopes;
    }

    @Data
    public static class AddRoleRequest {

        @Schema(description = "角色名称")
        @NotBlank(message = "请输入角色名称")
        @Size(max = 20,message = "角色名称长度不能超过20个字符")
        private String name;

        @Schema(description = "角色编码")
        @Size(max = 10,message = "角色编码长度不能超过10个字符")
        private String code;

        @Schema(description = "角色描述")
        @Size(max = 200,message = "角色描述长度不能超过200个字符")
        private String remark;

        @Schema(description = "权限")
        private List<String> authorities;
    }

    @Data
    public static class SupplierProfileVo {
        @Schema(description = "用户ID")
        private Long userId;
        @Schema(description = "用户姓名")
        private String userPersonName;
        @Schema(description = "当前的服务商ID")
        private Long currentSupplierId;
        @Schema(description = "是否管理员")
        private boolean isAdmin;
        @Schema(description = "手机号")
        private String cellphone;
        @Schema(description = "创建时间")
        private LocalDateTime createTime;
        @Schema(description = "是否设置账户密码")
        private Boolean isPwd;
        @Schema(description = "当前用户的权限")
        private List<String> authorities;
    }

    @Data
    public static class ListRoleFilters {

        private String name;

        public RoleQuery.Filters convert() {
            return Beans.copyBean(this, RoleQuery.Filters.class);
        }
    }

    @Data
    public static class RoleVo {
        private Long id;
        @Schema(description = "角色编码")
        private String code;
        @Schema(description = "角色名称")
        private String name;
        @Schema(description = "描述")
        private String remark;
        @Schema(description = "创建时间")
        private LocalDateTime createTime;
        @Schema(description = "更新时间")
        private LocalDateTime modifyTime;
        @Schema(description = "是否禁用")
        private boolean disabled;
        @Schema(description = "成员")
        private List<MemberVo> members;

        public static RoleVo transform(RoleEntity roleEntity) {
            RoleVo vo = new RoleVo();
            vo.setId(roleEntity.getId());
            vo.setCode(roleEntity.getCode());
            vo.setName(roleEntity.getName());
            vo.setRemark(roleEntity.getRemark());
            vo.setCreateTime(roleEntity.getCreateTime());
            vo.setModifyTime(roleEntity.getModifyTime());
            vo.setDisabled(roleEntity.getDisabled());
            return vo;
        }
    }


    @Data
    public static class MemberVo {
        private Long memberId;
        private String cellphone;
        private String name;
        private boolean disabled;
        private LocalDateTime createTime;
        private LocalDateTime modifyTime;
        private Set<RoleVo> roles;
    }

}
