package com.olading.operate.labor.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.security.SecuritySchemes;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition(info = @Info(title = "API文档", version = "v1"))
@SecuritySchemes({
        @SecurityScheme(
                name = "Authorization",
                type = SecuritySchemeType.HTTP,
                in = SecuritySchemeIn.HEADER,
                paramName = "Bearer Token (e.g. Bearer your_token)",
                description = "Bearer Token (e.g. Bearer your_token)"
        ),
        @SecurityScheme(
                name = "supplier",
                type = SecuritySchemeType.HTTP,
                in = SecuritySchemeIn.HEADER,
                paramName = "服务运营方编号",
                description = "服务运营方编号"
        ),
        @SecurityScheme(
                name = "supplierDomain",
                type = SecuritySchemeType.HTTP,
                in = SecuritySchemeIn.HEADER,
                paramName = "品牌域名",
                description = "品牌域名"
        )
})
public class SwaggerSecurityConfig {
}