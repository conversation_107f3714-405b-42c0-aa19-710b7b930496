package com.olading.operate.labor.domain.share.customer;

import com.olading.operate.labor.app.web.biz.enums.CustomerCooperationStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerVo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import com.olading.operate.labor.domain.share.info.*;
import com.olading.operate.labor.domain.share.info.QEnterpriseInfoEntity;
import com.olading.operate.labor.util.Utils;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Transactional
@Component
@RequiredArgsConstructor
public class CustomerManager {

    private final EntityManager em;
    private final InfoManager infoManager;

    /**
     * 创建一个客户
     */
    public CustomerEntity addCustomer(TenantInfo tenantInfo, CustomerVo vo) {
        CustomerEntity customer = new CustomerEntity(tenantInfo);
        customer.setUserId(vo.getUserId());
        customer.setName(vo.getName());
        customer.setSn(Utils.uuid());
        customer.setAddress(vo.getRegisterAddress());
        customer.setStatus(CustomerCooperationStatusEnum.NOT.name());
        customer.setSupplierId(vo.getSupplierId());
        customer.setShortName(vo.getShortName());
        customer = em.merge(customer);

        EnterpriseInfoEntity info = infoManager.save(OwnerType.CUSTOMER, customer.getId(), vo);
        customer.setEnterpriseInfoId(info.getId());
        return em.merge(customer);
    }



    /**
     * 修改客户信息
     */
    public CustomerEntity updateCustomerInfo(long customerId, CustomerVo vo) {
        CustomerEntity entity = getCustomer(customerId);
        if (vo.getName() != null) {
            entity.setName(vo.getName());
        }
        if (vo.getRegisterAddress() != null) {
            entity.setAddress(vo.getRegisterAddress());
        }
        if (vo.getShortName() != null) {
            entity.setShortName(vo.getShortName());
        }
        infoManager.save(OwnerType.CUSTOMER, entity.getId(), vo);
        return em.merge(entity);
    }

    /**
     * 更新客户状态
     */
    public void updateCustomerStatus(long customerId, String status) {
        CustomerEntity entity = getCustomer(customerId);
        entity.setStatus(status);
        em.merge(entity);
    }

    public void saveCustomer(CustomerEntity customer) {
        em.merge(customer);
    }


    public CustomerEntity getCustomer(long id) {
        CustomerEntity entity = em.find(CustomerEntity.class, id);
        if (entity == null) {
            throw new IllegalStateException("客户不存在");
        }
        return entity;
    }

    public CustomerWithInfo queryCustomer(long customerId) {
        QCustomerEntity t = QCustomerEntity.customerEntity;
        QEnterpriseInfoEntity info = QEnterpriseInfoEntity.enterpriseInfoEntity;
        Tuple tuple = new JPAQueryFactory(em)
                .select(t, info)
                .from(t)
                .leftJoin(info).on(t.id.eq(info.ownedBy.ownerId))
                .where(t.id.eq(customerId).and(info.ownedBy.ownerType.eq(OwnerType.CUSTOMER))).fetchOne();
        CustomerEntity customerEntity = tuple.get(t);
        EnterpriseInfoEntity infoEntity = tuple.get(info);
        return new CustomerWithInfo(customerEntity, infoEntity);
    }

    public List<CustomerWithInfo> queryCustomerBySupplier(long supplierId) {
        QCustomerEntity t = QCustomerEntity.customerEntity;
        QEnterpriseInfoEntity info = QEnterpriseInfoEntity.enterpriseInfoEntity;
        JPAQuery<Tuple> query = new JPAQueryFactory(em)
                .select(t, info)
                .from(t)
                .leftJoin(info).on(t.id.eq(info.ownedBy.ownerId))
                .where(t.supplierId.eq(supplierId).and(info.ownedBy.ownerType.eq(OwnerType.CUSTOMER)));
        return query.stream()
                .map(tuple -> new CustomerWithInfo(tuple.get(t), tuple.get(info)))
                .collect(Collectors.toList());
    }

    public JPAQuery<CustomerEntity> queryCustomer(Function<QCustomerEntity, Predicate> condition) {
        QCustomerEntity t = QCustomerEntity.customerEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    /**
     * 校验客户名称在同一供应商下的唯一性
     * @param supplierId 供应商ID
     * @param name 客户名称
     * @return 如果存在重复则返回true
     */
    public boolean existsBySupplierIdAndName(Long supplierId, String name) {
        QCustomerEntity t = QCustomerEntity.customerEntity;
        return new JPAQueryFactory(em)
                .selectFrom(t)
                .where(t.supplierId.eq(supplierId).and(t.name.eq(name)))
                .fetchFirst() != null;
    }

    /**
     * 校验社会信用代码在同一供应商下的唯一性
     * @param supplierId 供应商ID
     * @param socialCreditCode 社会信用代码
     * @return 如果存在重复则返回true
     */
    public boolean existsBySupplierIdAndSocialCreditCode(Long supplierId, String socialCreditCode) {
        QCustomerEntity customer = QCustomerEntity.customerEntity;
        QEnterpriseInfoEntity enterprise = QEnterpriseInfoEntity.enterpriseInfoEntity;

        return new JPAQueryFactory(em)
                .select(customer.id)
                .from(customer)
                .innerJoin(enterprise).on(customer.id.eq(enterprise.ownedBy.ownerId))
                .where(customer.supplierId.eq(supplierId)
                        .and(enterprise.ownedBy.ownerType.eq(OwnerType.CUSTOMER))
                        .and(enterprise.socialCreditCode.eq(socialCreditCode)))
                .fetchFirst() != null;
    }

}
