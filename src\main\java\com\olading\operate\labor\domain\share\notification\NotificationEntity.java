package com.olading.operate.labor.domain.share.notification;

import com.olading.boot.util.Json;
import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;

import java.util.Collections;
import java.util.Map;

/**
 * API推送通知
 */
@Table(name = "t_notification")
@Entity
public class NotificationEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Comment("通知接收人")
    @Column(name = "receiver", length = 50, nullable = false)
    private String receiver;

    @NotNull
    @Comment("通知url的hash")
    @Column(name = "url_hash", length = 50, nullable = false)
    private String urlHash;

    @Enumerated(EnumType.STRING)
    @Comment("通知内容格式")
    @Column(name = "content_format", length = 20, nullable = false)
    private ContentFormat contentFormat;

    @Lob
    @Comment("通知内容")
    @Column(name = "content", length = 17000000)
    private String content;

    @Comment("请求次数")
    @Column(name = "request_count", nullable = false)
    private Integer requestCount;

    @Comment("是否成功")
    @Column(name = "ok", nullable = false)
    private boolean ok;

    public NotificationEntity(String receiver, String urlHash, Map<String, String> form) {
        this.receiver = receiver;
        this.urlHash = urlHash;
        this.contentFormat = ContentFormat.FORM;
        this.content = Json.toJson(form);
        this.requestCount = 0;
        this.ok = false;
    }

    protected NotificationEntity() {
    }

    public static void main(String[] args) {

        byte[] b = DigestUtils.getSha256Digest().digest("hello".getBytes());
        System.out.println(b.length);
    }

    public Long getId() {
        return id;
    }

    public String getReceiver() {
        return receiver;
    }

    public String getUrlHash() {
        return urlHash;
    }

    public Map<String, String> getForm() {
        if (StringUtils.isBlank(content)) {
            return Collections.emptyMap();
        }
        return Json.toMap(content);
    }

    public int getRequestCount() {
        return requestCount;
    }

    public void setRequestCount(int retryCount) {
        this.requestCount = retryCount;
    }

    public boolean isOk() {
        return ok;
    }

    public void setOk(boolean ok) {
        this.ok = ok;
    }
}
