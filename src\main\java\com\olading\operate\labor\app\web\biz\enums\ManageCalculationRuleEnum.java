package com.olading.operate.labor.app.web.biz.enums;

/**
 * 服务合同计算规则
 */
public enum ManageCalculationRuleEnum {

    EMPLOYEE_COUNT("按雇员人数计算"),
    PAYABLE_AMOUNT_RATE("按应发金额比例计算");

    private final String name;

    ManageCalculationRuleEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }


    public static String getNameByKey(String key) {
        for (ManageCalculationRuleEnum e : values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getName();
            }
        }
        return "";
    }
}
