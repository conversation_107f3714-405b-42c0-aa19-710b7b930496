package com.olading.operate.labor.domain.share.signing.request;


import com.olading.operate.labor.domain.share.signing.enums.EnableHandSign;
import com.olading.operate.labor.domain.share.signing.enums.EnableSmsVerify;
import com.olading.operate.labor.domain.share.signing.enums.IdType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "准备签名数据请求参数")
public class PrepareSignRequest extends BaseRequest<PrepareSignRequest> {

    @Schema(description = "签约流水号", required = true)
    private String flowNo;
    @Schema(description = "文件ID", required = true)
    private String fileId;
    @Schema(description = "签名的图片，base64编码，为空则不提供默认签名")
    private String signImage;
    @Schema(description = "是否允许手写签名【YES】【NO】")
    private EnableHandSign enableHandSign;
    @Schema(description = "是否允许手写签名【YES】【NO】")
    private EnableSmsVerify enableSmsVerify;
    @Schema(description = "对哪一步进行签名", required = true)
    private String stepName;
    @Schema(description = "签名人名称", required = true)
    private String signer;
    @Schema(description = "签署人唯一标识符", required = true)
    private String idNo;
    @Schema(description = "证件类型", required = true)
    private IdType idType;
    @Schema(description = "手机号，如果需要发送短信验证码，则必传" )
    private String phone;
    @Schema(description = "法定代表人姓名，如果是企业签署人必填" )
    private String legalRepresentative;
    @Schema(description = "法定代表人身份证号，如果是企业签署人必填。" )
    private String legalRepresentativeIdNo;
    @Schema(description = "企业联系人手机号，如果是企业签署人必填" )
    private String contactsPhone;

}
