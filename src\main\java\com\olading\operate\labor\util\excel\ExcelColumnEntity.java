package com.olading.operate.labor.util.excel;

import com.olading.operate.labor.util.textfilter.FilterHelper;
import com.olading.operate.labor.util.textfilter.TextFilter;
import com.olading.operate.labor.util.validation.validator.Validator;
import com.olading.operate.labor.util.validation.validator.ValidatorHelper;
import com.lanmaoly.util.lang.exception.ValidationException;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 参考ExcelColumn注解
 * <AUTHOR>
 * @date 2022/3/8 13:23
 */
public class ExcelColumnEntity {

    /**
     * 导出excel时字段所在的列，从0开始
     */
    private Integer colIndex;

    private String group;

    private String name;

    /**
     * 默认导出的值
     */
    private String nullWriteValue = "";

    /**
     * 对应的Excel里面的名字
     */
    private String excelTitle;

    private boolean saveToOther = false;

    private int width = -1;

    private boolean required = false;

    private boolean unique = false;

    private String dateFormat;

    private Field field;

    private String[] options;

    private String validationFormula;

    private String comment;

    private List<Class<? extends TextFilter>> filters;

    private List<Class<? extends TextFilter>> formatters;

    private List<? extends Validator> validators;

    public Integer getColIndex() {
        return colIndex;
    }

    public void setColIndex(Integer colIndex) {
        this.colIndex = colIndex;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNullWriteValue() {
        return nullWriteValue;
    }

    public void setNullWriteValue(String nullWriteValue) {
        this.nullWriteValue = nullWriteValue;
    }

    public String getExcelTitle() {
        return excelTitle;
    }

    public void setExcelTitle(String excelTitle) {
        this.excelTitle = excelTitle;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public boolean isSaveToOther() {
        return saveToOther;
    }

    public void setSaveToOther(boolean saveToOther) {
        this.saveToOther = saveToOther;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public boolean isUnique() {
        return unique;
    }

    public void setUnique(boolean unique) {
        this.unique = unique;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public Field getField() {
        return field;
    }

    public void setField(Field field) {
        this.field = field;
    }

    public String[] getOptions() {
        return options;
    }

    public void setOptions(String[] options) {
        this.options = options;
    }

    public String getValidationFormula() {
        return validationFormula;
    }

    public void setValidationFormula(String validationFormula) {
        this.validationFormula = validationFormula;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public List<Class<? extends TextFilter>> getFilters() {
        return filters;
    }

    public void setFilters(List<Class<? extends TextFilter>> filters) {
        this.filters = filters;
    }

    public List<Class<? extends TextFilter>> getFormatters() {
        return formatters;
    }

    public void setFormatters(List<Class<? extends TextFilter>> formatters) {
        this.formatters = formatters;
    }

    public List<? extends Validator> getValidators() {
        return validators;
    }

    public void setValidators(List<? extends Validator> validators) {
        this.validators = validators;
    }

    /**
     * 约定日期格式定义为 LocalDateTime 或 LocalDate
     * @return
     */
    public boolean isDateField() {
        if (null == field) {
            return false;
        }

        Class<?> fieldType = field.getType();
        return LocalDateTime.class.equals(fieldType) || LocalDate.class.equals(fieldType);
    }

    /**
     * 是否自定义列（非ExcelRow定义的列）
     */
    public boolean isCustomField()
    {
        return null == field;
    }

    public String doFilter(String text) {
        if (null != filters) {
            for (Class<? extends TextFilter> filter: filters) {
                text = FilterHelper.invokeTextFilter(filter, text);
            }
        }

        return text;
    }

    public String doFormatter(String text) {
        if (null != formatters) {
            for (Class<? extends TextFilter> filter: formatters) {
                text = FilterHelper.invokeTextFilter(filter, text);
            }
        }

        return text;
    }

    public boolean doValidate(String text) throws ValidationException {
        if (null != validators) {
            for (Validator validator: validators) {
                validator.setName(excelTitle);

                try {
                    ValidatorHelper.invokeValidator(validator, text);
                } catch (ValidationException e) {
                    // 检验失败即停止下一步的校验（当前检验都不通过，说明格式有误，下一步校验无意义）
                    throw e;
                }
            }
        }

        return true;
    }
}
