package com.olading.operate.labor.domain.share.user;

import com.olading.operate.labor.domain.share.info.PersonInfoData;
import lombok.Data;


@Data
public class UserData {

    private Long id;

    private String name;

    private String cellphone;

    private PersonInfoData info = new PersonInfoData();

    public UserData(UserEntity user) {
        this.id = user.getId();
        this.name = user.getName();
        this.cellphone = user.getCellphone();
    }

    public UserData() {
    }
}
