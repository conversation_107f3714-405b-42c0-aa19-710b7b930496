package com.olading.operate.labor.domain.invoice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "开票预设信息查询请求")
public class InvoicePresetRequest {
    
    @NotNull(message = "合同ID不能为空")
    @Schema(description = "合同ID")
    private Long contractId;
    
    @Schema(description = "账单月份，不传则查询所有月份,yyyy-mm-01")
    @NotNull(message = "账单月份不能为空")
    private LocalDate billMonth;
}