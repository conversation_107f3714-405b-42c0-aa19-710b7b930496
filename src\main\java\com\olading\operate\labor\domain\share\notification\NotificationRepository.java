package com.olading.operate.labor.domain.share.notification;

import com.olading.boot.util.Misc;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Transactional
@Slf4j
@Component
public class NotificationRepository {

    private static final int MAX_RETRY = 2;
    private static final int LIMIT = 1000;

    private final EntityManager em;

    public Notification add(Notification notification) {

        NotificationUrlHashEntity hash = getHash(notification.getUrl());
        if (hash == null) {
            hash = new NotificationUrlHashEntity(
                    notification.getUrl(),
                    urlHash(notification.getUrl()));
            em.merge(hash);
        }

        NotificationEntity entity = new NotificationEntity(
                notification.getReceiver(),
                urlHash(notification.getUrl()),
                notification.getForm());
        entity = em.merge(entity);

        return new Notification(entity.getId().toString(), entity.getReceiver(), notification.getUrl(), notification.getForm());
    }

    public List<Notification> loadRetry() {

        LocalDateTime start = LocalDateTime.now().plusHours(-1);
        LocalDateTime end = LocalDateTime.now().plusMinutes(-10);

        List<NotificationEntity> list = queryNotification(t -> t.requestCount.between(0, MAX_RETRY - 1).and(t.ok.isFalse()).and(t.modifyTime.between(start, end)))
                .limit(LIMIT)
                .fetch();

        // 更新
        list.forEach(o -> {
            o.setRequestCount(o.getRequestCount() + 1);
            em.merge(o);
        });

        Set<String> hash = list.stream().map(NotificationEntity::getUrlHash).collect(Collectors.toSet());

        List<NotificationUrlHashEntity> hashList = queryNotificationUrlHash(t -> t.urlHash.in(hash)).fetch();

        Map<String, NotificationUrlHashEntity> map = new HashMap<>();
        hashList.forEach(o -> map.put(o.getUrlHash(), o));

        return list.stream()
                .map(o -> {
                    return new Notification(o.getId().toString(), o.getReceiver(), map.get(o.getUrlHash()).getUrl(), o.getForm());
                })
                .filter(o -> o.getUrl() != null).collect(Collectors.toList());
    }

    public void finishSend(Notification notification) {

        NotificationEntity entity = em.find(NotificationEntity.class, Long.parseLong(notification.getId()));
        if (entity != null) {
            entity.setOk(true);
            entity.setRequestCount(entity.getRequestCount() + 1);
            em.merge(entity);
        }
    }

    private NotificationUrlHashEntity getHash(String url) {
        return queryNotificationUrlHash(t -> t.urlHash.eq(urlHash(url))).fetchFirst();
    }

    private JPAQuery<NotificationEntity> queryNotification(Function<QNotificationEntity, Predicate> condition) {
        QNotificationEntity t = QNotificationEntity.notificationEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<NotificationUrlHashEntity> queryNotificationUrlHash(Function<QNotificationUrlHashEntity, Predicate> condition) {
        QNotificationUrlHashEntity t = QNotificationUrlHashEntity.notificationUrlHashEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private String urlHash(String url) {
        return Misc.base64(DigestUtils.getSha256Digest().digest(url.getBytes(StandardCharsets.UTF_8)));
    }
}
