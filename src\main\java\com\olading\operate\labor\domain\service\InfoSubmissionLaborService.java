package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.app.web.biz.enums.InfoSubmissionReportStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.LaborInfoRepository;
import com.olading.operate.labor.domain.share.labor.QLaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity;
import com.olading.operate.labor.domain.share.protocol.QLaborProtocolEntity;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionLaborEntity;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionLaborManager;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborInsertParam;
import com.olading.operate.labor.domain.share.submission.QInfoSubmissionLaborEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborVo;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class InfoSubmissionLaborService {

    private final InfoSubmissionLaborManager infoSubmissionLaborManager;
    private final LaborInfoRepository laborInfoRepository;
    private final EntityManager entityManager;

    /**
     * 更新人员信息报送记录
     */
    public InfoSubmissionLaborEntity updateInfoSubmissionLabor(TenantInfo tenantInfo, InfoSubmissionLaborVo vo) {
        InfoSubmissionLaborEntity entity = infoSubmissionLaborManager.updateInfoSubmissionLabor(vo);
        return entity;
    }

    /**
     * 查询人员信息报送记录详情
     */
    public InfoSubmissionLaborVo queryInfoSubmissionLabor(Long id) {
        InfoSubmissionLaborVo vo = infoSubmissionLaborManager.queryInfoSubmissionLabor(id);
        return vo;
    }

    /**
     * 删除人员信息报送记录
     */
    public void deleteInfoSubmissionLabor(TenantInfo tenantInfo, Long id) {
        // 删除记录
        infoSubmissionLaborManager.deleteInfoSubmissionLabor(id);
    }

    /**
     * 根据作业主体ID查询人员信息报送记录列表（按ID倒序）
     */
    public List<InfoSubmissionLaborEntity> queryInfoSubmissionLaborBySupplierCorporationId(Long supplierCorporationId) {
        QInfoSubmissionLaborEntity entity = QInfoSubmissionLaborEntity.infoSubmissionLaborEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.supplierCorporationId.eq(supplierCorporationId)
                        .and(entity.deleted.eq(false)))
                .orderBy(entity.id.desc())
                .fetch();
    }

    /**
     * 根据参数插入人员信息报送记录
     */
    @Transactional
    public InfoSubmissionLaborEntity insertInfoSubmissionLabor(InfoSubmissionLaborInsertParam param) {
        // 检查是否已存在记录
        if (existsInfoSubmissionLabor(param.getSupplierCorporationId(), param.getIdCard())) {
            log.info("人员信息报送记录已存在，supplierCorporationId: {}, idCard: {}",
                    param.getSupplierCorporationId(), param.getIdCard());
            return null;
        }

        // 查询t_labor_protocol表
        LaborProtocolEntity laborProtocol = findLaborProtocol(param.getSupplierId(), param.getSupplierCorporationId(), param.getIdCard());
        if (laborProtocol == null) {
            throw new BusinessException("未找到对应的劳务协议记录");
        }

        // 查询t_supplier_labor表
        SupplierLaborEntity supplierLabor = laborInfoRepository.findLaborByIdCard(param.getIdCard(), param.getSupplierId());
        if (supplierLabor == null) {
            throw new BusinessException("未找到对应的劳务人员记录");
        }

        // 查询t_labor_info表
        LaborInfoEntity laborInfo = findLaborInfoByLaborId(supplierLabor.getId());
        if (laborInfo == null) {
            throw new BusinessException("未找到对应的劳务人员详细信息");
        }

        // 创建InfoSubmissionLaborEntity
        InfoSubmissionLaborEntity entity = new InfoSubmissionLaborEntity();

        // 设置字段值
        entity.setSupplierId(laborProtocol.getSupplierId());
        entity.setSupplierCorporationId(laborProtocol.getSupplierCorporationId());
        entity.setReportStatus(InfoSubmissionReportStatusEnum.PENDING_REPORT.name());
        entity.setLaborName(supplierLabor.getName());
        entity.setCertificateType(CertificateTypeEnum.ID_CARD.name());
        entity.setIdCard(supplierLabor.getIdCard());
        entity.setHouseholdCity(laborInfo.getHouseholdCity());
        entity.setHouseholdAddress(laborInfo.getHouseholdAddress());
        entity.setStoreName(supplierLabor.getName());
        entity.setStoreUniqueCode(generateStoreUniqueCode(param.getSupplierCorporationId(), supplierLabor.getIdCard()));
        entity.setCardBank(laborInfo.getCardBank());
        entity.setBankCard(laborInfo.getBankCard());
        entity.setContactName(supplierLabor.getName());
        entity.setContactPhone(supplierLabor.getCellphone());
        entity.setStartDate(laborProtocol.getStartDate() != null ? laborProtocol.getStartDate().toString() : null);
        entity.setEndDate(laborProtocol.getEndDate() != null ? laborProtocol.getEndDate().toString() : null);

        // 保存记录
        return entityManager.merge(entity);
    }

    /**
     * 校验人员信息报送记录VO
     */
    private void validateInfoSubmissionLaborVo(InfoSubmissionLaborVo vo) {
        if (vo.getSupplierCorporationId() == null) {
            throw new BusinessException("作业主体ID不能为空");
        }
        if (vo.getSupplierId() == null) {
            throw new BusinessException("灵工平台ID不能为空");
        }
        // 可以根据业务需求添加其他必填字段的校验
    }

    /**
     * 检查是否已存在人员信息报送记录
     */
    private boolean existsInfoSubmissionLabor(Long supplierCorporationId, String idCard) {
        QInfoSubmissionLaborEntity entity = QInfoSubmissionLaborEntity.infoSubmissionLaborEntity;
        Long count = new JPAQueryFactory(entityManager)
                .select(entity.count())
                .from(entity)
                .where(entity.supplierCorporationId.eq(supplierCorporationId)
                        .and(entity.idCard.eq(idCard)))
                .fetchOne();
        return count != null && count > 0;
    }

    /**
     * 查询劳务协议记录
     */
    private LaborProtocolEntity findLaborProtocol(Long supplierId, Long supplierCorporationId, String idCard) {
        QLaborProtocolEntity entity = QLaborProtocolEntity.laborProtocolEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.supplierId.eq(supplierId)
                        .and(entity.supplierCorporationId.eq(supplierCorporationId))
                        .and(entity.idCard.eq(idCard)))
                .fetchFirst();
    }

    /**
     * 根据laborId查询劳务人员详细信息
     */
    private LaborInfoEntity findLaborInfoByLaborId(Long laborId) {
        QLaborInfoEntity entity = QLaborInfoEntity.laborInfoEntity;
        return new JPAQueryFactory(entityManager)
                .select(entity)
                .from(entity)
                .where(entity.laborId.eq(laborId))
                .fetchFirst();
    }

    /**
     * 生成店铺唯一标识码：supplierCorporationId_哈希后的idCard
     */
    private String generateStoreUniqueCode(Long supplierCorporationId, String idCard) {
        String hashedIdCard = DigestUtils.md5Hex(idCard);
        return supplierCorporationId + "_" + hashedIdCard;
    }
}
