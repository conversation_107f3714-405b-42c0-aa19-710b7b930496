package com.olading.operate.labor.domain.bill.dto;

import com.olading.operate.labor.util.excel.ExcelColumn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入验证错误信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportValidationError {
    
    /**
     * 行号
     */
    @ExcelColumn(name = "行号", required = true)
    private int rowNum;
    
    /**
     * 字段名
     */
    @ExcelColumn(name = "错误列", required = true)
    private String fieldName;
    
    /**
     * 错误信息
     */
    @ExcelColumn(name = "错误信息", required = true)
    private String errorMessage;
}