package com.olading.operate.labor.domain.invoice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
@Schema(description = "可开票账单查询请求")
public class AvailableBillRequest {
    
    @NotNull(message = "合同ID不能为空")
    @Schema(description = "合同ID")
    private Long contractId;
    
    @Schema(description = "账单月份，不传则查询所有月份")
    private LocalDate billMonth;
}