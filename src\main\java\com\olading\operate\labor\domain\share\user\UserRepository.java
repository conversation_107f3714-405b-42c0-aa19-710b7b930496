package com.olading.operate.labor.domain.share.user;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<UserEntity, Long>, JpaSpecificationExecutor<UserEntity> {
    
    Optional<UserEntity> findByTenantIdAndCellphone(String tenantId, String cellphone);
    
    Optional<UserEntity> findByTenantIdAndAccountNo(String tenantId, String accountNo);
    
    @Query("SELECT u FROM UserEntity u WHERE u.tenantId = :tenantId AND u.deleted = false")
    Page<UserEntity> findActiveUsers(@Param("tenantId") String tenantId, Pageable pageable);

    @Modifying
    @Query("UPDATE UserEntity c SET c.name = :name WHERE c.id = :id")
    int updateName(@Param("id") Long id, @Param("name") String name);
}