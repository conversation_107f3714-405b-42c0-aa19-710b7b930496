package com.olading.operate.labor.util.validation.constraints;

import com.olading.operate.labor.util.excel.ExcelConstraint;
import com.olading.operate.labor.util.textfilter.RemoveEnterFilter;
import com.olading.operate.labor.util.textfilter.TrimBlankFilter;
import com.olading.operate.labor.util.textfilter.TrimQuoteFilter;
import com.olading.operate.labor.util.validation.validator.DateTimeValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日期时间校对
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        DateTimeValidator.class
})
@ExcelConstraint(
        width = 20,
        filters = {
                RemoveEnterFilter.class,
                TrimBlankFilter.class,
                TrimQuoteFilter.class,
                TrimBlankFilter.class
        }
)
public @interface DateTime {
    boolean required() default false;

    String label() default "";

    String message() default "";

    /**
     * 格式
     * @return
     */
    String format() default "yyyy-MM-dd HH:mm:ss";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
