package com.olading.operate.labor;

import com.olading.boot.core.BootConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;

@Import(BootConfiguration.class)
@SpringBootApplication
@EnableConfigurationProperties(AppProperties.class)
public class OperateLaborApplication {

    public static void main(String[] args) {
        SpringApplication.run(OperateLaborApplication.class, args);
    }
}
