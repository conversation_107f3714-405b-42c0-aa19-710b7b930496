package com.olading.operate.labor.domain;

import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.domain.salary.NewFormulaCalculationExample;
import com.olading.operate.labor.domain.salary.engine.SalaryTaxCalculationService;
import com.olading.operate.labor.domain.service.SalaryCalculateService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @description:
 * @author: zhua<PERSON><PERSON><PERSON>
 * @time: 2025/7/18 16:04
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class SalaryFormulaCalculationTest extends BaseTest {

    @Autowired
    private NewFormulaCalculationExample newFormulaCalculationExample;

    @Autowired
    private SalaryTaxCalculationService salaryTaxCalculationService;

    @Test
    public void runAllExamples() {
        newFormulaCalculationExample.runAllExamples();
    }

    @Test
    public void setSalaryCalculateService(){
        salaryTaxCalculationService.batchCalculateSalaryTax(8L);
    }


}
