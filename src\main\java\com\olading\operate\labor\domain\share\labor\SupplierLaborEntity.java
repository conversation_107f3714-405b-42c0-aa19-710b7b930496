package com.olading.operate.labor.domain.share.labor;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Comment("平台劳务人员表")
@Entity
@Table(name = "t_supplier_labor")
@Getter
@Setter
public class SupplierLaborEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("服务运营方编号")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @Size(max = 20)
    @Comment("姓名")
    @Column(name = "name", length = 20)
    private String name;

    @Size(max = 20)
    @Comment("身份证号")
    @Column(name = "id_card", length = 20)
    private String idCard;

    @Size(max = 11)
    @NotNull
    @Comment("手机号,平台下唯一")
    @Column(name = "cellphone", nullable = false, length = 11)
    private String cellphone;

    @Comment("出生日期")
    @Column(name = "birthday_date")
    private String birthdayDate;

    @Comment("证件有效期")
    @Column(name = "id_card_period")
    private String idCardPeriod;

    @Comment("实名认证状态")
    @Column(name = "auth_status")
    private Boolean authStatus;

    @Comment("实名认证时间")
    @Column(name = "auth_time")
    private LocalDateTime authTime;

    public SupplierLaborEntity(TenantInfo tenantInfo, Long supplierId){
        setTenant(tenantInfo);
        setSupplierId(supplierId);
        authStatus = false;
    }

    public SupplierLaborEntity() {
    }

}