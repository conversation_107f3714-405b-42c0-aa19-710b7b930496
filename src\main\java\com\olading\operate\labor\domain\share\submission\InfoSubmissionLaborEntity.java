package com.olading.operate.labor.domain.share.submission;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("人员信息报送表")
@Entity
@Table(name = "t_info_submission_labor", schema = "olading_labor")
public class InfoSubmissionLaborEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 20)
    @Comment("报送状态")
    @Column(name = "report_status", length = 20)
    private String reportStatus;

    @Size(max = 20)
    @Comment("是否已取得登记证照")
    @Column(name = "registration_license_obtained", length = 20)
    private String registrationLicenseObtained;

    @Size(max = 20)
    @Comment("名称（姓名）")
    @Column(name = "name", length = 20)
    private String name;

    @Size(max = 64)
    @Comment("统一社会信用代码（纳税人识别号）")
    @Column(name = "unified_social_credit_code", length = 64)
    private String unifiedSocialCreditCode;

    @Size(max = 64)
    @Comment("专业服务机构标识")
    @Column(name = "professional_service_agency_flag", length = 64)
    private String professionalServiceAgencyFlag;

    @Size(max = 20)
    @Comment("姓名")
    @Column(name = "labor_name", length = 20)
    private String laborName;

    @Size(max = 20)
    @Comment("证件类型")
    @Column(name = "certificate_type", length = 20)
    private String certificateType;

    @Size(max = 20)
    @Comment("身份证号")
    @Column(name = "id_card", length = 20)
    private String idCard;

    @Size(max = 50)
    @Comment("国家或地区")
    @Column(name = "household_city", length = 50)
    private String householdCity;

    @Size(max = 64)
    @Comment("是否存在免于报送收入信息情形")
    @Column(name = "income_reporting_exemption_flag", length = 64)
    private String incomeReportingExemptionFlag;

    @Size(max = 20)
    @Comment("免报类型")
    @Column(name = "exemption_type", length = 20)
    private String exemptionType;

    @Size(max = 256)
    @Comment("地址")
    @Column(name = "household_address", length = 256)
    private String householdAddress;

    @Size(max = 64)
    @Comment("店铺（用户）名称")
    @Column(name = "store_name", length = 64)
    private String storeName;

    @Size(max = 128)
    @Comment("店铺（用户）唯一标识码")
    @Column(name = "store_unique_code", length = 128)
    private String storeUniqueCode;

    @Size(max = 64)
    @Comment("网址链接（选填）")
    @Column(name = "website_url", length = 64)
    private String websiteUrl;

    @Size(max = 64)
    @Comment("开户银行/非银行支付机构")
    @Column(name = "card_bank", length = 64)
    private String cardBank;

    @Size(max = 64)
    @Comment("账户名称")
    @Column(name = "account_name", length = 64)
    private String accountName;

    @Size(max = 64)
    @Comment("银行账号/支付账户")
    @Column(name = "bank_card", length = 64)
    private String bankCard;

    @Size(max = 20)
    @Comment("联系人姓名")
    @Column(name = "contact_name", length = 20)
    private String contactName;

    @Size(max = 20)
    @Comment("联系电话")
    @Column(name = "contact_phone", length = 20)
    private String contactPhone;

    @Size(max = 20)
    @Comment("经营开始时间")
    @Column(name = "start_date", length = 20)
    private String startDate;

    @Size(max = 20)
    @Comment("经营结束时间")
    @Column(name = "end_date", length = 20)
    private String endDate;

    @Size(max = 20)
    @Comment("信息状态标识")
    @Column(name = "info_status_flag", length = 20)
    private String infoStatusFlag;

    public InfoSubmissionLaborEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }

    public InfoSubmissionLaborEntity() {
    }

}