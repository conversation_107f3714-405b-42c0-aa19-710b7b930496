package com.olading.operate.labor.domain.share.signing.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "文件查询请求参数")
public class QueryFileRequest extends BaseRequest<QueryFileRequest> {

    @Schema(description = "文件ID", required = true)
    private Long id;

}
