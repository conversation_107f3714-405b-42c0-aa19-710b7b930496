package com.olading.operate.labor.domain.share.submission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InfoSubmissionEnterpriseVo {
    @Schema(description = "企业信息报送记录ID")
    private Long id;

    @Schema(description = "灵工平台ID")
    private Long supplierId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "报送状态")
    private String reportStatus;

    @Schema(description = "名称（姓名）")
    private String name;

    @Schema(description = "统一社会信用代码（纳税人识别号）")
    private String socialCreditCode;

    @Schema(description = "平台内的平台名称")
    private String platformName;

    @Schema(description = "平台内的平台唯一标识码")
    private String platformUniqueCode;

    @Schema(description = "经营开始时间")
    private String startDate;

    @Schema(description = "经营结束时间")
    private String endDate;

    @Schema(description = "信息状态标识")
    private String infoStatusFlag;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;
}
