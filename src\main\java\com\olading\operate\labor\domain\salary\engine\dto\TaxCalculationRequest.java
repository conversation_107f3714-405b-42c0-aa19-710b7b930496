package com.olading.operate.labor.domain.salary.engine.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 个税计算请求对象
 */
@Data
public class TaxCalculationRequest {
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 作业主体ID
     */
    private Long supplierCorporationId;
    
    /**
     * 税款所属期 (格式: YYYY-MM)
     */
    private String taxPeriod;
    
    /**
     * 薪酬明细ID
     */
    private Long salaryDetailId;
    
    // ========== 本期传入数据 ==========
    
    /**
     * 本期应发金额
     */
    private BigDecimal payableAmount;
    
    /**
     * 本期免税收入
     */
    private BigDecimal taxFreeIncome;
    
    /**
     * 本期依法确定的其他扣除
     */
    private BigDecimal otherDeductions;
    
    /**
     * 本期减免税额
     */
    private BigDecimal taxReliefAmount;

    
    /**
     * 构造方法
     */
    public TaxCalculationRequest() {
        this.payableAmount = BigDecimal.ZERO;
        this.taxFreeIncome = BigDecimal.ZERO;
        this.otherDeductions = BigDecimal.ZERO;
        this.taxReliefAmount = BigDecimal.ZERO;
    }
    
    /**
     * 构造方法
     */
    public TaxCalculationRequest(String idCard, Long supplierCorporationId, String taxPeriod, 
                                BigDecimal payableAmount) {
        this();
        this.idCard = idCard;
        this.supplierCorporationId = supplierCorporationId;
        this.taxPeriod = taxPeriod;
        this.payableAmount = payableAmount;
    }
    
    /**
     * 构造方法
     */
    public TaxCalculationRequest(String idCard, Long supplierCorporationId, String taxPeriod, 
                                BigDecimal payableAmount, BigDecimal taxFreeIncome, 
                                BigDecimal otherDeductions, BigDecimal taxReliefAmount) {
        this.idCard = idCard;
        this.supplierCorporationId = supplierCorporationId;
        this.taxPeriod = taxPeriod;
        this.payableAmount = payableAmount != null ? payableAmount : BigDecimal.ZERO;
        this.taxFreeIncome = taxFreeIncome != null ? taxFreeIncome : BigDecimal.ZERO;
        this.otherDeductions = otherDeductions != null ? otherDeductions : BigDecimal.ZERO;
        this.taxReliefAmount = taxReliefAmount != null ? taxReliefAmount : BigDecimal.ZERO;
    }
    
    /**
     * 校验请求参数
     */
    public void validate() {
        if (idCard == null || idCard.trim().isEmpty()) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        if (supplierCorporationId == null) {
            throw new IllegalArgumentException("作业主体ID不能为空");
        }
        if (taxPeriod == null || taxPeriod.trim().isEmpty()) {
            throw new IllegalArgumentException("税款所属期不能为空");
        }
        if (payableAmount == null || payableAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("应发金额不能为空且不能为负数");
        }
        if (taxFreeIncome == null || taxFreeIncome.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("免税收入不能为负数");
        }
        if (otherDeductions == null || otherDeductions.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("其他扣除不能为负数");
        }
        if (taxReliefAmount == null || taxReliefAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("减免税额不能为负数");
        }
        
        // 校验税期格式 (YYYY-MM)
        if (!taxPeriod.matches("\\d{4}-\\d{2}")) {
            throw new IllegalArgumentException("税款所属期格式错误，应为YYYY-MM格式");
        }
    }
    
    @Override
    public String toString() {
        return "TaxCalculationRequest{" +
                "idCard='" + maskIdCard(idCard) + '\'' +
                ", supplierCorporationId=" + supplierCorporationId +
                ", taxPeriod='" + taxPeriod + '\'' +
                ", payableAmount=" + payableAmount +
                ", taxFreeIncome=" + taxFreeIncome +
                ", otherDeductions=" + otherDeductions +
                ", taxReliefAmount=" + taxReliefAmount +
                '}';
    }
    
    /**
     * 脱敏身份证号
     */
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "****" + idCard.substring(idCard.length() - 4);
    }
}