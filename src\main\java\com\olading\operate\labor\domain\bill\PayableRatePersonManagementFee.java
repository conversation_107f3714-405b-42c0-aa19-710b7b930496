package com.olading.operate.labor.domain.bill;

import com.olading.operate.labor.app.web.biz.enums.ManageCalculationRuleEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/16 19:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayableRatePersonManagementFee implements  PersonManagementFee{
    @Override
    public BigDecimal calculatePersonManagementFee(BillManager.PersonManagementFeeInfo personInfo, BigDecimal manageRate, BigDecimal manageAmount) {
        return personInfo.getPayableAmount().multiply(manageRate.divide(new BigDecimal("100")));
    }

    @Override
    public String getFeeItem() {
        return ManageCalculationRuleEnum.PAYABLE_AMOUNT_RATE.name();
    }
}
