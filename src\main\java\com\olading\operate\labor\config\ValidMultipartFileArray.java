package com.olading.operate.labor.config;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = MultipartFileArrayValidator.class)
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidMultipartFileArray {
    String message() default "文件数量、大小或类型超出限制";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int maxCount() default 10; // 最大文件数量

    long maxSizeInBytes() default 10 * 1024 * 1024; // 默认最大总大小 10MB

    String[] allowedTypes() default {"application/pdf", "image/jpeg", "image/png"}; // 允许的文件类型
}