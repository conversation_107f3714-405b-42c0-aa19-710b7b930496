package com.olading.operate.labor.app.web.biz.supplier;

import com.olading.operate.labor.app.query.WebApiQueryService;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.share.file.FileManager;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "服务商接口")
@RestController
@RequestMapping("/api/supplier/receipt")
@RequiredArgsConstructor
@Slf4j
public class SupplierReceiptController extends BusinessController {

    private final WebApiQueryService webApiQueryService;
    private final FileManager fileManager;



}
