package com.olading.operate.labor.util.validation.validator;

import com.lanmaoly.util.lang.ValidateUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Money;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class MoneyValidator extends BaseValidator<Money> {
    private double max = 999999999;
    private double min = 0;

    public MoneyValidator() {}

    public MoneyValidator(String name) {
        setName(name);
    }

    private boolean strictMode = true;

    public void setMax(double max) {
        this.max = max;
    }

    public void setMin(double min) {
        this.min = min;
    }

    public void setStrictMode(boolean strictMode) {
        this.strictMode = strictMode;
    }


    @Override
    public void initialize(Money constraintAnnotation) {
        max = constraintAnnotation.max();
        min = constraintAnnotation.min();
        strictMode = constraintAnnotation.strictMode();
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        super.initialize(constraintAnnotation);
    }

    @Override
    protected boolean constraintCheck(Object o) {
        String fieldName = getName();
        String s = String.valueOf(o);

        try {
            ValidateUtils.checkMoney(s, strictMode);
        } catch (ValidationException e) {
            e.setName(fieldName);
            throw e;
        }


        // 去掉千位分隔符
        s = s.replace(",", "");

        double number = Double.parseDouble(s);
        if (number < min) {
            throw new ValidationException(fieldName, String.format("不能小于%s", new BigDecimal(min)));
        } else if (number > max) {
            throw new ValidationException(fieldName, String.format("不能大于%s", new BigDecimal(max)));
        }

        return true;
    }
}
