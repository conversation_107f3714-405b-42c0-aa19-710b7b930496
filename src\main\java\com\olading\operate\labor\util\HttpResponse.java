package com.olading.operate.labor.util;

import org.apache.http.Header;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

public class HttpResponse {

    private int statusCode;

    private List<Header> headers;

    private byte[] data;

    public HttpResponse(int statusCode, List<Header> headers, byte[] data) {
        this.statusCode = statusCode;
        this.headers = Collections.unmodifiableList(headers);
        this.data = data;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public List<Header> getHeaders() {
        return headers;
    }

    public byte[] bodyToByteArray() {
        return data.clone();
    }

    public String bodyToString() {
        return bodyToString(StandardCharsets.UTF_8);
    }

    public String bodyToString(Charset charset) {
        return new String(data, charset);
    }
}
