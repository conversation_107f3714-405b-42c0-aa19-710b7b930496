package com.olading.operate.labor.domain.share.tax.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ValueAddedTaxDeclareVo {
    @Schema(description = "增值税申报ID")
    private Long id;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "税款所属期")
    private String taxPaymentPeriod;

    @Schema(description = "个税申报月")
    private String incomeTaxMonth;

    @Schema(description = "纳税人数")
    private String taxpayersCount;

    @Schema(description = "本期收入")
    private String currentIncome;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "灵工平台ID")
    private Long supplierId;

    @Schema(description = "增值税总额")
    private BigDecimal totalVatAmount;

    @Schema(description = "附加税总额")
    private BigDecimal totalSurtaxAmount;

    @Schema(description = "生成状态")
    private String status;

    @Schema(description = "申报状态")
    private String taxStatus;

    @Schema(description = "增值税申报明细列表")
    private List<ValueAddedTaxDetailVo> details;

}
