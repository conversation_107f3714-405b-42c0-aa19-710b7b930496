package com.olading.operate.labor.util.validation.constraints;

import com.olading.operate.labor.util.excel.ExcelConstraint;
import com.olading.operate.labor.util.textfilter.TrimBlankFilter;
import com.olading.operate.labor.util.validation.validator.TextValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日期时间校对
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        TextValidator.class
})
@ExcelConstraint(
        width = 20,
        filters = {
                TrimBlankFilter.class
        }
)
public @interface Text {
    /**
     * 是否必填
     * @return
     */
    boolean required() default false;

    /**
     * 字段名字，自动组装提示语用
     * @return
     */
    String label() default "";

    /**
     * 自定义提示文案
     * @return
     */
    String message() default "";

    /**
     * 正则匹配规则
     * @return
     */
    String regex() default "";

    String[] options() default {};

    /**
     * 最大长度
     * @return
     */
    int maxLength() default TextValidator.MAX_LENGTH;

    /**
     * 最小长度
     * @return
     */
    int minLength() default TextValidator.MIN_LENGTH;

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
