package com.olading.operate.labor.domain.identity;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

/**
 * OCR识别记录实体
 * 任务 2.2: 创建OCR识别记录实体类
 */
@Entity
@Table(name = "t_identity_ocr_record")
@Comment("OCR识别记录表")
@Data
public class IdentityOcrRecordEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    @Comment("认证记录编号")
    @Column(name = "record_no", length = 64, nullable = false, unique = true)
    private String recordNo;

    @Comment("用户ID（可空，兼容非登录场景）")
    @Column(name = "user_id")
    private Long userId;

    @Comment("供应商ID")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("作业主体ID")
    @Column(name = "corporation_id")
    private Long corporationId;

    @Comment("真实姓名（OCR识别出的）")
    @Column(name = "name", length = 32)
    private String name;

    @Comment("身份证号（OCR识别出的）")
    @Column(name = "id_card_no", length = 20)
    private String idCardNo;

    @Comment("性别")
    @Column(name = "sex", length = 1)
    private String sex;

    @Comment("民族")
    @Column(name = "nation", length = 10)
    private String nation;

    @Comment("出生日期")
    @Column(name = "birthday", length = 20)
    private String birthday;

    @Comment("出生地址")
    @Column(name = "birth_address", length = 100)
    private String birthAddress;

    @Comment("身份证签发机关")
    @Column(name = "id_card_signing_organs", length = 20)
    private String idCardSigningOrgans;

    @Comment("身份证有效期")
    @Column(name = "id_card_validity_period", length = 32)
    private String idCardValidityPeriod;

    @Comment("国徽面图片文件ID")
    @Column(name = "national_emblem_file_id", length = 32)
    private String nationalEmblemFileId;

    @Comment("个人证件照面图片文件ID")
    @Column(name = "personal_id_file_id", length = 32)
    private String personalIdFileId;

    @Comment("认证场景：CONTRACT_SIGNING,PAYMENT_DISBURSEMENT,REAL_NAME_AUTH")
    @Column(name = "auth_scene", length = 30, nullable = false)
    private String authScene;

    @Comment("OCR状态：SUCCESS,FAILED")
    @Column(name = "ocr_status", length = 20, nullable = false)
    private String ocrStatus;

    @Comment("识别置信度")
    @Column(name = "confidence_score", precision = 5, scale = 4)
    private BigDecimal confidenceScore;

    @Comment("错误码")
    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Comment("错误信息")
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    public IdentityOcrRecordEntity(TenantInfo tenantInfo) {
        setTenant(tenantInfo);
    }

    public IdentityOcrRecordEntity() {
    }

}