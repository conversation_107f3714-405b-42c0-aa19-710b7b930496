package com.olading.operate.labor.domain.share.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.URLUtil;
import com.olading.boot.core.spi.FileStorageProvider;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.util.Utils;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

@Transactional
@Component
@RequiredArgsConstructor
public class FileManager {

    private final EntityManager em;
    private final FileStorageProvider storageProvider;

    public String saveTemp(String name, InputStream input, OwnerType ownerType, String ownerId) {
        return save(name, input, LocalDateTime.now().plusDays(1), ownerType, ownerId);
    }

    /**
     * 保存一个文件
     */
    @SneakyThrows
    public String save(String name, InputStream input, LocalDateTime expiryTime, OwnerType ownerType, String ownerId) {

        String id = Utils.uuid();
        String storageId = storageProvider.writeFile(id, name, input);
        FileEntity file = new FileEntity(id, name, storageId, ownerType, ownerId);
        file.setExpiryTime(expiryTime);
        return em.merge(file).getId();
    }

    /**
     * 读取文件信息
     */
    public FileInfo getInfo(String id) {
        FileEntity file = require(id);

        FileInfo info = new FileInfo();
        info.setId(file.getId());
        info.setName(file.getName());
        return info;
    }

    /**
     * 读取文件内容
     */
    @SneakyThrows
    public void load(String id, OutputStream output) {
        FileEntity file = require(id);
        storageProvider.readFile(file.getStorageId(), output);
    }

    public byte[] load(String id) {
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        load(id, buff);
        return buff.toByteArray();
    }

    public InputStream loadAsStream(String id) {
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        load(id, buff);
        return new ByteArrayInputStream(buff.toByteArray());
    }

    @SneakyThrows
    public void delete(String id) {
        FileEntity file = require(id);
        storageProvider.deleteFile(file.getStorageId());
    }

    public void modify(String id, LocalDateTime expiryTime) {
        FileEntity file = require(id);
        if (expiryTime != null) {
            file.setExpiryTime(expiryTime);
        }
        em.merge(file);
    }

    /**
     * 标记文件为永久文件
     */
    public void markPermanent(String id) {
        modify(id, LocalDateTime.now().plusYears(1000));
    }

    public FileEntity require(String id) {
        FileEntity file = em.find(FileEntity.class, id);
        if (file == null) {
            throw new IllegalStateException("文件不存在");
        }
        return file;
    }


    /**
     * 保存一个文件
     */
    @SneakyThrows
    public String save(String url, OwnerType ownerType, String ownerId) {

        String id = Utils.uuid();
        final String name = extractFileNameFromUrl(url);
        String storageId = storageProvider.writeFile(id,name , URLUtil.getStream(new URL(url)));
        FileEntity file = new FileEntity(id, name, storageId, ownerType, ownerId);
        file.setExpiryTime(LocalDateTime.now().plusYears(100));
        return em.merge(file).getId();
    }



    /**
     * 从URL中提取文件名
     *
     * @param url 文件URL
     * @return 文件名，如果无法提取则返回null
     */
    private static String extractFileNameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        try {
            // 使用URI来正确处理URL编码
            URI uri = new URI(url);
            String path = uri.getPath();

            if (path != null && !path.isEmpty()) {
                // 获取路径中最后一个斜杠后的内容
                String fileName = path.substring(path.lastIndexOf('/') + 1);

                // URL解码文件名（处理可能的URL编码）
                fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8.toString());

                // 如果文件名不为空且不等于斜杠，则返回文件名
                if (!fileName.isEmpty() && !"/".equals(fileName)) {
                    return fileName;
                }
            }

            // 如果path中没有找到文件名，尝试从查询参数中获取
            String query = uri.getQuery();
            if (query != null && !query.isEmpty()) {
                // 尝试从常见的查询参数中提取文件名
                String[] queryParams = query.split("&");
                for (String param : queryParams) {
                    if (param.startsWith("filename=") || param.startsWith("file=") || param.startsWith("name=")) {
                        String fileName = param.substring(param.indexOf('=') + 1);
                        return URLDecoder.decode(fileName, StandardCharsets.UTF_8.toString());
                    }
                }
            }

        } catch (URISyntaxException | UnsupportedEncodingException e) {
            // 如果URI解析失败，使用简单的字符串处理
            return extractFileNameFromUrlSimple(url);
        }

        return null;
    }

    /**
     * 简单的URL文件名提取方法（备用方案）
     *
     * @param url 文件URL
     * @return 文件名
     */
    private static String extractFileNameFromUrlSimple(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        // 移除查询参数和锚点
        int queryIndex = url.indexOf('?');
        if (queryIndex != -1) {
            url = url.substring(0, queryIndex);
        }

        int fragmentIndex = url.indexOf('#');
        if (fragmentIndex != -1) {
            url = url.substring(0, fragmentIndex);
        }

        // 获取最后一个斜杠后的内容
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
            String fileName = url.substring(lastSlashIndex + 1);
            if (!fileName.isEmpty()) {
                return fileName;
            }
        }

        return null;
    }

}
