package com.olading.operate.labor.util;

import com.lanmaoly.util.lang.StreamUtils;
import org.apache.commons.beanutils.BeanUtilsBean2;
import org.apache.commons.beanutils.PropertyUtilsBean;
import org.apache.commons.lang3.StringUtils;
import org.supercsv.io.CsvListWriter;
import org.supercsv.prefs.CsvPreference;

import java.io.IOException;
import java.io.Writer;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class CsvTableGenerator {

    private final List<Column> columns = new ArrayList<>();

    private String[] dataPath = new String[]{};

    public <T> CsvTableGenerator dataPath(String... dataPath) {
        this.dataPath = dataPath;
        return this;
    }

    @SuppressWarnings("unchecked")
    public <T> CsvTableGenerator column(String title, Formatter<T> formatter, String... propsPath) {
        columns.add(new Column(title, propsPath, (Formatter<Object>) formatter));
        return this;
    }

    public CsvWriter writer(Object data) {
        return new CsvWriter(data, dataPath);
    }

    @FunctionalInterface
    public interface Formatter<T> {

        String format(T value);
    }

    public class CsvWriter {

        private final Object data;

        private final String[] dataPath;

        public CsvWriter(Object data, String[] dataPath) {
            this.data = data;
            this.dataPath = dataPath;
        }

        public void write(Writer output) throws IOException {

            CsvListWriter writer = new CsvListWriter(output, CsvPreference.STANDARD_PREFERENCE);

            // 写入标题列
            writer.write(StreamUtils.map(columns, Column::getTitle));

            // 逐行写入数据
            Object bean = props(data, dataPath);
            if (bean instanceof Collection) {
                Collection<?> c = (Collection<?>) bean;
                for (Object item : c) {
                    writer.write(row(item));
                }
            }

            writer.flush();
        }

        private ArrayList<String> row(Object item) {
            ArrayList<String> row = new ArrayList<>();
            for (Column column : columns) {
                Object value = props(item, column.getPropsPath());
                if (column.getFormatter() != null) {
                    value = column.getFormatter().format(value);
                }
                row.add(value.toString());
            }
            return row;
        }
    }

    private static class Column {

        private final String title;

        private final String[] propsPath;

        private final Formatter<Object> formatter;

        public Column(String title, String[] propsPath, Formatter<Object> formatter) {
            this.title = title;
            this.propsPath = propsPath;
            this.formatter = formatter;
        }

        public String getTitle() {
            return title;
        }

        public String[] getPropsPath() {
            return propsPath;
        }

        public Formatter<Object> getFormatter() {
            return formatter;
        }
    }

    private static Object props(Object o, String... propsPath) {
        try {
            PropertyUtilsBean propertyUtils = BeanUtilsBean2.getInstance().getPropertyUtils();
            Object bean = o;
            if (propsPath != null) {
                for (String s : propsPath) {
                    if (bean == null) {
                        throw new NullPointerException("对象" + o + "的propsPath不正确: " + StringUtils.join(propsPath, "."));
                    }
                    bean = propertyUtils.getProperty(bean, s);
                }
            }
            return bean;
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            throw new IllegalStateException(e);
        }
    }

    public static void main(String[] args) {
        new CsvTableGenerator().column("", (Long a) -> "");
    }

}
