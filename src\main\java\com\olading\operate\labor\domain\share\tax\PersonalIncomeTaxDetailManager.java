package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.domain.TenantInfo;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;

@Transactional
@Component
@RequiredArgsConstructor
public class PersonalIncomeTaxDetailManager {

    private final EntityManager em;

    /**
     * 批量新增个税申报明细记录
     */
    public List<PersonalIncomeTaxDetailEntity> batchAddPersonalIncomeTaxDetail(List<PersonalIncomeTaxDetailEntity> details) {
        for (PersonalIncomeTaxDetailEntity detail : details) {
            em.persist(detail);
        }
        em.flush();
        return details;
    }

    /**
     * 新增个税申报明细记录
     */
    public PersonalIncomeTaxDetailEntity addPersonalIncomeTaxDetail(TenantInfo tenantInfo, PersonalIncomeTaxDetailEntity detail) {
        return em.merge(detail);
    }

    /**
     * 根据申报ID查询明细列表
     */
    public List<PersonalIncomeTaxDetailEntity> queryDetailsByTaxDeclareId(Long taxDeclareId) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.taxDeclareId.eq(taxDeclareId))
                .fetch();
    }

    /**
     * 根据税款所属期和作业主体ID查询明细列表
     */
    public List<PersonalIncomeTaxDetailEntity> queryDetailsByTaxPeriodAndCorporation(String taxPeriod, Long supplierCorporationId) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.taxPeriod.eq(taxPeriod)
                        .and(t.supplierCorporationId.eq(supplierCorporationId)))
                .fetch();
    }

    /**
     * 根据申报ID统计纳税人数
     */
    public Long countTaxpayersByTaxDeclareId(Long taxDeclareId) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t.count())
                .from(t)
                .where(t.taxDeclareId.eq(taxDeclareId))
                .fetchOne();
    }

    /**
     * 根据申报ID统计本期收入总额
     */
    public BigDecimal sumCurrentIncomeByTaxDeclareId(Long taxDeclareId) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        BigDecimal result = new JPAQueryFactory(em)
                .select(t.currentIncome.sum())
                .from(t)
                .where(t.taxDeclareId.eq(taxDeclareId))
                .fetchOne();
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 根据申报ID统计本期应预扣预缴税额总额
     */
    public BigDecimal sumCurrentWithholdingTaxByTaxDeclareId(Long taxDeclareId) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        BigDecimal result = new JPAQueryFactory(em)
                .select(t.currentWithholdingTax.sum())
                .from(t)
                .where(t.taxDeclareId.eq(taxDeclareId))
                .fetchOne();
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 根据申报ID统计总实发金额
     */
    public BigDecimal sumActualAmountByTaxDeclareId(Long taxDeclareId) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        BigDecimal result = new JPAQueryFactory(em)
                .select(t.actualAmount.sum())
                .from(t)
                .where(t.taxDeclareId.eq(taxDeclareId))
                .fetchOne();
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 根据申报ID删除明细记录
     */
    public void deleteDetailsByTaxDeclareId(Long taxDeclareId) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        new JPAQueryFactory(em)
                .delete(t)
                .where(t.taxDeclareId.eq(taxDeclareId))
                .execute();
    }

    /**
     * 通用查询方法
     */
    public JPAQuery<PersonalIncomeTaxDetailEntity> queryPersonalIncomeTaxDetail(Function<QPersonalIncomeTaxDetailEntity, Predicate> condition) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    /**
     * 根据申报ID和身份证号查询明细
     */
    public PersonalIncomeTaxDetailEntity queryDetailByTaxDeclareIdAndIdCard(Long taxDeclareId, String idCard) {
        QPersonalIncomeTaxDetailEntity t = QPersonalIncomeTaxDetailEntity.personalIncomeTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.taxDeclareId.eq(taxDeclareId)
                        .and(t.idCard.eq(idCard)))
                .fetchOne();
    }
}
