package com.olading.operate.labor.app.web.biz.protocol.vo;

import com.olading.operate.labor.app.web.biz.enums.EnumTemplateFieldType;
import com.olading.operate.labor.app.web.biz.protocol.TemplateController;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolTempFiledEntity;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolTempStepEntity;
import com.olading.operate.labor.domain.share.signing.common.Control;
import com.olading.operate.labor.domain.share.signing.common.Field;
import com.olading.operate.labor.domain.share.signing.enums.ControlType;
import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import com.olading.operate.labor.domain.share.signing.enums.SignWay;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Schema(description="合同管理-合同模板-提交")
public class CommitTemplateVo {

    @Schema(description = "合同管理-模板ID")
    private Long templateId;

    @Schema(description = "签署方")
    private List<Signatory> signatories;

    public void checkValid() {
        if (templateId == null) throw new ApiException("请选择模板", ApiException.API_PARAM_ERROR);
        Set<String> fieldNames = new HashSet<>();
        if (signatories != null) signatories.forEach(signatory -> signatory.checkValid(fieldNames));
    }

    public List<CorporationProtocolTempFiledEntity> listTemplateFields(TemplateController.SupplierAndUser supplierAndUser, Map<Long, CorporationProtocolTempStepEntity> idToStep) {
        if (CollectionUtils.isEmpty(signatories)) return Collections.emptyList();
        List<CorporationProtocolTempFiledEntity> templateFields = new ArrayList<>();
        for (Signatory signatory : signatories) {
            CorporationProtocolTempStepEntity step = idToStep.get(signatory.stepId);
            if (step == null || CollectionUtils.isEmpty(signatory.fields)) continue;
            for (TemplateFieldParam field : signatory.fields) {
                CorporationProtocolTempFiledEntity fields = getCorporationProtocolTempFiledEntity(supplierAndUser, field, step);
                templateFields.add(fields);
            }
        }
        return templateFields;
    }

    @NotNull
    private CorporationProtocolTempFiledEntity getCorporationProtocolTempFiledEntity(TemplateController.SupplierAndUser supplierAndUser, TemplateFieldParam field, CorporationProtocolTempStepEntity step) {
        CorporationProtocolTempFiledEntity fields = new CorporationProtocolTempFiledEntity();
        fields.setTenantId(supplierAndUser.getTenantId());
        fields.setSupplierId(supplierAndUser.getSupplier().getId());
        fields.setTemplateId(templateId);
        fields.setTemplateStepId(step.getId());
        fields.setTemplateStepName(step.getStepName());
        fields.setFieldName(field.getName());
        fields.setFieldCode(field.getRelationCode());
        fields.setOperate(field.getFieldType());
        fields.setSignatory(field.getSignatory());
        return fields;
    }

    public List<Long> listStepIds() {
        if (CollectionUtils.isEmpty(signatories)) return Collections.emptyList();
        return signatories.stream().map(Signatory::getStepId).collect(Collectors.toList());
    }

    @Data
    @Schema(description="签署方")
    public static class Signatory {

        @Schema(description = "步骤ID")
        private Long stepId;

        @Schema(description = "签署方类型 SEAL-企业签署方 SIGN-个人签署方")
        private EnumOperateType type;

        @Schema(description = "字段(包含签章/日期/填充域)")
        private List<TemplateFieldParam> fields;

        @Schema(description = "控件")
        private List<ControlParam> controls;

        public void checkValid(Set<String> fieldNames) {
            if (stepId == null) throw new ApiException("请选择签署方", ApiException.API_PARAM_ERROR);
            if (fields != null) fields.forEach(field -> field.checkValid(fieldNames));
            if (type == null) throw new ApiException("请选择签署方类型", ApiException.API_PARAM_ERROR);
            if (CollectionUtils.isEmpty(controls)
                || (type == EnumOperateType.SEAL && controls.stream().noneMatch(control -> control.type == ControlType.SEAL_CONTROL))
                || (type == EnumOperateType.SIGN && controls.stream().noneMatch(control -> control.type == ControlType.SIGN_CONTROL)))
                throw new ApiException("签署人未分配签章控件", ApiException.API_PARAM_ERROR);
        }

        public List<Field> listFields() {
            return Optional.ofNullable(fields).orElse(Collections.emptyList()).stream()
                    .filter(field -> field.getFieldType().equals(EnumTemplateFieldType.FIELD.name()))
                    .map(TemplateFieldParam::to).collect(Collectors.toList());
        }

        public List<Control> listControls() {
            return Optional.ofNullable(controls).orElse(Collections.emptyList())
                    .stream().map(ControlParam::to).collect(Collectors.toList());
        }
    }

    @Data
    @Schema(description="控件")
    static class ControlParam {

        @Schema(description = "控件名称")
        private String name;

        @Schema(description = "类型")
        private ControlType type;

        @Schema(description = "页码")
        private Integer page;

        @Schema(description = "横坐标")
        private String xAxis;

        @Schema(description = "纵坐标(百分比坐标)")
        private String yAxis;

        @Schema(description = "宽度(相对页面百分比)")
        private String width;

        @Schema(description = "高度(相对页面百分比 )")
        private String height;

        @Schema(description = "字体")
        private String fontFamily;

        @Schema(description = "字体大小(相对页面百分比)")
        private String fontSize;

        @Schema(description = "时间格式")
        private String dateFormat;

        @Schema(description = "签署方式")
        private SignWay signWay;

        @Schema(description = "签署步骤(关键字或者签署步骤) 作为填充域的时候就是填充域的名字 作为签章和日期的时候是步骤的名字")
        private String stepName;

        @Schema(description = "默认值")
        private String value;

        public Control to() {
            Control control = new Control();
            control.setName(name);
            control.setType(type);
            control.setPage(page);
            control.setXAxis(new BigDecimal(xAxis));
            control.setYAxis(new BigDecimal(yAxis));
            control.setWidth(new BigDecimal(width));
            control.setHeight(new BigDecimal(height));
            control.setFontFamily(fontFamily);
            control.setFontSize(new BigDecimal(fontSize));
            control.setDateFormat(dateFormat);
            control.setSignWay(signWay);
            control.setStepId(stepName);
            control.setValue(value);
            return control;
        }

        public static ControlParam from(Control control) {
            ControlParam controlParam = new ControlParam();
            controlParam.name = control.getName();
            controlParam.type = control.getType();
            controlParam.page = control.getPage();
            controlParam.xAxis = control.getXAxis().toString();
            controlParam.yAxis = control.getYAxis().toString();
            controlParam.width = control.getWidth().toString();
            controlParam.height = control.getHeight().toString();
            controlParam.fontFamily = control.getFontFamily();
            controlParam.fontSize = control.getFontSize().toString();
            controlParam.dateFormat = control.getDateFormat();
            controlParam.signWay = control.getSignWay();
            controlParam.stepName = control.getStepId();
            controlParam.value = control.getValue();
            return controlParam;
        }
    }

}
