package com.olading.operate.labor.app.web.biz.enums;

public enum BusinessContractStatusEnum {

    INIT("服务中"),
    TERMINATION("提前终止"),
    EXPIRED("已到期");

    private final String name;

    BusinessContractStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameByKey(String key) {
        for (BusinessContractStatusEnum e : values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getName();
            }
        }
        return "";
    }
}
