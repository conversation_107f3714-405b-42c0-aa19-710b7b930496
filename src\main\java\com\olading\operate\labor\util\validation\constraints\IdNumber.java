package com.olading.operate.labor.util.validation.constraints;

import com.olading.operate.labor.util.excel.ExcelConstraint;
import com.olading.operate.labor.util.textfilter.RemoveEnterBlankFilter;
import com.olading.operate.labor.util.textfilter.ToUpperCaseFilter;
import com.olading.operate.labor.util.textfilter.TrimQuoteFilter;
import com.olading.operate.labor.util.validation.validator.IDNumberValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 身份证号码校验
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        IDNumberValidator.class
})
@ExcelConstraint(
        width = 20,
        filters = {
                RemoveEnterBlankFilter.class,
                TrimQuoteFilter.class,
                ToUpperCaseFilter.class
        }
)
public @interface IdNumber {
    boolean required() default false;

    String label() default "";

    String message() default "";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
