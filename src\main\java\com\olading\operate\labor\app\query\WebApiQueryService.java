package com.olading.operate.labor.app.query;

import com.olading.boot.util.jpa.querydsl.QueryFactory;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.query.SalaryDetailQuery;
import com.olading.operate.labor.domain.query.SupplierCorporationQuery;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.vo.SalaryDetailVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class WebApiQueryService {

    private final QueryFactory queryFactory;


    public WebApiQuery<CorporationRecord, SupplierCorporationQuery.Filters, SupplierCorporationEntity> exportCorporation() {
        return new WebApiQuery<>(queryFactory.create(new SupplierCorporationQuery()),
                CorporationRecord.class,
                CorporationRecord::new);
    }

    public WebApiQuery<SalaryDetailVO, SalaryDetailQuery.Filters, SalaryDetailEntity> exportSalaryDetail() {
        return new WebApiQuery<>(queryFactory.create(new SalaryDetailQuery()),
                SalaryDetailVO.class,
                SalaryDetailVO::toVo);
    }
}
