package com.olading.operate.labor.domain;

public class ApiException extends RuntimeException {

    public static final String OK = "0";

    public static final String SYSTEM_ERROR = "0001";

    public static final String API_PARAM_ERROR = "0002";

    public static final String NOT_FOUND = "1001";

    public static final String TRANSFER_QUOTA_EXCEED = "1002";

    public static final String PARAM_ERROR = "1003";

    public static final String ACCOUNT_FAIL = "1004";

    public static final String ACCESS_DENIED = "1005";

    private final String code;

    public ApiException(String message, String code) {
        super(message);
        this.code = code;
    }

    public static ApiException apiError(String message) {
        return new ApiException(message, API_PARAM_ERROR);
    }

    public static ApiException apiVerifyFail() {
        return new ApiException("验签失败", "0003");
    }

    public static ApiException apiKeyFail() {
        return new ApiException("clientKey不正确", "0005");
    }

    public static ApiException notFound() {
        return new ApiException("订单不存在", NOT_FOUND);
    }

    public static ApiException transferQuotaExceed(String message) {
        return new ApiException(message, TRANSFER_QUOTA_EXCEED);
    }

    public static ApiException paramError(String message) {
        return new ApiException(message, PARAM_ERROR);
    }

    public static ApiException accountFail(String message) {
        return new ApiException(message, ACCOUNT_FAIL);
    }

    public static ApiException accessDenied() {
        return new ApiException("无权访问", ACCESS_DENIED);
    }

    public String getCode() {
        return code;
    }
}
