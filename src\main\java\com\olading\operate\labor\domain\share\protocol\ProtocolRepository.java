package com.olading.operate.labor.domain.share.protocol;


import com.olading.operate.labor.app.web.biz.enums.EnumContractSignState;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.app.web.biz.protocol.ProtocolController;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

@RequiredArgsConstructor
@Transactional
@Slf4j
@Component
public class ProtocolRepository {

    private final EntityManager em;

    public void saveProtocol(CorporationProtocolEntity entity) {
        em.persist(entity);
    }

    public void updateProtocol(CorporationProtocolEntity entity) {
        em.merge(entity);
    }
    public void updateProtocolStep(CorporationProtocolStepEntity entity) {
        em.merge(entity);
     }


    public void saveProtocolSteps(CorporationProtocolStepEntity entity) {
        em.persist(entity);
    }

    public void saveProtocolFiled(CorporationProtocolFiledEntity entity) {
        em.persist(entity);
    }

    public List<ProtocolController.LaborProtocolVo> queryLaborProtocol(String idCard, Long supplierId) {
        QCorporationProtocolEntity protocol = QCorporationProtocolEntity.corporationProtocolEntity;
        QCorporationProtocolStepEntity step = QCorporationProtocolStepEntity.corporationProtocolStepEntity;
        QSupplierCorporationEntity corporation = QSupplierCorporationEntity.supplierCorporationEntity;
        List<Tuple> list = new JPAQueryFactory(em)
                .select(protocol, step, corporation)
                .from(step)
                .leftJoin(protocol).on(step.protocolId.eq(protocol.id))
                .leftJoin(corporation).on(protocol.supplierCorporationId.eq(corporation.id))
                .where(step.idCard.eq(idCard)
                        .and(step.signStatus.eq(EnumContractSignState.ACCEPT))
                        .and(protocol.supplierId.eq(supplierId)))
                .fetch();
        return list.stream().map(tuple -> {
            ProtocolController.LaborProtocolVo vo = new ProtocolController.LaborProtocolVo();
            vo.setContractSignStatus(tuple.get(step).getSignStatus().name());
            vo.setCreatedTime(tuple.get(step).getCreateTime());
            vo.setFlowNo(tuple.get(step).getFlowNo());
            vo.setProtocolName(tuple.get(protocol).getProtocolName());
            vo.setStepId(tuple.get(step).getId());
            vo.setCorporationId(tuple.get(protocol).getSupplierCorporationId());
            vo.setCorporationName(tuple.get(corporation).getName());
            vo.setSignContractId(tuple.get(protocol).getAgentContractId());
            vo.setProtocolId(tuple.get(protocol).getId());
            vo.setArchiveId(tuple.get(protocol).getProtocolFileId());
            return vo;
        }).toList();

    }

    public List<CorporationProtocolFiledEntity> queryFieldByTempId(Long tempId) {
        return queryProtocolField(v -> v.protocolId.eq(tempId)).fetch();
    }

    public List<CorporationProtocolStepEntity> queryStepByProtocolId(Long protocol) {
        return queryProtocolStep(v -> v.protocolId.eq(protocol)).fetch();
    }

    public CorporationProtocolStepEntity queryStepById(Long stepId) {
        return queryProtocolStep(v -> v.id.eq(stepId)).fetchOne();
    }

    public List<CorporationProtocolStepEntity> queryStepByStatus(List<EnumContractSignState> stateList, LocalDateTime start, LocalDateTime end) {
        return queryProtocolStep(v -> v.signStatus.in(stateList).and(v.createTime.between(start, end))).fetch();
    }

    public CorporationProtocolEntity queryProtocolById(Long id) {
        return queryProtocol(v -> v.id.eq(id)).fetchOne();
    }

    public List<CorporationProtocolEntity> queryProtocolByStatus(List<EnumContractSignStatus> statusList, LocalDateTime start, LocalDateTime end) {
        return queryProtocol(v -> v.signStatus.in(statusList).and(v.createTime.between(start, end))).fetch();
    }



    private JPAQuery<CorporationProtocolEntity> queryProtocol(Function<QCorporationProtocolEntity, Predicate> condition) {
        QCorporationProtocolEntity t = QCorporationProtocolEntity.corporationProtocolEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<CorporationProtocolFiledEntity> queryProtocolField(Function<QCorporationProtocolFiledEntity, Predicate> condition) {
        QCorporationProtocolFiledEntity t = QCorporationProtocolFiledEntity.corporationProtocolFiledEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<CorporationProtocolStepEntity> queryProtocolStep(Function<QCorporationProtocolStepEntity, Predicate> condition) {
        QCorporationProtocolStepEntity t = QCorporationProtocolStepEntity.corporationProtocolStepEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t))
                .orderBy(t.sortby.asc());
    }



}
