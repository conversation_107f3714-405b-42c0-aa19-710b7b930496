package com.olading.operate.labor.util.crypto;

import org.apache.commons.codec.digest.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;

public class SM3Password {

    public static String generate(String password) {
        return Base64.getEncoder().encodeToString(digest(password.getBytes(StandardCharsets.UTF_8)));
    }

    public static boolean validate(String password, String v) {
        byte[] ciphertext = Base64.getDecoder().decode(v);
        return sm3Verify(password.getBytes(StandardCharsets.UTF_8), ciphertext);
    }

    /**
     * SM3加盐哈希算法
     *
     * @param plain 明文
     * @return hash密文
     */
    public static byte[] digest(byte[] plain) {
        SecureRandom random = new SecureRandom();

        byte[] salt = new byte[32];
        random.nextBytes(salt);
        return digest(plain, salt);
    }

    /**
     * SM3加盐哈希算法数据验证
     *
     * @param plain      明文
     * @param ciphertext 密文
     * @return 是否验证一致
     */
    public static boolean sm3Verify(byte[] plain, byte[] ciphertext) {
        byte[] salt = Arrays.copyOf(ciphertext, 32);
        byte[] hash = Arrays.copyOfRange(ciphertext, 32, ciphertext.length);
        byte[] hash2 = digest(plain, ciphertext);

        return Arrays.equals(hash, hash2);
    }

    public static byte[] digest(byte[] plain, byte[] salt) {
        byte[] hash = DigestUtils.getSha512Digest().digest(plain);
        byte[] ciphertext = Arrays.copyOf(salt, salt.length + hash.length);
        System.arraycopy(hash, 0, ciphertext, salt.length, hash.length);
        return ciphertext;
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {

        String s = "hello world";

        String b64 = generate(s);
        System.out.println(b64);

        boolean b = validate(s, b64);

        long t1 = System.currentTimeMillis();
        for (int i = 0; i < 1000000; i++) {
            validate(s, b64);
        }
        System.out.println(System.currentTimeMillis() - t1);
    }
}
