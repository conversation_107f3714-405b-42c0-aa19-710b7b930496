package com.olading.operate.labor.domain.share.protocol;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.Instant;

@Getter
@Setter
@Comment("模板适用范围表")
@Entity
@Table(name = "t_corporation_protocol_temp_range")
public class CorporationProtocolTempRangeEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @Comment("租户id")
    @Column(name = "tenant_id")
    private Integer tenantId;

    @Comment("平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("模板id")
    @Column(name = "template_id")
    private Long templateId;

    @Comment("模板适用范围类型")
    @Column(name = "range_type")
    private Integer rangeType;

    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id")
    private Long supplierCorporationId;

    @Comment("排序")
    @Column(name = "sorted_by")
    private Integer sortedBy;

    @Comment("version")
    @Column(name = "version")
    private Integer version;

    @Comment("创建时间")
    @Column(name = "create_time")
    private Instant createTime;

    @Comment("更新时间")
    @Column(name = "modify_time")
    private Instant modifyTime;

}