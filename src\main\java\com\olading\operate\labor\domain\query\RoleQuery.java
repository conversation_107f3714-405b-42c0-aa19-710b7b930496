package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.authority.QRoleEntity;
import com.olading.operate.labor.domain.share.authority.RoleData;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;

public class RoleQuery implements EntityQuery<QueryFilter<RoleQuery.Filters>, RoleData> {

    private final QRoleEntity t1 = QRoleEntity.roleEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();
        if (filters.getFilters().getTenant() != null) {
            criteria.and(t1.tenantId.eq(filters.getFilters().getTenant().toTenantId()));
        }
        if (filters.getFilters().getName() != null) {
            criteria.and(t1.name.like(JpaUtils.fullLike(filters.getFilters().getName())));
        }

        query.select(t1)
                .from(t1);

        query.where(criteria);
    }

    @Override
    public RoleData transform(Object v) {
        RoleEntity role = (RoleEntity) v;
        return new RoleData(role);
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    public static class Filters {

        private TenantInfo tenant;

        private String name;
    }

}
