package com.olading.operate.labor.app.web.biz.protocol.vo;

import com.olading.operate.labor.app.web.biz.protocol.TemplateController;
import com.olading.operate.labor.domain.share.signing.common.Control;
import com.olading.operate.labor.domain.share.signing.response.QueryTemplateResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Schema(description="查看合同模板")
public class TemplateVo {

    @Schema(description = "模板id")
    private Long id;

    @Schema(description = "模板名")
    private String name;

    @Schema(description = "模板类型")
    private String type;

    @Schema(description = "使用范围（公司id列表）")
    List<TemplateController.Corporation> subjects;

    @Schema(description = "模板步骤")
    private List<TemplateStep> steps;

    @Schema(description = "合同文件")
    private List<String> archives;

    public static TemplateVo from(TemplateController.GetTemplateDetailVo template,
                                  QueryTemplateResponse cloudTemplate) {
        TemplateVo response = new TemplateVo();
        response.id = template.getTempId();
        response.name = template.getTemplateName();
        response.type = template.getTemplateType();
//        Set<Long> subjectIds = new HashSet<>(template.getTaxSubId());
//        response.subjects = subjects.stream()
//                .filter(subject -> subjectIds.contains(subject.getContractSubId())).collect(Collectors.toList());
        response.archives = template.getArchives();
        Map<String, List<Control>> nameToControls =
                Optional.ofNullable(cloudTemplate.getControls()).orElse(Collections.emptyList())
                        .stream().collect(Collectors.groupingBy(Control::getName));
//        response.steps = Optional.ofNullable(template.getSteps()).orElse(Collections.emptyList()).stream()
//                .map(tsp -> TemplateStep.copySignatory(tsp, nameToControls))
//                .collect(Collectors.toList());
        return response;
    }



    @Data
    @Schema(description="模板步骤")
    static class TemplateStep {

        @Schema(description = "步骤ID")
        private Long stepId;

        @Schema(description = "操作类型")
        private String operate;

        @Schema(description = "步骤名")
        private String stepName;

        @Schema(description = "步骤排序")
        private Long sortby;

        @Schema(description = "操作员工id")
        private Long compEmpId;

        @Schema(description = "操作员工姓名")
        private String compEmpName;

        @Schema(description = "用工记录id")
        private Long empRecordId;

        @Schema(description = "域列表")
        private List<TemplateFiled> filedList;

        @Schema(description = "控件")
        private List<CommitTemplateVo.ControlParam> controls;

        public static TemplateStep copySignatory(
                TemplateStep templateStep, Map<String, List<Control>> nameToControls) {
            TemplateStep signatory = new TemplateStep();
            signatory.filedList = Optional.ofNullable(templateStep.getFiledList()).orElse(Collections.emptyList());
            signatory.controls = new ArrayList<>();
            /*
                云签控件map(nameToControls)
                1 因为每个签署方下肯定有一个签章
                2 然后再去遍历签署方 signatory 的域列表（包括签章、日期、域），这时只需要处理域的控件 即 field.name in nameToControls
             */
            // 1
            List<Control> nonFieldControls = nameToControls.getOrDefault(templateStep.getStepName(), Collections.emptyList());
            signatory.controls.addAll(nonFieldControls.stream()
                    .map(CommitTemplateVo.ControlParam::from).toList());
            for (TemplateFiled filed : signatory.filedList) {
                if (!nameToControls.containsKey(filed.getFieldName())) continue;
                List<Control> controls = nameToControls.get(filed.getFieldName());
                signatory.controls.addAll(
                        controls.stream().map(CommitTemplateVo.ControlParam::from).toList());
            }
            return signatory;
        }

    }



}
