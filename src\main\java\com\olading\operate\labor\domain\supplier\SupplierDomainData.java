package com.olading.operate.labor.domain.supplier;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description:
 * @author: zhuangweifeng
 * @time: 2025/7/4 10:03
 */
@Data
@Getter
@Setter
public class SupplierDomainData {

    @Schema(description = "品牌域名")
    private String domainName;
    @Schema(description = "slogan")
    private String slogan;
    @Schema(description = "logo地址")
    private String logoUrl;
    @Schema(description = "品牌名称")
    private String brandName;
    @Schema(description = "h5域名")
    private String h5DomainName;
    @Schema(description = "h5_logo地址")
    private String h5LogoUrl;
    @Schema(description = "服务商id")
    private Long supplierId;
    @Schema(description = "h5_服务协议")
    private List<H5ServiceAgreement> h5ServiceAgreement;

    public SupplierDomainData(SupplierDomainConfigEntity  entity) {
        this.domainName = entity.getDomainName();
        this.slogan = entity.getSlogan();
        this.logoUrl = entity.getLogoUrl();
        this.brandName = entity.getBrandName();
        this.h5DomainName = entity.getH5DomainName();
        this.h5LogoUrl = entity.getH5LogoUrl();
        this.h5ServiceAgreement = entity.getH5ServiceAgreement() != null ?
                JSONUtil.toList(entity.getH5ServiceAgreement(), H5ServiceAgreement.class):null;
        this.supplierId = entity.getSupplierId();
    }
    public SupplierDomainData() {
    }
}
