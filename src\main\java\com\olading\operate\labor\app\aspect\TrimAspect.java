package com.olading.operate.labor.app.aspect;

import com.olading.boot.util.beans.BeanValueProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 自动对Controller的入参做trim
 */
@Slf4j
@RequiredArgsConstructor
@Component
@Aspect
public class TrimAspect {

    private static final String PKG = "com.olading.operate.labor";

    private final BeanValueProcessor processor = BeanValueProcessor.builder().scanPackages(PKG).trim().build();

    @Pointcut("within(com.olading.operate.labor.app.web.biz.BusinessController+)")
    public void businessController() {
    }

    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public void postMapping() {
    }

    @Before("businessController() && postMapping()")
    public void execute(JoinPoint joinPoint) {

        for (Object arg : joinPoint.getArgs()) {
            if (arg != null && arg.getClass().getPackage().getName().startsWith(PKG)) {
                processor.process(arg);
            }
        }

    }

}
