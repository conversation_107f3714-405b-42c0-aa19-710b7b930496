package com.olading.operate.labor.domain.share.seq;

import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class SequenceRepository {

    private final EntityManager em;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void declare(String name, long start) {
        var entity = em.find(SequenceEntity.class, name);
        if (entity == null) {
            entity = new SequenceEntity(name, start);
            em.persist(entity);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Pair<Long, Long> request(String name, long step) {
        SequenceEntity entity = em.find(SequenceEntity.class, name, LockModeType.PESSIMISTIC_WRITE);
        if (entity == null) {
            throw new RuntimeException();
        }
        long start = entity.getNextValue();
        long end = start + step;
        entity.increase(step);
        em.merge(entity);
        return Pair.of(start, end);
    }
}
