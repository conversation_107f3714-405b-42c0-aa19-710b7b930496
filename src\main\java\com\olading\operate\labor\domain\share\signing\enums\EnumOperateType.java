package com.olading.operate.labor.domain.share.signing.enums;

/**
 * 操作类型
 */
public enum EnumOperateType {

    /**
     * 发起
     */
    START("发起人"),

    /**
     * 审核
     */
    AUDIT("审核人"),

    /**
     * 签署
     */
    SIGN("个人签署方"),

    /**
     * 加盖公章
     */
    SEAL("企业签署方"),

    /**
     *抄送
     */
    CARBON_COPY("抄送人"),

    /**
     *结束
     */
    END("结束人");

    public String operateGuy;

    EnumOperateType(String operateGuy) {
        this.operateGuy = operateGuy;
    }
}