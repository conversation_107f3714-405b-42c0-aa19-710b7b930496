package com.olading.operate.labor.util.validation.validator;


import com.lanmaoly.util.lang.StringUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;


import java.lang.annotation.Annotation;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
abstract public class BaseValidator<A extends Annotation> implements ConstraintValidator<A, Object>,
        Validator {
    public static final String ERRORS_SPLIT = "；\n";

    private boolean required = false;
    private String name = "";

    @Override
    public boolean isRequired() {
        return required;
    }

    @Override
    public void setRequired(boolean required) {
        this.required = required;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 表单验证使用(注解方式）
     * @param o
     * @param constraintValidatorContext
     * @return
     */
    @Override
    public boolean isValid(Object o, ConstraintValidatorContext constraintValidatorContext) {
        try {
            validate(o);
        } catch (ValidationException e) {
            if (null != constraintValidatorContext) {
                e.setName(name);
                String message = constraintValidatorContext.getDefaultConstraintMessageTemplate();
                if (StringUtils.isBlank(message)) {
                    // 优先使用注解里面自定义的文本
                    message = e.getMessage();
                }

                resetMessage(constraintValidatorContext, message);
            }

            return false;
        }

        return true;
    }

    /**
     * 自定义验证使用
     * @param o
     * @return
     * @throws ValidationException
     */
    @Override
    public boolean validate(Object o) throws ValidationException{
        boolean isBlankVal = StringUtils.isBlank(StringUtils.toNoNullString(o));
        if (isRequired() && isBlankVal) {
            // 必填校验
            throw new ValidationException(name, String.format("请填写%s", ValidationException.PLACEHOLDER));
        }

        try {
            if (!isBlankVal && !constraintCheck(o)) {
                // 非空数据校验
                return false;
            }
        } catch (ValidationException e) {
            throw e;
        }

        return true;
    }

    /**
     * 表单验证用，走validation验证框架
     * @param o
     * @return
     */
    protected boolean constraintCheck(Object o) {
        return true;
    }

    protected void resetMessage(ConstraintValidatorContext constraintValidatorContext, String errmsg) {
        if (null != constraintValidatorContext) {
            //禁用默认的message的值
            constraintValidatorContext.disableDefaultConstraintViolation();
            //重新添加错误提示语句
            constraintValidatorContext
                    .buildConstraintViolationWithTemplate(errmsg)
                    .addConstraintViolation();
        }
    }
}
