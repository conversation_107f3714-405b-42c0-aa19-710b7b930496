package com.olading.operate.labor.domain.share.submission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InfoSubmissionLaborVo {
    @Schema(description = "人员信息报送记录ID")
    private Long id;

    @Schema(description = "灵工平台ID")
    private Long supplierId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "报送状态")
    private String reportStatus;

    @Schema(description = "是否已取得登记证照")
    private String registrationLicenseObtained;

    @Schema(description = "名称（姓名）")
    private String name;

    @Schema(description = "统一社会信用代码（纳税人识别号）")
    private String unifiedSocialCreditCode;

    @Schema(description = "专业服务机构标识")
    private String professionalServiceAgencyFlag;

    @Schema(description = "姓名")
    private String laborName;

    @Schema(description = "证件类型")
    private String certificateType;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "国家或地区")
    private String householdCity;

    @Schema(description = "是否存在免于报送收入信息情形")
    private String incomeReportingExemptionFlag;

    @Schema(description = "免报类型")
    private String exemptionType;

    @Schema(description = "地址")
    private String householdAddress;

    @Schema(description = "店铺（用户）名称")
    private String storeName;

    @Schema(description = "店铺（用户）唯一标识码")
    private String storeUniqueCode;

    @Schema(description = "网址链接（选填）")
    private String websiteUrl;

    @Schema(description = "开户银行/非银行支付机构")
    private String cardBank;

    @Schema(description = "账户名称")
    private String accountName;

    @Schema(description = "银行账号/支付账户")
    private String bankCard;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "经营开始时间")
    private String startDate;

    @Schema(description = "经营结束时间")
    private String endDate;

    @Schema(description = "信息状态标识")
    private String infoStatusFlag;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;
}
