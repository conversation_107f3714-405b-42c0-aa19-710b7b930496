package com.olading.operate.labor.domain.bill;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单分类统计表（第二层）- 各费用类型统计
 */
@Entity
@Table(name = "t_bill_category")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("账单分类统计表")
public class BillCategoryEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Comment("账单主表ID")
    @Column(name = "bill_master_id", nullable = false)
    private Long billMasterId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Comment("费用类型")
    @Column(name = "fee_type", nullable = false, length = 20)
    private BillFeeType feeType;

    @NotNull
    @Comment("账单月份")
    @Column(name = "bill_month", nullable = false)
    private LocalDate billMonth;

    @NotNull
    @Comment("费用总额")
    @Column(name = "total_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal totalAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("明细条数")
    @Column(name = "detail_count", nullable = false)
    private Integer detailCount = 0;

    @NotNull
    @Comment("涉及人数")
    @Column(name = "person_count", nullable = false)
    private Integer personCount = 0;

    @Comment("计算规则说明")
    @Column(name = "calculation_rule", length = 200)
    private String calculationRule;

    @Comment("备注")
    @Column(name = "remark", length = 500)
    private String remark;

    public BillCategoryEntity(TenantInfo info){
        setTenant( info);
    }

    protected BillCategoryEntity() {
    }
}