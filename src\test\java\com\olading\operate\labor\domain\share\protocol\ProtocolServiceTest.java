package com.olading.operate.labor.domain.share.protocol;

import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.app.provider.OladingEnvironmentProvider;
import com.olading.operate.labor.domain.service.ProtocolService;
import com.olading.operate.labor.domain.service.ProtocolTemplateService;
import com.olading.operate.labor.domain.share.signing.CloudSigningService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Arrays;

@Slf4j
public class ProtocolServiceTest extends BaseTest {
    @Autowired
    ProtocolTemplateService protocolTemplateService;
    @Autowired
    OladingEnvironmentProvider oladingEnvironmentProvider;
    @Autowired
    private ProtocolService protocolService;

    @Autowired
    private ProtocolRepository protocolRepository;

    @Autowired
    CloudSigningService cloudSigningService;
    @Autowired
    ProtocolManager protocolManager;


    @Test
    public void testQueryTemplateStatus() {
        protocolTemplateService.queryTemplateStatus();
    }

    @Test
    public void getFile() {
        String fileId = "13e207ee1584446c9b350813d5ceea12";
        String filePath = "d://1.pdf";

        try (OutputStream os = new BufferedOutputStream(new FileOutputStream(filePath))) {
            oladingEnvironmentProvider.readFile(fileId, os);
            os.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void signContract2CloudSignTest() {
//        CorporationProtocolEntity protocol = protocolRepository.queryProtocolById(14L);
        protocolService.createSigningFile();
    }

    @Test
    public void signContract2ArchiveTest() {
        CorporationProtocolEntity protocol = protocolRepository.queryProtocolById(14L);
        protocolService.signContract2Archive(Arrays.asList(protocol));
    }

    @Test
    public void signTest() {
        protocolService.sign(14L, "", null, null);
    }

    @Test
    public void querySignStatusTest() {
//        protocolService.querySignStatus(18L);
        protocolService.querySignStepStatus();
    }
    @Test
    public void downloadSigningFileAndSignTest() {
//        protocolService.querySignStatus(18L);
        protocolService.downloadSigningFileAndSign();
    }

    @Test
    public void nextStepTest() {
        protocolManager.nextStep(protocolRepository.queryProtocolById(14L),  "");
    }
}
