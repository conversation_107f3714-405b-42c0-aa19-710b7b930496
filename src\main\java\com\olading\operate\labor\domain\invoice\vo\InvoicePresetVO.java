package com.olading.operate.labor.domain.invoice.vo;

import com.olading.operate.labor.domain.invoice.InvoiceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "开票预设信息")
public class InvoicePresetVO {
    
    @Schema(description = "客户ID")
    private Long customerId;
    
    @Schema(description = "客户名称")
    private String customerName;
    
    @Schema(description = "合同ID")
    private Long contractId;
    
    @Schema(description = "合同名称")
    private String contractName;
    
    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;
    
    @Schema(description = "作业主体名称")
    private String supplierCorporationName;
    
    @Schema(description = "可选发票类型列表")
    private List<InvoiceTypeOption> invoiceTypes;
    
    @Schema(description = "发票信息")
    private InvoiceInfoVO invoiceInfo;
    
    @Schema(description = "可开票明细列表")
    private List<AvailableBillVO> availableBills;
    
    @Data
    @Schema(description = "发票类型选项")
    public static class InvoiceTypeOption {
        @Schema(description = "发票类型")
        private InvoiceType type;
        
        @Schema(description = "发票类型描述")
        private String description;
        
        public InvoiceTypeOption(InvoiceType type) {
            this.type = type;
            this.description = type.getDescription();
        }
    }
}