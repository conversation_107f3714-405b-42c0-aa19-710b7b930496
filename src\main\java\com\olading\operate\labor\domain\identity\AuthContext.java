package com.olading.operate.labor.domain.identity;

/**
 * 认证上下文信息
 */
public class AuthContext {
    
    // 基础信息（必填）
    private String tenantId;        // 租户ID
    private String authScene;       // 认证场景
    
    // 用户信息（协议签约时必填，代发时可空）
    private Long userId;            // 用户ID
    private Long supplierId;        // 供应商ID
    private Long corporationId;     // 作业主体ID
    
    // 业务信息（可选）
    private String businessId;      // 业务ID（合同ID、批次ID等）
    private String businessType;    // 业务类型
    
    // 文件信息（可选）
    private String personalIdFileId;     // 个人证件照面文件ID
    private String nationalEmblemFileId; // 国徽面文件ID
    private String faceVideoFileId;      // 人脸视频文件ID
    
    // 操作人信息（可选）
    private Long operatorId;        // 操作人ID
    private String operatorName;    // 操作人姓名

    // 私有构造函数，强制使用Builder模式
    private AuthContext() {}

    // Builder模式
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private AuthContext context = new AuthContext();

        public Builder tenantId(String tenantId) {
            context.tenantId = tenantId;
            return this;
        }

        public Builder authScene(String authScene) {
            context.authScene = authScene;
            return this;
        }

        public Builder userId(Long userId) {
            context.userId = userId;
            return this;
        }

        public Builder supplierId(Long supplierId) {
            context.supplierId = supplierId;
            return this;
        }

        public Builder corporationId(Long corporationId) {
            context.corporationId = corporationId;
            return this;
        }

        public Builder businessId(String businessId) {
            context.businessId = businessId;
            return this;
        }

        public Builder businessType(String businessType) {
            context.businessType = businessType;
            return this;
        }

        public Builder personalIdFileId(String personalIdFileId) {
            context.personalIdFileId = personalIdFileId;
            return this;
        }

        public Builder nationalEmblemFileId(String nationalEmblemFileId) {
            context.nationalEmblemFileId = nationalEmblemFileId;
            return this;
        }

        public Builder faceVideoFileId(String faceVideoFileId) {
            context.faceVideoFileId = faceVideoFileId;
            return this;
        }

        public Builder operatorId(Long operatorId) {
            context.operatorId = operatorId;
            return this;
        }

        public Builder operatorName(String operatorName) {
            context.operatorName = operatorName;
            return this;
        }

        public AuthContext build() {
            // 参数验证
            if (context.authScene == null || context.authScene.trim().isEmpty()) {
                throw new IllegalArgumentException("认证场景不能为空");
            }
            return context;
        }
    }

    // Getters
    public String getTenantId() {
        return tenantId;
    }

    public String getAuthScene() {
        return authScene;
    }

    public Long getUserId() {
        return userId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public Long getCorporationId() {
        return corporationId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public String getPersonalIdFileId() {
        return personalIdFileId;
    }

    public String getNationalEmblemFileId() {
        return nationalEmblemFileId;
    }

    public String getFaceVideoFileId() {
        return faceVideoFileId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    // Setters for optional fields
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public void setPersonalIdFileId(String personalIdFileId) {
        this.personalIdFileId = personalIdFileId;
    }

    public void setNationalEmblemFileId(String nationalEmblemFileId) {
        this.nationalEmblemFileId = nationalEmblemFileId;
    }

    public void setFaceVideoFileId(String faceVideoFileId) {
        this.faceVideoFileId = faceVideoFileId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }
}