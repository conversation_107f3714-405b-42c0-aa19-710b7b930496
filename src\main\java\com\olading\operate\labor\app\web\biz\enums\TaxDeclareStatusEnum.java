package com.olading.operate.labor.app.web.biz.enums;

public enum TaxDeclareStatusEnum {

    NOT_DECLARED("未申报"),
    DECLARED("已申报");

    private final String name;

    TaxDeclareStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameByKey(String key) {
        for (TaxDeclareStatusEnum e : values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getName();
            }
        }
        return "";
    }
}
