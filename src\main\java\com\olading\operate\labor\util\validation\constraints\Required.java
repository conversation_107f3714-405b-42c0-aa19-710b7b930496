package com.olading.operate.labor.util.validation.constraints;


import com.olading.operate.labor.util.excel.ExcelConstraint;
import com.olading.operate.labor.util.textfilter.RemoveEnterBlankFilter;
import com.olading.operate.labor.util.validation.validator.RequiredValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;


import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 必填项
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        RequiredValidator.class
})
@ExcelConstraint (
        width = 14,
        filters = {
                RemoveEnterBlankFilter.class
        }
)
public @interface Required {
    String message() default "";

    boolean required() default true;

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
