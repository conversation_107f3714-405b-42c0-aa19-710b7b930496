package com.olading.operate.labor.util;

import org.apache.commons.io.IOUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

public class MacUtils {

    public static final Charset CHARSET = StandardCharsets.UTF_8;


    public static String fileSign(String algorithm, String key, InputStream inputStream) {
        byte[] sign = new byte[0];
        try {
            sign = sign(algorithm, key.getBytes(CHARSET), IOUtils.toByteArray(inputStream));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString(sign);
    }


    public static String sign(String algorithm, String key, String data) {
        byte[] sign = sign(algorithm, key.getBytes(CHARSET), data.getBytes(CHARSET));
        return Base64.getEncoder().encodeToString(sign);
    }

    public static byte[] sign(String algorithm, byte[] key, byte[] data) {
        try {
            SecretKey secretKey = new SecretKeySpec(key, algorithm);
            Mac mac = Mac.getInstance(algorithm);
            mac.init(secretKey);
            return mac.doFinal(data);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new IllegalStateException("签名出现严重问题", e);
        }
    }

    public static boolean verify(String algorithm, String key, String data, String sign) {
        return verify(algorithm, key.getBytes(CHARSET), data.getBytes(CHARSET), Base64.getDecoder().decode(sign));
    }

    public static boolean verify(String algorithm, byte[] key, byte[] data, byte[] sign) {
        return Arrays.equals(sign(algorithm, key, data), sign);
    }
}
