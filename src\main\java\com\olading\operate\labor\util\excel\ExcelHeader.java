package com.olading.operate.labor.util.excel;

import com.lanmaoly.util.lang.StringUtils;
import com.lanmaoly.util.lang.exception.ValidationException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/3/3 14:21
 */
public class ExcelHeader {

    /**
     * 表头列
     */
    private List<ExcelColumnEntity> columns = new ArrayList<>();

    /**
     * 表头名与Column名字的映射关系<excelTitle, columnName>
     */
    private Map<String, String> mapping = new HashMap<>();

    public List<ExcelColumnEntity> getColumns() {
        return columns;
    }

    public ExcelHeader()
    {

    }

    public ExcelHeader(List<ExcelColumnEntity> columns)
    {
        this.columns = columns;
    }

    public ExcelColumnEntity getColumnByName(String columnName) {
        for (ExcelColumnEntity column: columns) {
            if (null != columnName && columnName.equals(column.getName())) {
                return column;
            }
        }

        return null;
    }

    public ExcelColumnEntity getColumnByTitle(String columnTitle) {
        for (ExcelColumnEntity column: columns) {
            if (null != columnTitle && columnTitle.equals(column.getExcelTitle())) {
                return column;
            }
        }

        return null;
    }

    /**
     * 重排Column的列号
     */
    public void resortColumnIndex() {
        int i = 0;
        for (ExcelColumnEntity column: columns) {
            Integer index = column.getColIndex();
            if (null != index && i < index){
                i = index;
            }
        }

        // 未定义colIndex的往后排
        for (ExcelColumnEntity column: columns) {
            Integer index = column.getColIndex();
            if (null == column.getColIndex()) {
                i++;
                column.setColIndex(i);
            }
        }
    }

    /**
     * @param column
     * @return
     */
    public ExcelHeader put(ExcelColumnEntity column) {
        columns.add(column);
        if (!StringUtils.isBlank(column.getExcelTitle())) {
            mapping.put(column.getExcelTitle(), column.getName());
        }

        return this;
    }

    /**
     * 添加扩展字段（一般用于导出Excel数据用）
     * @param extraTitles
     * @return
     */
    public ExcelHeader putExtraDataColumn(Set<String> extraTitles) {
        if (null == extraTitles) {
            return this;
        }

        for (String title: extraTitles) {
            if (mapping.containsKey(title)) {
                continue;
            }

            ExcelColumnEntity column = new ExcelColumnEntity();
            column.setExcelTitle(title);
            put(column);
        }

        return this;
    }

    /**
     * 获取绑定关系
     * @return
     */
    public Map<String, String> getNameTitleMapping() {
        Map<String, String> newMapping = new HashMap<>();
        for (Map.Entry<String, String> entry: mapping.entrySet()) {
            if (!StringUtils.isBlank(entry.getValue())) {
                newMapping.put(entry.getValue(), entry.getKey());
            }
        }

        return newMapping;
    }

    public Map<String, String> getTitleNameMapping() {
        return mapping;
    }

    public Set<String> getTitleSet() {
        return mapping.keySet();
    }

    public boolean isEmpty() {
        return null == columns || columns.isEmpty();
    }

    public List<String> getTitleListByColumnIndex()
    {
        List<String> list = new ArrayList<>();
        for (ExcelColumnEntity column: columns) {
            list.add(column.getExcelTitle());
        }

        return list;
    }

    /**
     * 表头校对
     * @return
     * @throws ValidationException
     */
    public boolean verify() throws ExcelIOException {
        if (null == columns || columns.isEmpty()) {
            throw new ExcelIOException("未读到正确的表头信息，请检查文件格式是否正确");
        }

        Set<String> repeatTitles = new HashSet<>();
        Map<String, Set> repeatMapping = new HashMap();

        Map<String, String> existedNameMap = new HashMap<>();
        List<String> existedTitles = new ArrayList<>();
        for (ExcelColumnEntity column: columns) {
            String name = column.getName();
            String title = column.getExcelTitle();

            if (null != title && existedTitles.contains(title)) {
                repeatTitles.add(title);
            } else {
                existedTitles.add(title);
            }

            if (null != title && null != name && existedNameMap.containsKey(name)) {
                Set<String> set = repeatMapping.containsKey(name) ? repeatMapping.get(name) : new HashSet<>();
                if (!set.contains(title)) {
                    set.add(existedNameMap.get(name));
                    set.add(title);
                }

                repeatMapping.put(name, set);
            } else {
                existedNameMap.put(name, title);
            }
        }

        List<String> errors = new ArrayList<>();
        if (!repeatTitles.isEmpty()) {
            errors.add(String.format("数据列'%s'重复", StringUtils.join(repeatTitles, "、")));
        }

        if (!repeatMapping.isEmpty()) {
            for (Map.Entry<String, Set> entry: repeatMapping.entrySet()) {
                errors.add(String.format("表头'%s'不能同时匹配数据列'%s'", StringUtils.join(entry.getValue(), "、"),
                        entry.getKey()));
            }
        }

        if (!errors.isEmpty()) {
            throw new ExcelIOException(StringUtils.join(errors, "\n"));
        }

        return true;
    }

}
