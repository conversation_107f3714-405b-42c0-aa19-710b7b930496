package com.olading.operate.labor.domain.share.signing.response;


import com.olading.operate.labor.domain.share.signing.enums.IdType;
import com.olading.operate.labor.domain.share.signing.enums.SignStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "签名查询响应参数")
public class QuerySignResponse {

    @Schema(description = "签约流水号", required = true)
    private String flowNo;
    @Schema(description = "文件ID", required = true)
    private String fileId;
    @Schema(description = "签名步骤", required = true)
    private String stepName;
    @Schema(description = "签名人名称", required = true)
    private String signer;
    @Schema(description = "签署人唯一标识符", required = true)
    private String idNo;
    @Schema(description = "证件类型", required = true)
    private IdType idType;
    @Schema(description = "签名状态", required = true)
    private SignStatus status;
}
