package com.olading.operate.labor.util.crypto;

import java.security.SecureRandom;
import java.util.Arrays;

public class Salt2Digest implements Digest {

    private final Digest digest;
    private final int saltSize;

    public Salt2Digest(Digest digest, int saltSize) {
        this.digest = digest;
        this.saltSize = saltSize;
    }

    @Override
    public byte[] digest(byte[] plaintext) {
        byte[] salt = createSalt();
        return concat(salt, digest.digest(concat(plaintext, salt)));
    }

    @Override
    public boolean verify(byte[] plaintext, byte[] signature) {
        byte[] salt = Arrays.copyOfRange(signature, 0, saltSize);
        byte[] originSignature = Arrays.copyOfRange(signature, saltSize, signature.length);

        return digest.verify(concat(plaintext, salt), originSignature);
    }

    private byte[] createSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[saltSize];
        random.nextBytes(salt);
        return salt;
    }

    private byte[] concat(byte[] a, byte[] b) {
        byte[] t = Arrays.copyOf(a, a.length + b.length);
        System.arraycopy(b, 0, t, a.length, b.length);
        return t;
    }
}
