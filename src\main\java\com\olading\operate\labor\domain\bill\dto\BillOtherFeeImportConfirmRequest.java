package com.olading.operate.labor.domain.bill.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 其他费用导入确认请求
 */
@Data
public class BillOtherFeeImportConfirmRequest {
    
    /**
     * 导入数据
     */
    @Valid
    @NotEmpty(message = "导入数据不能为空")
    private List<BillOtherFeeImportRow> importData;
    
    /**
     * 备注
     */
    private String remark;
}