package com.olading.operate.labor.domain.identity;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;

/**
 * 要素鉴权记录实体
 * 任务 2.1: 创建要素鉴权记录实体类
 */
@Entity
@Table(name = "t_identity_factor_auth_record")
@Comment("要素鉴权记录表")
@Data
public class IdentityFactorAuthRecordEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    @Comment("认证记录编号")
    @Column(name = "record_no", length = 64, nullable = false)
    private String recordNo;

    @Comment("用户ID（可空，兼容非登录场景）")
    @Column(name = "user_id")
    private Long userId;

    @Comment("供应商ID")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Comment("作业主体ID")
    @Column(name = "corporation_id")
    private Long corporationId;

    @Comment("真实姓名")
    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @Comment("身份证号")
    @Column(name = "id_card", length = 18, nullable = false)
    private String idCard;

    @Comment("银行卡号（三要素时必填）")
    @Column(name = "bank_card_number", length = 30)
    private String bankCardNumber;

    @Comment("手机号（运营商三要素时必填）")
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;

    @Comment("鉴权类型：TWO_FACTOR,THREE_FACTOR,OPERATOR_THREE_FACTOR")
    @Column(name = "auth_type", length = 30, nullable = false)
    private String authType;

    @Comment("认证场景：CONTRACT_SIGNING,PAYMENT_DISBURSEMENT,REAL_NAME_AUTH")
    @Column(name = "auth_scene", length = 30, nullable = false)
    private String authScene;

    @Comment("认证状态：SUCCESS,FAILED")
    @Column(name = "auth_status", length = 20, nullable = false)
    private String authStatus;

    @Comment("鉴权结果：CONSISTENT,INCONSISTENT,NO_RECORD")
    @Column(name = "auth_result", length = 20)
    private String authResult;

    @Comment("错误码")
    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Comment("错误信息")
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    public IdentityFactorAuthRecordEntity(TenantInfo tenantInfo){
        setTenant(tenantInfo);
    }

    public IdentityFactorAuthRecordEntity(){}

}