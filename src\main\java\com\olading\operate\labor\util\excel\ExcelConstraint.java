package com.olading.operate.labor.util.excel;

import com.olading.operate.labor.util.textfilter.TextFilter;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2022/4/7 11:41
 */
@Documented
@Target({ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelConstraint {
    /**
     * 导出表格列的宽度
     * @return
     */
    int width() default -1;


    /**
     * 自定义数据过滤器（校验前执行）
     */
    Class<? extends TextFilter>[] filters() default { };

    /**
     * 自定义数据格式化（校验后执行）
     */
    Class<? extends TextFilter>[] formatters() default { };

}
