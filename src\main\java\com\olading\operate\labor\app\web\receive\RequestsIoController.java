package com.olading.operate.labor.app.web.receive;

import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * http请求工具
 */
@RestController
@RequestMapping("/api/request-io")
@RequiredArgsConstructor
@Slf4j
public class RequestsIoController {

    private final List<RequestInfo> queue = new ArrayList<>();

    @RequestMapping(value = "receive/**")
    public String receive(HttpServletRequest request) throws IOException {

        RequestInfo info = new RequestInfo();
        info.setReceivedTime(LocalDateTime.now());
        info.setRemoteAddr(request.getRemoteAddr());
        info.setRequestUri(request.getRequestURI());
        info.setMethod(request.getMethod());
        info.setBody(IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8));

        info.setHeaders(new ArrayList<>());
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement();
            info.getHeaders().add(name + ": " + request.getHeader(name));
        }

        synchronized (queue) {
            queue.add(info);
            if (queue.size() > 100) {
                queue.remove(0);
            }
        }

        return "SUCCESS";
    }

    @PostMapping(value = "print")
    public String print() {
        List<RequestInfo> list = null;
        synchronized (queue) {
            list = new ArrayList<>(queue);
        }

        StringBuilder sb = new StringBuilder();
        for (RequestInfo info : list) {
            sb.append("----------------REQUEST----------------\r\n");
            sb.append(info.print());
        }
        return sb.toString();
    }

    @Data
    public static class RequestInfo {
        private LocalDateTime receivedTime;
        private String remoteAddr;
        private String requestUri;
        private String method;
        private List<String> headers;
        private String body;

        public String print() {
            StringBuilder sb = new StringBuilder();


            sb.append("From: ").append(remoteAddr).append("\r\n");
            sb.append("Time: ").append(receivedTime).append("\r\n");
            sb.append("Method: ").append(method).append("\r\n");
            sb.append("Uri: ").append(requestUri).append("\r\n");

            sb.append("\r\n[Headers]\r\n");
            for (String header : headers) {
                sb.append(header).append("\r\n");
            }

            sb.append("\r\n[Body]\r\n");
            sb.append(body).append("\r\n");
            return sb.toString();
        }
    }
}
