package com.olading.operate.labor.domain.supplier;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("品牌信息配置表")
@Entity
@Table(name = "t_supplier_domain_config")
public class SupplierDomainConfigEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("服务运营方编号")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @Size(max = 60)
    @Comment("二级域名")
    @Column(name = "domain_name", length = 60)
    private String domainName;

    @Size(max = 20)
    @Comment("slogan")
    @Column(name = "slogan", length = 20)
    private String slogan;

    @Size(max = 255)
    @Comment("logo地址")
    @Column(name = "logo_url")
    private String logoUrl;

    @Size(max = 255)
    @Comment("品牌名称")
    @Column(name = "brand_name")
    private String brandName;

    @Size(max = 60)
    @Comment("h5域名")
    @Column(name = "h5_domain_name", length = 60)
    private String h5DomainName;

    @Size(max = 255)
    @Comment("h5_logo地址")
    @Column(name = "h5_logo_url")
    private String h5LogoUrl;

    @Comment("h5服务协议文件及文件地址,以json方式存储")
    @Lob
    @Column(name = "h5_service_agreement")
    private String h5ServiceAgreement;

}