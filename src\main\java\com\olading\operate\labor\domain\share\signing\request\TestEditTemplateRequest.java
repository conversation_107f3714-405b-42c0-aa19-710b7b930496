package com.olading.operate.labor.domain.share.signing.request;

import com.olading.operate.labor.domain.share.signing.common.ApiControl;
import com.olading.operate.labor.domain.share.signing.common.Field;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/4
 * @description ：
 * @version: 1.0
 */
@Data
public class TestEditTemplateRequest extends BaseRequest<TestEditTemplateRequest> {
    @Schema(description = "模板编号", required = true)
    private Long templateId;
    @Schema(description = "模板域")
    private java.util.List<Field> fields;
    @Schema(description = "模板控件", required = true)
    private java.util.List<ApiControl> controls;
}
