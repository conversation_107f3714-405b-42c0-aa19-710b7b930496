package com.olading.operate.labor.domain.corporation;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("作业主体通道配置信息")
@Entity
@Table(name = "t_corporation_pay_channel")
public class CorporationPayChannelEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("id")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 64)
    @NotNull
    @Comment("通道编码")
    @Column(name = "pay_channel", nullable = false, length = 64)
    private String payChannel;

    @NotNull
    @Comment("是否默认通道")
    @ColumnDefault("b'0'")
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    @NotNull
    @Comment("是否启用")
    @ColumnDefault("b'1'")
    @Column(name = "is_open", nullable = false)
    private Boolean isOpen = false;

    @Comment("通道配置信息")
    @Lob
    @Column(name = "channel_config")
    private String channelConfig;

    public CorporationPayChannelEntity (TenantInfo tenantInfo,Long supplierId,Long corporationId){
        setTenant(tenantInfo);
        setSupplierId(supplierId);
        setSupplierCorporationId(corporationId);
    }

    protected CorporationPayChannelEntity(){}

}