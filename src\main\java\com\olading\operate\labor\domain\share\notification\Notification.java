package com.olading.operate.labor.domain.share.notification;

import lombok.Getter;

import java.util.Collections;
import java.util.Map;

@Getter
public class Notification {

    private final String receiver;
    private final String url;
    private final Map<String, String> form;
    private String id;

    public Notification(String id, String receiver, String url, Map<String, String> form) {
        this.id = id;
        this.receiver = receiver;
        this.url = url;
        this.form = Collections.unmodifiableMap(form);
    }
}
