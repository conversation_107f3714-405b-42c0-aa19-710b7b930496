package com.olading.operate.labor.util.excel;


import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/2/22 16:16
 */
public class ExcelWriterBuilder {
    private ExcelWriter excelWriter;

    public ExcelWriterBuilder(ExcelResult excelResult) {
        this.excelWriter = new ExcelWriter(excelResult);
    }

    public ExcelWriterBuilder titleRow(int titleRow) {
        excelWriter.setTitleRow(titleRow);
        return this;
    }

    public ExcelWriterBuilder headerGroupRow(int headerGroupRow) {
        excelWriter.setHeaderGroupRow(headerGroupRow);
        return this;
    }

    public ExcelWriterBuilder headerRow(int headerRow) {
        excelWriter.setHeaderRow(headerRow);
        return this;
    }

    public ExcelWriterBuilder sheetIndex(Integer sheetIndex) {
        excelWriter.setSheetIndex(sheetIndex);
        return this;
    }

    public ExcelWriterBuilder sheetName(String sheetName) {
        excelWriter.setSheetName(sheetName);
        return this;
    }

    public ExcelWriterBuilder excelType(ExcelType excelType) {
        excelWriter.setExcelType(excelType);
        return this;
    }

    public ExcelWriterBuilder writeOriginData(boolean writeOriginData) {
        excelWriter.setWriteOriginData(writeOriginData);
        return this;
    }

    public ExcelWriterBuilder writerMode(ExcelWriterMode writerMode) {
        excelWriter.setWriterMode(writerMode);
        return this;
    }

    /**
     * 单行文本高度
     * @param rowHeight
     * @return
     */
    public ExcelWriterBuilder rowHeight(double rowHeight) {
        excelWriter.setRowHeight(rowHeight);
        return this;
    }

    public ExcelWriter build() {
        return excelWriter;
    }

    public void download(String fileName, HttpServletResponse response) throws IOException{
        excelWriter.download(fileName, response);
    }

    public InputStream writeToInputStream() throws IOException {
        return excelWriter.writeToInputStream();
    }

    public Workbook writeToWorkbook() {
        return excelWriter.writeToWorkbook();
    }

    public Workbook writeToWorkbook(Workbook workbook) {
        return excelWriter.writeToWorkbook(workbook);
    }
}
