package com.olading.operate.labor.domain.proxy.channel;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@Comment("通道出款订单")
@Entity
@Table(name = "t_channel_remit_order")
public class ChannelRemitOrderEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("工资代发订单id")
    @Column(name = "proxy_order_id", nullable = false)
    private Long proxyOrderId;

    @Size(max = 64)
    @NotNull
    @Comment("通道编码")
    @Column(name = "pay_channel", nullable = false, length = 64)
    private String payChannel;

    @Size(max = 64)
    @NotNull
    @Comment("出款流水号")
    @Column(name = "request_no", nullable = false, length = 64)
    private String requestNo;


    @NotNull
    @Comment("状态")
    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private RemitStatusEnum status;

    @Size(max = 20)
    @NotNull
    @Comment("姓名")
    @Column(name = "name", nullable = false, length = 20)
    private String name;

    @Size(max = 20)
    @NotNull
    @Comment("身份证号")
    @Column(name = "id_card", nullable = false, length = 20)
    private String idCard;

    @Size(max = 11)
    @NotNull
    @Comment("手机号")
    @Column(name = "cellphone", nullable = false, length = 11)
    private String cellphone;

    @Size(max = 24)
    @NotNull
    @Comment("银行卡号")
    @Column(name = "bank_card", nullable = false, length = 24)
    private String bankCard;


    @Comment("出款银行编码")
    @Column(name = "bank_code", length = 20)
    private String bankCode;


    @Comment("出款银行名称")
    @Column(name = "bank_name", length = 20)
    private String bankName;

    @NotNull
    @Comment("付款金额")
    @Column(name = "amount", nullable = false, precision = 38, scale = 2)
    private BigDecimal amount;


    @Comment("错误码")
    @Column(name = "error_code", length = 64)
    private String errorCode;


    @Comment("错误原因")
    @Column(name = "error_reason", length = 100)
    private String errorReason;

    @Comment("完成时间")
    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    @Size(max = 50)
    @Comment("凭证文件id")
    @Column(name = "voucher", length = 50)
    private String voucher;

    @Size(max = 200)
    @Comment("业务备注")
    @Column(name = "remark", length = 200)
    private String remark;

}