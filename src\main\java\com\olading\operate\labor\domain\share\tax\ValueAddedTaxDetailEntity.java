package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

@Getter
@Setter
@Comment("税务增值税申报明细表")
@Entity
@Table(name = "t_value_added_tax_detail", schema = "olading_labor")
public class ValueAddedTaxDetailEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("增值税申报ID")
    @Column(name = "value_added_tax_id", nullable = false)
    private Long valueAddedTaxId;

    @Size(max = 20)
    @NotNull
    @Comment("税款所属期")
    @Column(name = "tax_period", nullable = false, length = 20)
    private String taxPeriod;

    @Size(max = 20)
    @NotNull
    @Comment("申报月份")
    @Column(name = "declare_month", nullable = false, length = 20)
    private String declareMonth;

    @Size(max = 50)
    @Comment("姓名")
    @Column(name = "name", length = 50)
    private String name;

    @Size(max = 20)
    @Comment("证件类型")
    @Column(name = "certificate_type", length = 20)
    private String certificateType;

    @Size(max = 20)
    @Comment("证件号码")
    @Column(name = "id_card", length = 20)
    private String idCard;

    @Size(max = 50)
    @Comment("国家或地区")
    @Column(name = "country_region", length = 50)
    private String countryRegion;

    @Size(max = 200)
    @Comment("地址")
    @Column(name = "address", length = 200)
    private String address;

    @Size(max = 50)
    @Comment("用户名称")
    @Column(name = "user_name", length = 50)
    private String userName;

    @Size(max = 64)
    @Comment("用户唯一标识码")
    @Column(name = "user_unique_code", length = 64)
    private String userUniqueCode;

    @Comment("计税依据 (增值税)")
    @ColumnDefault("0.00")
    @Column(name = "tax_basis", precision = 22, scale = 2)
    private BigDecimal taxBasis;

    @Size(max = 50)
    @Comment("征收品目 (增值税)")
    @Column(name = "tax_item", length = 50)
    private String taxItem;

    @Size(max = 20)
    @Comment("征收率 (增值税)")
    @Column(name = "tax_rate", length = 20)
    private String taxRate;

    @Comment("本期应纳税额 (增值税)")
    @ColumnDefault("0.00")
    @Column(name = "vat_amount", precision = 22, scale = 2)
    private BigDecimal vatAmount;

    @Size(max = 20)
    @Comment("减免性质代码 (增值税)")
    @Column(name = "vat_exemption_code", length = 20)
    private String vatExemptionCode;

    @Comment("减免税额 (增值税)")
    @ColumnDefault("0.00")
    @Column(name = "vat_exemption_amount", precision = 22, scale = 2)
    private BigDecimal vatExemptionAmount;

    @Comment("应补(退)税额 (增值税)")
    @ColumnDefault("0.00")
    @Column(name = "vat_payable", precision = 22, scale = 2)
    private BigDecimal vatPayable;

    @Size(max = 20)
    @Comment("适用税率 (城市维护建设税)")
    @Column(name = "urban_tax_rate", length = 20)
    private String urbanTaxRate;

    @Comment("本期应纳税额 (城市维护建设税)")
    @ColumnDefault("0.00")
    @Column(name = "urban_tax_amount", precision = 22, scale = 2)
    private BigDecimal urbanTaxAmount;

    @Size(max = 20)
    @Comment("减免性质代码 (城市维护建设税)")
    @Column(name = "urban_exemption_code", length = 20)
    private String urbanExemptionCode;

    @Comment("减免税额 (城市维护建设税)")
    @ColumnDefault("0.00")
    @Column(name = "urban_exemption_amount", precision = 22, scale = 2)
    private BigDecimal urbanExemptionAmount;

    @Comment("应补(退)税额 (城市维护建设税)")
    @ColumnDefault("0.00")
    @Column(name = "urban_tax_payable", precision = 22, scale = 2)
    private BigDecimal urbanTaxPayable;

    @Size(max = 20)
    @Comment("适用税率 (教育附加)")
    @Column(name = "edu_tax_rate", length = 20)
    private String eduTaxRate;

    @Comment("本期应纳税额 (教育附加)")
    @ColumnDefault("0.00")
    @Column(name = "edu_tax_amount", precision = 22, scale = 2)
    private BigDecimal eduTaxAmount;

    @Size(max = 20)
    @Comment("减免性质代码 (教育附加)")
    @Column(name = "edu_exemption_code", length = 20)
    private String eduExemptionCode;

    @Comment("减免税额 (教育附加)")
    @ColumnDefault("0.00")
    @Column(name = "edu_exemption_amount", precision = 22, scale = 2)
    private BigDecimal eduExemptionAmount;

    @Comment("应补(退)税额 (教育附加)")
    @ColumnDefault("0.00")
    @Column(name = "edu_tax_payable", precision = 22, scale = 2)
    private BigDecimal eduTaxPayable;

    @Size(max = 20)
    @Comment("适用税率 (地方教育附加)")
    @Column(name = "local_edu_tax_rate", length = 20)
    private String localEduTaxRate;

    @Comment("本期应纳税额 (地方教育附加)")
    @ColumnDefault("0.00")
    @Column(name = "local_edu_tax_amount", precision = 22, scale = 2)
    private BigDecimal localEduTaxAmount;

    @Size(max = 20)
    @Comment("减免性质代码 (地方教育附加)")
    @Column(name = "local_edu_exemption_code", length = 20)
    private String localEduExemptionCode;

    @Comment("减免税额 (地方教育附加)")
    @ColumnDefault("0.00")
    @Column(name = "local_edu_exemption_amount", precision = 22, scale = 2)
    private BigDecimal localEduExemptionAmount;

    @Comment("应补(退)税额 (地方教育附加)")
    @ColumnDefault("0.00")
    @Column(name = "local_edu_tax_payable", precision = 22, scale = 2)
    private BigDecimal localEduTaxPayable;

    public ValueAddedTaxDetailEntity() {
    }

    public ValueAddedTaxDetailEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }
}