package com.olading.operate.labor.domain.salary.engine;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.salary.engine.dto.AccumulatedTaxData;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationRequest;
import com.olading.operate.labor.domain.salary.engine.dto.TaxCalculationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 薪酬个税计算服务
 *
 * 提供薪酬计算的业务接口，封装个税计算引擎的调用
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalaryTaxCalculationService {

    private final PersonalIncomeTaxCalculationEngine taxEngine;
    private final SalaryManager salaryManager;

    /**
     * 计算薪酬明细的个税
     *
     * @param salaryDetail 薪酬明细实体
     * @return 计算结果
     */
    @Transactional(rollbackFor = Exception.class)
    public TaxCalculationResult calculateSalaryTax(SalaryDetailEntity salaryDetail) {
        log.info("开始计算薪酬明细个税，ID: {}, 身份证: {}",
                salaryDetail.getId(), salaryDetail.getIdCard());

        try {

            // 1. 构建计算请求
            TaxCalculationRequest request = buildTaxCalculationRequest(salaryDetail);

            // 2. 执行个税计算
            TaxCalculationResult result = taxEngine.calculatePersonalIncomeTax(request);

            // 3. 更新薪酬明细
            updateSalaryDetailWithTaxResult(salaryDetail, result);

            // 4. 保存薪酬明细
            salaryManager.updateSalaryDetail(salaryDetail);

            log.info("薪酬明细个税计算完成，ID: {}",
                    salaryDetail.getId());

            return result;
        } catch (Exception e) {
            log.error("薪酬明细个税计算失败，ID: {}", salaryDetail.getId(), e);
            throw new RuntimeException("薪酬个税计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量计算薪酬明细的个税
     *
     * @param statementId 批次id
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchCalculateSalaryTax(Long statementId) {

        final SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(statementId);
        if(salaryStatementEntity.getStatus() != SalaryStatementStatus.CALCULATING){
            throw new BusinessException("工资批次状态不正确");
        }

        //主体在税款所属期上是否存在未确认的工资表
        SalaryStatementEntity statement = salaryManager.querySalaryStatement(t->t.taxPeriod.eq(salaryStatementEntity.getTaxPeriod())
                .and(t.supplierCorporationId.eq(salaryStatementEntity.getSupplierCorporationId()))
                .and(t.status.ne(SalaryStatementStatus.CONFIRMED))).fetchOne();

        if (statement != null && !Objects.equals(statement.getId(), statementId)) {
            throw new BusinessException("主体存在未确认的工资表，请确认后重试！");
        }

        List<SalaryDetailEntity> salaryDetails = salaryManager.querySalaryDetailByStatementId(statementId);

        log.info("开始批量计算薪酬个税，数量: {}", salaryDetails.size());

        for (SalaryDetailEntity salaryDetail : salaryDetails) {
            calculateSalaryTax(salaryDetail);
        }

        log.info("批量计算薪酬个税完成,更新批次状态为已计算，数量: {}", salaryDetails.size());

        this.updateTaxCalculationStatistics(statementId);
    }

    /**
     * 重新计算薪酬明细的个税
     *
     * @param salaryDetailId 薪酬明细ID
     * @return 计算结果
     */
    public TaxCalculationResult recalculateSalaryTax(Long salaryDetailId) {
        log.info("开始重新计算薪酬明细个税，ID: {}", salaryDetailId);

        SalaryDetailEntity salaryDetail = salaryManager.querySalaryDetail(salaryDetailId);
        if (salaryDetail == null) {
            throw new IllegalArgumentException("薪酬明细不存在，ID: " + salaryDetailId);
        }

        return calculateSalaryTax(salaryDetail);
    }

    /**
     * 预览个税计算结果（不保存）
     *
     * @param request 计算请求
     * @return 计算结果
     */
    public TaxCalculationResult previewTaxCalculation(TaxCalculationRequest request) {
        log.info("预览个税计算，身份证: {}, 作业主体: {}, 税期: {}",
                request.getIdCard(), request.getSupplierCorporationId(), request.getTaxPeriod());

        request.validate();
        return taxEngine.calculatePersonalIncomeTax(request);
    }

    /**
     * 构建个税计算请求
     */
    private TaxCalculationRequest buildTaxCalculationRequest(SalaryDetailEntity salaryDetail) {
        TaxCalculationRequest request = new TaxCalculationRequest();
        request.setIdCard(salaryDetail.getIdCard());
        request.setSupplierCorporationId(salaryDetail.getSupplierCorporationId());
        request.setTaxPeriod(salaryDetail.getTaxPeriod());
        request.setSalaryDetailId(salaryDetail.getId());
        request.setPayableAmount(salaryDetail.getPayableAmount());
        request.setTaxFreeIncome(salaryDetail.getTaxFreeIncome());
        request.setOtherDeductions(salaryDetail.getOtherDeductions());
        request.setTaxReliefAmount(salaryDetail.getTaxReliefAmount());

        request.validate();
        return request;
    }

    /**
     * 更新薪酬明细的个税计算结果
     */
    private void updateSalaryDetailWithTaxResult(SalaryDetailEntity salaryDetail,
                                                 TaxCalculationResult result) {

        AccumulatedTaxData accumulatedData = result.getNewAccumulatedData();

        // 更新累计数据
        salaryDetail.setAccumulatedIncome(accumulatedData.getAccumulatedIncome());
        salaryDetail.setAccumulatedExpenses(accumulatedData.getAccumulatedExpenses());
        salaryDetail.setAccumulatedDeductionExpenses(accumulatedData.getAccumulatedDeductionExpenses());
        salaryDetail.setAccumulatedTaxFreeIncome(accumulatedData.getAccumulatedTaxFreeIncome());
        salaryDetail.setAccumulatedOtherDeductions(accumulatedData.getAccumulatedOtherDeductions());
        salaryDetail.setAccumulatedTaxRelief(accumulatedData.getAccumulatedTaxRelief());
        salaryDetail.setAccumulatedTaxableAmount(accumulatedData.getAccumulatedTaxableAmount());
        salaryDetail.setAccumulatedTaxAmount(accumulatedData.getAccumulatedTaxAmount());
        salaryDetail.setAccumulatedPrepaidTax(accumulatedData.getAccumulatedPrepaidTax());

        // 更新本期计算结果
        salaryDetail.setCurrentTaxAmount(accumulatedData.getAccumulatedCurrentTaxAmount());
        // 本次应预扣预缴税额（实际扣款）
        salaryDetail.setCurrentWithholdingTax(result.getCurrentWithholdingTax());
        salaryDetail.setNetPayment(result.getNetPayment());

        // 更新增值税和附加税计算结果
        salaryDetail.setVatAmount(result.getVatAmount());
        salaryDetail.setAdditionalTaxAmount(result.getAdditionalTaxAmount());
        salaryDetail.setUrbanConstructionTax(result.getUrbanConstructionTax());
        salaryDetail.setEducationSurcharge(result.getEducationSurcharge());
        salaryDetail.setLocalEducationSurcharge(result.getLocalEducationSurcharge());
    }


    /**
     * 更新个税计算统计信息
     */
    public TaxCalculationStatistics updateTaxCalculationStatistics(Long salaryStatementId) {
        final SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(salaryStatementId);
        final TaxCalculationStatistics taxCalculationStatistics = getTaxCalculationStatistics(salaryStatementId);
        salaryStatementEntity.setStatus(SalaryStatementStatus.UNCONFIRMED);
        salaryStatementEntity.setNetPaymentTotal(taxCalculationStatistics.getTotalNetPayment());
        salaryStatementEntity.setTotalPayable(taxCalculationStatistics.getTotalPayableAmount());
        salaryStatementEntity.setTotalIncomeTax(taxCalculationStatistics.getTotalWithholdingTax());
        salaryStatementEntity.setTotalVat(taxCalculationStatistics.getVatAmount());
        salaryStatementEntity.setTotalSurtax(taxCalculationStatistics.getAdditionalTaxAmount());
        salaryManager.updateSalaryStatement(salaryStatementEntity);
        return taxCalculationStatistics;
    }



    /**
     * 获取个税计算统计信息
     */
    public TaxCalculationStatistics getTaxCalculationStatistics(Long salaryStatementId) {
        List<SalaryDetailEntity> salaryDetails = salaryManager.querySalaryDetailByStatementId(salaryStatementId);

        TaxCalculationStatistics statistics = new TaxCalculationStatistics();
        statistics.setTotalCount(salaryDetails.size());

        BigDecimal totalPayableAmount = BigDecimal.ZERO;
        BigDecimal totalWithholdingTax = BigDecimal.ZERO;
        BigDecimal totalNetPayment = BigDecimal.ZERO;
        BigDecimal vatAmount = BigDecimal.ZERO;
        BigDecimal additionalTaxAmount = BigDecimal.ZERO;
        int taxableCount = 0;

        for (SalaryDetailEntity detail : salaryDetails) {
            totalPayableAmount = totalPayableAmount.add(detail.getPayableAmount());
            totalWithholdingTax = totalWithholdingTax.add(detail.getCurrentWithholdingTax());
            totalNetPayment = totalNetPayment.add(detail.getNetPayment());
            vatAmount = vatAmount.add(detail.getVatAmount());
            additionalTaxAmount = additionalTaxAmount.add(detail.getAdditionalTaxAmount());
            if (detail.getCurrentWithholdingTax().compareTo(BigDecimal.ZERO) > 0) {
                taxableCount++;
            }
        }

        statistics.setTotalPayableAmount(totalPayableAmount);
        statistics.setTotalWithholdingTax(totalWithholdingTax);
        statistics.setTotalNetPayment(totalNetPayment);
        statistics.setTaxableCount(taxableCount);
        statistics.setVatAmount(vatAmount);
        statistics.setAdditionalTaxAmount(additionalTaxAmount);
        statistics.setNonTaxableCount(salaryDetails.size() - taxableCount);

        if (totalPayableAmount.compareTo(BigDecimal.ZERO) > 0) {
            statistics.setAverageTaxRate(totalWithholdingTax.divide(totalPayableAmount, 4, RoundingMode.HALF_UP));
        }

        return statistics;
    }

    /**
     * 个税计算统计信息
     */
    @lombok.Data
    public static class TaxCalculationStatistics {
        private int totalCount;              // 总人数
        private int taxableCount;            // 需要缴税人数
        private int nonTaxableCount;         // 无需缴税人数
        private BigDecimal totalPayableAmount;    // 总应发金额
        private BigDecimal totalWithholdingTax;   // 总预扣预缴税额
        private BigDecimal totalNetPayment;       // 总实发金额
        private BigDecimal averageTaxRate;        // 平均税负率
        private BigDecimal vatAmount;             // 增值税总额
        private BigDecimal additionalTaxAmount;   // 附加税总额
    }
}