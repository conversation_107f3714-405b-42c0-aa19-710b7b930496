package com.olading.operate.labor.domain.share.signing.common;

import com.olading.operate.labor.domain.share.signing.enums.SignType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description= "签署步骤")
@Data
public class Step {

    @Schema(description = "步骤名称", required = true)
    private String name;
    @Schema(description = "签名占位图片样式", required = true)
    private SignType signType;
}
