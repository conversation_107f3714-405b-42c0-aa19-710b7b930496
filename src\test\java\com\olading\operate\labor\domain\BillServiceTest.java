package com.olading.operate.labor.domain;

import cn.hutool.json.JSONUtil;
import com.olading.operate.labor.BaseTest;
import com.olading.operate.labor.domain.bill.BillFeeType;
import com.olading.operate.labor.domain.bill.BillMasterStatus;
import com.olading.operate.labor.domain.bill.dto.BillGenerateRequest;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportConfirmRequest;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportPreviewResult;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportResult;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportRow;
import com.olading.operate.labor.domain.bill.vo.BillMasterVO;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.service.BillService;
import com.olading.operate.labor.domain.share.contract.BusinessContractConfigEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.util.RedisUtils;
import com.olading.operate.labor.util.excel.ExcelResult;
import com.olading.operate.labor.util.excel.ExcelWriter;
import com.olading.operate.labor.util.excel.Excels;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * 账单服务单元测试
 * 测试账单生成、状态管理、权限控制等核心业务逻辑
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class BillServiceTest  extends BaseTest {

    @Autowired
    private BillService billService;

    @Autowired
    RedisUtils redisUtils;

    // 测试数据
    private Long testSupplierId;
    private Long testCustomerId;
    private Long testCorporationId;
    private Long testContractId;
    private Long testSalaryStatementId;
    private LocalDate testBillMonth;
    private TenantInfo testTenantInfo=TenantInfo.ofSupplier(9L);
    @Autowired
    private BusinessContractManager businessContractManager;


    @Test
    public void testRedis(){
        redisUtils.set("test", "test", 10);
        final String s = redisUtils.get("test");
        log.info( s);
    }



    public void beforeEach(){
        final ContractVo contractVo = businessContractManager.queryContract(28L);

        testBillMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        testSupplierId = 9L;
        testCustomerId =contractVo.getCustomerId();
        testCorporationId = contractVo.getSupplierCorporationId();
        testContractId = contractVo.getId();
    }


    /**
     * 准备测试数据：创建供应商、客户、合同、薪酬数据等
     */
    public void setupTestData() {




        // 4. 创建薪酬批次数据
        testSalaryStatementId = createSalaryStatement();

        // 5. 创建薪酬明细数据
        createSalaryDetails(testSalaryStatementId, 5); // 创建5个人的薪酬明细

        em.flush();
    }

    /**
     * 创建薪酬批次数据
     */
    private Long createSalaryStatement() {
        SalaryStatementEntity salaryStatement = new SalaryStatementEntity(TenantInfo.ofSupplier(testSupplierId));
        salaryStatement.setCustomerId(testCustomerId);
        salaryStatement.setContractId(testContractId);
        salaryStatement.setSupplierId(testSupplierId);
        salaryStatement.setSupplierCorporationId(testCorporationId);
        salaryStatement.setTaxPeriod(testBillMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        salaryStatement.setTotalPeople(5L);
        salaryStatement.setTotalPayable(new BigDecimal("50000.00"));
        salaryStatement.setTotalIncomeTax(new BigDecimal("2500.00"));
        salaryStatement.setTotalVat(new BigDecimal("1500.00"));
        salaryStatement.setTotalSurtax(new BigDecimal("150.00"));
        salaryStatement.setNetPaymentTotal(new BigDecimal("45850.00"));
        salaryStatement.setTaxDeclarationMonth(testBillMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        salaryStatement.setStatus(SalaryStatementStatus.CONFIRMED);
        salaryStatement.setUploadTime(LocalDateTime.now());

        em.persist(salaryStatement);
        return salaryStatement.getId();
    }

    /**
     * 创建薪酬明细数据
     */
    private void createSalaryDetails(Long salaryStatementId, int count) {
        for (int i = 1; i <= count; i++) {
            SalaryDetailEntity detail = new SalaryDetailEntity(TenantInfo.ofSupplier(testSupplierId));
            detail.setSalaryStatementId(salaryStatementId);
            detail.setName("测试员工" + i);
            detail.setIdCard("11010119900101123" + i);
            detail.setPhoneNumber("1380000000" + i);
            detail.setPayableAmount(new BigDecimal("10000.00"));
            detail.setAccumulatedIncome(new BigDecimal("10000.00"));
            detail.setCurrentWithholdingTax(new BigDecimal("500.00"));
            detail.setVatAmount(new BigDecimal("300.00"));
            detail.setAdditionalTaxAmount(new BigDecimal("30.00"));
            detail.setNetPayment(new BigDecimal("9170.00"));

            em.persist(detail);
        }
    }

    // ==================== 账单生成测试 ====================

    @Test
    @DisplayName("生成账单 - 成功场景")
    void generateBill_Success() {

        testBillMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());

        // 准备测试数据
        withTransaction(status -> {
            setupTestData();
            return null;
        });



        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(testBillMonth);
        request.setRemark("测试生成账单");

        // When
        BillMasterVO result = billService.generateBill(testTenantInfo, testSupplierId, request);

        // Then
        log.info("账单生成结果: {}", JSONUtil.toJsonStr( result));
    }

    @Test
    @DisplayName("生成账单 - 合同ID为空")
    void generateBill_NullContractId() {
        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(null);
        request.setBillMonth(testBillMonth);

        // When & Then
        assertThatThrownBy(() -> billService.generateBill(testTenantInfo, testSupplierId, request))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("合同ID不能为空");
    }

    @Test
    @DisplayName("生成账单 - 账单月份为空")
    void generateBill_NullBillMonth() {
        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(null);

        // When & Then
        assertThatThrownBy(() -> billService.generateBill(testTenantInfo, testSupplierId, request))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("账单月份不能为空");
    }

    @Test
    @DisplayName("生成账单 - 合同不存在")
    void generateBill_ContractNotFound() {
        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(999999L);
        request.setBillMonth(testBillMonth);

        // When & Then
        assertThatThrownBy(() -> billService.generateBill(testTenantInfo, testSupplierId, request))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("合同不存在");
    }

    @Test
    @DisplayName("生成账单 - 供应商ID为空")
    void generateBill_NullSupplierId() {
        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(testBillMonth);

        // When & Then
        assertThatThrownBy(() -> billService.generateBill(testTenantInfo, null, request))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("供应商ID不能为空");
    }

    @Test
    @DisplayName("生成账单 - 未来月份")
    void generateBill_FutureMonth() {
        // Given
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(LocalDate.now().plusMonths(2)); // 未来月份

        // When & Then
        assertThatThrownBy(() -> billService.generateBill(testTenantInfo, testSupplierId, request))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("不能为未来月份生成账单");
    }

    // ==================== 账单状态管理测试 ====================

    @Test
    @DisplayName("提交账单确认 - 成功场景")
    void submitBillForConfirm_Success() {
        // Given
        Long billId =11L;

        // When
        billService.submitBillForConfirm(testSupplierId, billId);

        // Then
        BillMasterVO bill = billService.getBillDetail(testSupplierId, billId);
        assertThat(bill.getBillStatus()).isEqualTo(BillMasterStatus.PENDING_CONFIRM);
    }

    @Test
    @DisplayName("确认账单 - 成功场景")
    void confirmBill_Success() {

        // Given
        Long billId =11L;

        // When
        billService.confirmBill(testSupplierId, billId);

        // Then
        BillMasterVO bill = billService.getBillDetail(testSupplierId, billId);
        assertThat(bill.getBillStatus()).isEqualTo(BillMasterStatus.CONFIRMED);
        assertThat(bill.getConfirmTime()).isNotNull();
    }

    @Test
    @DisplayName("确认账单 - 状态不正确")
    void confirmBill_InvalidStatus() {
        // Given - 生成账单但不提交确认
        Long billId = createTestBill();

        // When & Then - 直接确认应该失败
        assertThatThrownBy(() -> billService.confirmBill(testSupplierId, billId))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("只有待确认状态的账单才能确认");
    }

    @Test
    @DisplayName("删除账单 - 成功场景")
    void deleteBill_Success() {
        // Given
        Long billId = 7L;

        // When
        billService.deleteBill(testSupplierId, billId);

        // Then - 账单应该被删除，获取详情应该失败
        assertThatThrownBy(() -> billService.getBillDetail(testSupplierId, billId))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("账单不存在");
    }

    @Test
    @DisplayName("删除账单 - 已确认状态不能删除")
    void deleteBill_ConfirmedBillCannotDelete() {
        // Given - 生成并确认账单
        Long billId = 11L;

        // When & Then
        assertThatThrownBy(() -> billService.deleteBill(testSupplierId, billId))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("已确认的账单不能删除");
    }

    // ==================== 权限控制测试 ====================

    @Test
    @DisplayName("权限验证 - 无权限访问其他供应商账单")
    void validateBillPermission_NoPermission() {
        // Given
        Long billId = createTestBill();
        Long otherSupplierId = uniqueId();
        Map<OwnerType, Set<Long>> dataScopes = new HashMap<>();

        // When & Then
        assertThatThrownBy(() -> billService.validateBillPermissionWithDataScope(
            otherSupplierId, billId, dataScopes, false))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("无权限");
    }

    @Test
    @DisplayName("权限验证 - 管理员权限")
    void validateBillPermission_AdminAccess() {
        // Given
        Long billId = createTestBill();
        Long otherSupplierId = uniqueId();
        Map<OwnerType, Set<Long>> dataScopes = new HashMap<>();

        // When & Then - 管理员应该能访问
        billService.validateBillPermissionWithDataScope(otherSupplierId, billId, dataScopes, true);
        // 不应该抛出异常
    }

    // ==================== 其他费用导入测试 ====================

    @Test
    @DisplayName("其他费用导入预览 - 成功场景")
    void previewOtherFeeImport_Success() {
        // Given
        Long billId = createTestBill();
        MockMultipartFile file = createTestExcelFile();

        // When
        BillOtherFeeImportPreviewResult result = billService.previewOtherFeeImport(testSupplierId, billId, file);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTotalRows()).isGreaterThan(0);
        assertThat(result.getValidRows()).isGreaterThan(0);
        assertThat(result.getErrorRows()).isEqualTo(0);
        assertThat(result.getTotalAmount()).isGreaterThan(BigDecimal.ZERO);
        assertThat(result.getPersonCount()).isGreaterThan(0);
        assertThat(result.getPreviewData()).isNotEmpty();
    }

    @Test
    @DisplayName("确认导入其他费用 - 成功场景")
    void confirmOtherFeeImport_Success() {

        testBillMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());

        // 准备测试数据
        withTransaction(status -> {
            setupTestData();
            return null;
        });

        // Given
        Long billId = createTestBill();
        List<BillOtherFeeImportRow> importData = createTestImportData();
        
        BillOtherFeeImportConfirmRequest request = new BillOtherFeeImportConfirmRequest();
        request.setImportData(importData);

        // When
        BillOtherFeeImportResult result = billService.confirmOtherFeeImport(testSupplierId, billId, request);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getImportCount()).isEqualTo(importData.size());
        assertThat(result.getImportBatchNo()).isNotNull();
        assertThat(result.getTotalAmount()).isGreaterThan(BigDecimal.ZERO);
        assertThat(result.getPersonCount()).isGreaterThan(0);

        // 验证账单金额已更新
        BillMasterVO updatedBill = billService.getBillDetail(testSupplierId, billId);
        assertThat(updatedBill.getOtherFeeAmount()).isGreaterThan(BigDecimal.ZERO);
    }

    @Test
    @DisplayName("其他费用导入 - 已确认账单不能导入")
    void previewOtherFeeImport_ConfirmedBillCannotImport() {
        // Given - 生成并确认账单
        Long billId = createTestBill();
        billService.submitBillForConfirm(testSupplierId, billId);
        billService.confirmBill(testSupplierId, billId);
        
        MockMultipartFile file = createTestExcelFile();

        // When & Then
        assertThatThrownBy(() -> billService.previewOtherFeeImport(testSupplierId, billId, file))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("当前账单状态不允许导入其他费用");
    }

    // ==================== 账单详情测试 ====================

    @Test
    @DisplayName("获取账单详情 - 成功场景")
    void getBillDetail_Success() {
        // Given
        Long billId = 17L;

        // When
        BillMasterVO result = billService.getBillDetail(testSupplierId, billId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(billId);
        assertThat(result.getContractId()).isEqualTo(testContractId);
        assertThat(result.getCustomerId()).isEqualTo(testCustomerId);
        assertThat(result.getCustomerName()).isNotNull();
        assertThat(result.getContractName()).isNotNull();
        assertThat(result.getCategories()).isNotEmpty();
        
        // 验证分类信息
        assertThat(result.getCategories()).anyMatch(category -> 
            category.getFeeType() == BillFeeType.SALARY);
        assertThat(result.getCategories()).anyMatch(category -> 
            category.getFeeType() == BillFeeType.MANAGEMENT_FEE);
    }

    @Test
    @DisplayName("获取账单详情 - 账单不存在")
    void getBillDetail_BillNotFound() {
        // When & Then
        assertThatThrownBy(() -> billService.getBillDetail(testSupplierId, 999999L))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("账单不存在");
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试账单
     */
    private Long createTestBill() {
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(testBillMonth);
        request.setRemark("测试账单");

        BillMasterVO bill = billService.generateBill(testTenantInfo, testSupplierId, request);
        return bill.getId();
    }

    /**
     * 创建测试Excel文件
     */
    private MockMultipartFile createTestExcelFile(){

        // 获取账单信息用于模板说明
        // 创建Excel模板
        ExcelResult excelResult = Excels.createWriteResult(BillOtherFeeImportRow.class);

        // 添加示例数据
        BillOtherFeeImportRow example1 = new BillOtherFeeImportRow();
        example1.setFeeName("交通费");
        example1.setFeeAmount(new java.math.BigDecimal("150.00"));
        // 账单月份中的某一天
        example1.setOccurDate(LocalDate.now());
        example1.setLaborName("张三");
        example1.setIdCard("110101199001011234");
        example1.setFeePurpose("出差交通费用");

        BillOtherFeeImportRow example2 = new BillOtherFeeImportRow();
        example2.setFeeName("餐费");
        example2.setFeeAmount(new java.math.BigDecimal("80.50"));
        example2.setOccurDate(LocalDate.now());
        example2.setLaborName("李四");
        example2.setIdCard("110101199002021234");
        example2.setFeePurpose("加班餐费补贴");

        excelResult.addRow(example1);
        excelResult.addRow(example2);

        // 生成并下载Excel文件
        final ExcelWriter excelWriter = Excels.writer(excelResult)
                .sheetName(String.format("其他费用导入模板_%s", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月"))))
                .build();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            excelWriter.writeToWorkbook().write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return new MockMultipartFile(
            "file",
            "test-other-fees.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
        );
    }

    /**
     * 创建测试导入数据
     */
    private List<BillOtherFeeImportRow> createTestImportData() {
        List<BillOtherFeeImportRow> importData = new ArrayList<>();
        
        BillOtherFeeImportRow row1 = new BillOtherFeeImportRow();
        row1.setFeeName("交通费");
        row1.setFeeAmount(new BigDecimal("150.00"));
        row1.setOccurDate(LocalDate.of(2025, 7, 15));
        row1.setLaborName("张三");
        row1.setIdCard("110101199001011234");
        row1.setFeePurpose("出差交通费");
        importData.add(row1);
        
        BillOtherFeeImportRow row2 = new BillOtherFeeImportRow();
        row2.setFeeName("餐费");
        row2.setFeeAmount(new BigDecimal("80.50"));
        row2.setOccurDate(LocalDate.of(2025, 7, 16));
        row2.setLaborName("李四");
        row2.setIdCard("110101199002021234");
        row2.setFeePurpose("加班餐费");
        importData.add(row2);
        
        return importData;
    }

    // ==================== 边界条件测试 ====================

    @Test
    @DisplayName("生成账单 - 无薪酬数据")
    void generateBill_NoSalaryData() {
        // Given - 创建一个没有薪酬数据的合同
        Long newContractId = createContractWithoutSalaryData();
        
        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(newContractId);
        request.setBillMonth(testBillMonth);

        // When
        BillMasterVO result = billService.generateBill(testTenantInfo, testSupplierId, request);

        // Then - 应该能生成账单，但薪酬金额为0
        assertThat(result).isNotNull();
        assertThat(result.getSalaryAmount()).isEqualTo(BigDecimal.ZERO);
        assertThat(result.getTotalReceivableAmount()).isEqualTo(BigDecimal.ZERO);
    }

    @Test
    @DisplayName("生成账单 - 客户已禁用")
    void generateBill_DisabledCustomer() {
        // Given - 禁用客户
        withTransaction(status -> {
            CustomerEntity customer = em.find(CustomerEntity.class, testCustomerId);
            customer.setDisabled(true);
            em.merge(customer);
            return null;
        });

        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(testBillMonth);

        // When & Then
        assertThatThrownBy(() -> billService.generateBill(testTenantInfo, testSupplierId, request))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("客户");
    }

    @Test
    @DisplayName("生成账单 - 作业主体已禁用")
    void generateBill_DisabledCorporation() {
        // Given - 禁用作业主体
        withTransaction(status -> {
            SupplierCorporationEntity corporation = em.find(SupplierCorporationEntity.class, testCorporationId);
            corporation.setDisabled(true);
            em.merge(corporation);
            return null;
        });

        BillGenerateRequest request = new BillGenerateRequest();
        request.setContractId(testContractId);
        request.setBillMonth(testBillMonth);

        // When & Then
        assertThatThrownBy(() -> billService.generateBill(testTenantInfo, testSupplierId, request))
            .isInstanceOf(Exception.class)
            .hasMessageContaining("作业主体");
    }

    /**
     * 创建没有薪酬数据的合同
     */
    private Long createContractWithoutSalaryData() {
        return withTransaction(status -> {
            Long newContractId = uniqueId();
            BusinessContractEntity newContract = new BusinessContractEntity();
            newContract.setId(newContractId);
            newContract.setSupplierId(testSupplierId);
            newContract.setCustomerId(testCustomerId);
            newContract.setSupplierCorporationId(testCorporationId);
            newContract.setName("无薪酬数据合同");
            newContract.setStopped(false);
            em.persist(newContract);

            // 创建合同配置
            BusinessContractConfigEntity contractConfig = new BusinessContractConfigEntity();
            contractConfig.setId(uniqueId());
            contractConfig.setContractId(newContractId);
            contractConfig.setManageCalculationRule("EMPLOYEE_COUNT");
            contractConfig.setManageRate(new BigDecimal("0.10"));
            em.persist(contractConfig);

            em.flush();
            return newContractId;
        });
    }
}