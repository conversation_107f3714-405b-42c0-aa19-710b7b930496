package com.olading.operate.labor.domain.supplier;

import com.olading.operate.labor.app.web.biz.boss.BossController;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SupplierData {

    private Long id;

    @Schema(description = "灵工平台编号")
    private String supplierNo;
    @Schema(description = "管理员用户ID")
    private Long adminUserId;
    @Schema(description = "管理员用户名称")
    private String adminName;
    @Schema(description = "名称")
    private String name;
    private boolean disabled;

    private LocalDateTime businessCreateTime;
    @Schema(description = "短信签名")
    private String signatureCode;

    private SupplierDomainData domain = new SupplierDomainData();

    private EnterpriseInfoData info = new EnterpriseInfoData();

    public SupplierData() {
    }

    public SupplierData(SupplierEntity entity) {
        this.id = entity.getId();
        this.adminUserId = entity.getAdminUserId();
        this.disabled = entity.getDisabled();
        this.signatureCode = entity.getSignatureCode();
        this.supplierNo = entity.getSupplierNo();
        this.businessCreateTime = entity.getBusinessCreateTime();
    }

    public static SupplierData toSupplierData(BossController.SupplierDetailVo request) {
        SupplierData data = new SupplierData();
        data.setId(request.getId());
        data.setSupplierNo(request.getSupplierNo());
        data.setBusinessCreateTime(request.getBusinessCreateTime());
        data.getInfo().setAttachments(request.getAttachments());
        data.getInfo().setContactPhone(request.getContactPhone());
        data.getInfo().setContacts(request.getContacts());
        data.getInfo().setName(request.getName());
        data.getInfo().setSocialCreditCode(request.getSocialCreditCode());
        data.setSignatureCode(request.getSignatureCode());
        data.getDomain().setDomainName(request.getDomainName());
        data.getDomain().setSlogan(request.getSlogan());
        data.getDomain().setLogoUrl(request.getLogoUrl());
        data.getDomain().setH5DomainName(request.getH5DomainName());
        data.getDomain().setH5LogoUrl(request.getH5LogoUrl());
        data.getDomain().setBrandName(request.getBrandName());
        data.getDomain().setH5ServiceAgreement(request.getH5ServiceAgreement());
        return data;
    }
}
