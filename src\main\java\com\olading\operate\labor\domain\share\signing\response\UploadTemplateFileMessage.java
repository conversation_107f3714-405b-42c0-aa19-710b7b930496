package com.olading.operate.labor.domain.share.signing.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date ：Created in 2021/3/22
 * @description ：
 * @version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
public class UploadTemplateFileMessage {

    @Schema(description = "结果" )
    private Boolean success;

    @Schema(description = "消息" )
    private String message;

    @Schema(description = "错误码" )
    private String errorCode;

    private String data;
}
