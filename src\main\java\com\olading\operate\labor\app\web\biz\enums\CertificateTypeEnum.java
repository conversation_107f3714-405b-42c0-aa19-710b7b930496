package com.olading.operate.labor.app.web.biz.enums;

public enum CertificateTypeEnum {

    ID_CARD("居民身份证"),
    PASSPORT("护照"),
    HONG_KONG_MACAO_TAIWAN("港澳台同胞回乡证"),
    FOREIGNER("外国人永久居留证");

    private final String name;

    CertificateTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameByKey(String key) {
        for (CertificateTypeEnum e : values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getName();
            }
        }
        return "";
    }
}
