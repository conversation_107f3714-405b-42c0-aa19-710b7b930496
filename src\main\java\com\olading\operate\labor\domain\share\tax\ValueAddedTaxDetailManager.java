package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.domain.TenantInfo;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Transactional
@Component
@RequiredArgsConstructor
public class ValueAddedTaxDetailManager {

    private final EntityManager em;

    /**
     * 批量新增增值税申报明细记录
     */
    public List<ValueAddedTaxDetailEntity> batchAddValueAddedTaxDetail(List<ValueAddedTaxDetailEntity> details) {
        for (ValueAddedTaxDetailEntity detail : details) {
            em.persist(detail);
        }
        em.flush();
        return details;
    }

    /**
     * 新增增值税申报明细记录
     */
    public ValueAddedTaxDetailEntity addValueAddedTaxDetail(TenantInfo tenantInfo, ValueAddedTaxDetailEntity detail) {
        return em.merge(detail);
    }

    /**
     * 根据申报ID查询明细列表
     */
    public List<ValueAddedTaxDetailEntity> queryDetailsByValueAddedTaxId(Long valueAddedTaxId) {
        QValueAddedTaxDetailEntity t = QValueAddedTaxDetailEntity.valueAddedTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .fetch();
    }

    /**
     * 根据税款所属期和作业主体ID查询明细列表
     */
    public List<ValueAddedTaxDetailEntity> queryDetailsByTaxPeriodAndCorporation(String taxPeriod, Long supplierCorporationId) {
        QValueAddedTaxDetailEntity t = QValueAddedTaxDetailEntity.valueAddedTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.taxPeriod.eq(taxPeriod)
                        .and(t.supplierCorporationId.eq(supplierCorporationId)))
                .fetch();
    }

    /**
     * 根据申报ID统计纳税人数
     */
    public Long countTaxpayersByValueAddedTaxId(Long valueAddedTaxId) {
        QValueAddedTaxDetailEntity t = QValueAddedTaxDetailEntity.valueAddedTaxDetailEntity;
        return new JPAQueryFactory(em)
                .select(t.count())
                .from(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .fetchOne();
    }

    /**
     * 根据申报ID统计本期收入总额
     */
    public BigDecimal sumTaxBasisByValueAddedTaxId(Long valueAddedTaxId) {
        QValueAddedTaxDetailEntity t = QValueAddedTaxDetailEntity.valueAddedTaxDetailEntity;
        BigDecimal result = new JPAQueryFactory(em)
                .select(t.taxBasis.sum())
                .from(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .fetchOne();
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 根据申报ID统计增值税总额
     */
    public BigDecimal sumVatAmountByValueAddedTaxId(Long valueAddedTaxId) {
        QValueAddedTaxDetailEntity t = QValueAddedTaxDetailEntity.valueAddedTaxDetailEntity;
        BigDecimal result = new JPAQueryFactory(em)
                .select(t.vatAmount.sum())
                .from(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .fetchOne();
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 根据申报ID统计附加税总额（城建税+教育附加+地方教育附加）
     */
    public BigDecimal sumSurtaxAmountByValueAddedTaxId(Long valueAddedTaxId) {
        QValueAddedTaxDetailEntity t = QValueAddedTaxDetailEntity.valueAddedTaxDetailEntity;
        
        // 城建税总额
        BigDecimal urbanTaxSum = new JPAQueryFactory(em)
                .select(t.urbanTaxAmount.sum())
                .from(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .fetchOne();
        urbanTaxSum = urbanTaxSum != null ? urbanTaxSum : BigDecimal.ZERO;
        
        // 教育附加总额
        BigDecimal eduTaxSum = new JPAQueryFactory(em)
                .select(t.eduTaxAmount.sum())
                .from(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .fetchOne();
        eduTaxSum = eduTaxSum != null ? eduTaxSum : BigDecimal.ZERO;
        
        // 地方教育附加总额
        BigDecimal localEduTaxSum = new JPAQueryFactory(em)
                .select(t.localEduTaxAmount.sum())
                .from(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .fetchOne();
        localEduTaxSum = localEduTaxSum != null ? localEduTaxSum : BigDecimal.ZERO;
        
        // 合计
        return urbanTaxSum.add(eduTaxSum).add(localEduTaxSum);
    }

    /**
     * 根据申报ID删除明细记录
     */
    public void deleteDetailsByValueAddedTaxId(Long valueAddedTaxId) {
        QValueAddedTaxDetailEntity t = QValueAddedTaxDetailEntity.valueAddedTaxDetailEntity;
        new JPAQueryFactory(em)
                .delete(t)
                .where(t.valueAddedTaxId.eq(valueAddedTaxId))
                .execute();
    }
}
