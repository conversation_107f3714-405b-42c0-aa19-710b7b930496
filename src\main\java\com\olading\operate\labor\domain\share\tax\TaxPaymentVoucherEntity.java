package com.olading.operate.labor.domain.share.tax;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("税务税款缴纳表")
@Entity
@Table(name = "t_tax_payment_voucher", schema = "olading_labor")
public class TaxPaymentVoucherEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 20)
    @Comment("税款所属期")
    @Column(name = "tax_payment_period", length = 20)
    private String taxPaymentPeriod;

    @Size(max = 5000)
    @Comment("附件id,逗号分隔")
    @Column(name = "file_ids", length = 5000)
    private String fileIds;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    public TaxPaymentVoucherEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }

    public TaxPaymentVoucherEntity() {
    }
}