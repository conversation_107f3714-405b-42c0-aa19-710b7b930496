package com.olading.operate.labor.domain.identity.repository;

import com.olading.operate.labor.domain.identity.IdentityOcrRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * OCR识别记录仓储接口
 * 任务 2.4: 创建Repository接口
 */
@Repository
public interface IdentityOcrRecordRepository extends JpaRepository<IdentityOcrRecordEntity, Long>, JpaSpecificationExecutor<IdentityOcrRecordEntity> {

    /**
     * 根据记录编号查找记录
     */
    Optional<IdentityOcrRecordEntity> findByRecordNoAndDeletedFalse(String recordNo);

    /**
     * 根据用户ID查找OCR记录
     */
    Page<IdentityOcrRecordEntity> findByUserIdAndDeletedFalse(Long userId, Pageable pageable);

    /**
     * 根据供应商ID查找OCR记录
     */
    Page<IdentityOcrRecordEntity> findBySupplierIdAndDeletedFalse(Long supplierId, Pageable pageable);

    /**
     * 根据作业主体ID查找OCR记录
     */
    Page<IdentityOcrRecordEntity> findByCorporationIdAndDeletedFalse(Long corporationId, Pageable pageable);

    /**
     * 根据身份证号查找OCR记录
     */
    List<IdentityOcrRecordEntity> findByIdCardNoAndDeletedFalse(String idCardNo);

    /**
     * 根据认证场景查找记录
     */
    List<IdentityOcrRecordEntity> findByAuthSceneAndDeletedFalse(String authScene);
}