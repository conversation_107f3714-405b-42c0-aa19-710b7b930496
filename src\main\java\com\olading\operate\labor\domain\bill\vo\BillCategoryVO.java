package com.olading.operate.labor.domain.bill.vo;

import com.olading.operate.labor.domain.bill.BillFeeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单分类统计视图对象
 */
@Data
@Schema(description = "账单分类统计信息")
public class BillCategoryVO {

    @Schema(description = "分类ID")
    private Long id;

    @Schema(description = "账单主表ID")
    private Long billMasterId;

    @Schema(description = "费用类型")
    private BillFeeType feeType;

    @Schema(description = "费用类型描述")
    private String feeTypeDesc;

    @Schema(description = "费用总额")
    private BigDecimal totalAmount;

    @Schema(description = "明细条数")
    private Integer detailCount;

    @Schema(description = "涉及人数")
    private Integer personCount;

    @Schema(description = "计算规则说明")
    private String calculationRule;

    @Schema(description = "账单月份")
    private LocalDate billMonth;

    @Schema(description = "备注")
    private String remark;
}