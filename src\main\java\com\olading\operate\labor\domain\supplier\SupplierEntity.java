package com.olading.operate.labor.domain.supplier;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 服务运营方
 */
@SuppressWarnings("LombokGetterMayBeUsed")
@Table(name = "t_supplier")
@Entity
@Getter
@Setter
public class SupplierEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;


    @Size(max = 20)
    @Comment("名称")
    @Column(name = "name", length = 20)
    private String name;

    @Comment("企业信息ID")
    @Column(name = "enterprise_info_id")
    private Long enterpriseInfoId;

    @Comment("业务创建时间")
    @Column(name = "business_create_time")
    private LocalDateTime businessCreateTime;

    @Size(max = 50)
    @Comment("短信签名")
    @Column(name = "signature_code", length = 50)
    private String signatureCode;

    @Comment("是否禁用")
    @Column(name = "disabled")
    private Boolean disabled = Boolean.FALSE;

    @Comment("管理员用户ID")
    @Column(name = "admin_user_id")
    private Long adminUserId;

    @Size(max = 100)
    @Comment("服务商编号")
    @Column(name = "supplier_no", length = 100)
    private String supplierNo;

    public SupplierEntity(TenantInfo tenantInfo){
        setTenant(tenantInfo);
    }

    protected SupplierEntity() {
    }

}
