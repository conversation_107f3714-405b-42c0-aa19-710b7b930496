package com.olading.operate.labor.app.web.biz.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Setter;


public enum EnumContractSignState {

    ACCEPT("ACCEPT","已受理"),

    IN_PROCESS("IN_PROCESS","处理中"),

    SUCCESS("SUCCESS","签署成功");


    private String code;
    private String name;

    EnumContractSignState(String code, String name) {
        this.code = code;
        this.name = name;

    }

    @JsonCreator
    public static EnumContractSignState getItem(String code) {
        for (EnumContractSignState item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    @JsonValue
    public String getCode() {
        return code;
    }


    public String getName() {
        return name;
    }

}
