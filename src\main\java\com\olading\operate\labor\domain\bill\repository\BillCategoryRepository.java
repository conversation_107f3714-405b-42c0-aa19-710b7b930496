package com.olading.operate.labor.domain.bill.repository;

import com.olading.operate.labor.domain.bill.BillCategoryEntity;
import com.olading.operate.labor.domain.bill.BillFeeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 账单分类统计仓储接口
 */
@Repository
public interface BillCategoryRepository extends JpaRepository<BillCategoryEntity, Long> {

    /**
     * 根据账单主表ID查找分类
     */
    List<BillCategoryEntity> findByBillMasterIdAndDeletedFalse(Long billMasterId);

    /**
     * 根据账单主表ID和费用类型查找分类列表
     */
    List<BillCategoryEntity> findByBillMasterIdAndFeeTypeAndDeletedFalse(Long billMasterId, BillFeeType feeType);

    /**
     * 统计账单主表的分类数量
     */
    @Query("SELECT COUNT(c) FROM BillCategoryEntity c WHERE c.billMasterId = :billMasterId AND c.deleted = false")
    long countByBillMasterId(@Param("billMasterId") Long billMasterId);


    void deleteByBillMasterId(@Param("billMasterId") Long billMasterId);
}