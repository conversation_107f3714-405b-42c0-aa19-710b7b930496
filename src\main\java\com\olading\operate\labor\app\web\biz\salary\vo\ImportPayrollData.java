package com.olading.operate.labor.app.web.biz.salary.vo;

import com.olading.operate.labor.util.excel.ExcelColumn;
import com.olading.operate.labor.util.excel.ExcelRow;
import com.olading.operate.labor.util.validation.constraints.Name;
import lombok.Data;

@Data
public class ImportPayrollData extends ExcelRow {
    @ExcelColumn(name = "姓名", required = true)
    @Name(required = true)
    private String name;

    @ExcelColumn(name = "身份证号", required = true,width = 40)
    @Name(required = true, maxLength = 60)
    private String idCard;

    private String cellPhone;

    @ExcelColumn(name = "应发金额", required = true)
    private String amount;

    @ExcelColumn(name = "当期免税收入",width = 35)
    private String currentDeductionIncome;

    @ExcelColumn(name = "依法确定的其他扣除",width = 40)
    private String otherDeduction;

    @ExcelColumn(name = "减免税额")
    private String reductionTax;

    @ExcelColumn(name = "反馈信息",width = 40)
    @Name(required = true, maxLength = 60)
    private String errorMsg;
}
