package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.app.web.biz.protocol.ProtocolController;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.share.labor.QSupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolEntity;
import com.olading.operate.labor.domain.share.protocol.QCorporationProtocolEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

public class ProtocolQuery implements EntityQuery<QueryFilter<ProtocolQuery.Filters>, ProtocolQuery.Record> {

    private final QCorporationProtocolEntity t1 = QCorporationProtocolEntity.corporationProtocolEntity;

    private final QSupplierLaborEntity t2 = QSupplierLaborEntity.supplierLaborEntity;

    private final QSupplierCorporationEntity corporation = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();
        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t1.supplierId.eq(filters.getFilters().getSupplierId()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getProtocolNameOrLabor())) {
            criteria.and(t1.protocolName.like(JpaUtils.fullLike(filters.getFilters().getProtocolNameOrLabor())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getProtocolName())) {
            criteria.and(t1.protocolName.like(JpaUtils.fullLike(filters.getFilters().getProtocolName())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getProtocolStatus())) {
            criteria.and(t1.signStatus.eq(EnumContractSignStatus.valueOf(filters.getFilters().getProtocolStatus())));
        }
        if (filters.getFilters().getCorporationIds() != null) {
            criteria.and(t1.supplierCorporationId.in(filters.getFilters().getCorporationIds()));
        }
//        criteria.and(t2.deleted.eq(false));

        query.select(t1, t2.name, t2.idCard, corporation)
                .distinct()
                .from(t1)
                .leftJoin(t2).on(t1.idCard.eq(t2.idCard))
                .leftJoin(corporation).on(t1.supplierCorporationId.eq(corporation.id));

        query.where(criteria).orderBy(t1.id.desc());
    }

    @Override
    public Record transform(Object v) {
        Tuple tuple = (Tuple) v;

        String laborName = tuple.get(t2.name);
        CorporationProtocolEntity protocol = tuple.get(t1);
        SupplierCorporationEntity corp = tuple.get(corporation);

        ProtocolQuery.Record record = new ProtocolQuery.Record();
        record.setProtocol(protocol);
        record.setLaborName(laborName);
        record.setLaborIdCard(protocol != null? protocol.getIdCard() : "");
        record.setCorporation(corp);
        return record;
    }


    @Data
    public static class Filters {
        private String protocolNameOrLabor;
        private String protocolName;
        private String protocolStatus;
        private Long supplierId;
        private Set<Long> corporationIds;
    }

    @Data
    public static class Record {
        private CorporationProtocolEntity protocol;
        private String laborName;
        private String laborIdCard;
        private SupplierCorporationEntity corporation;

        public ProtocolController.ProtocolListVo toVo() {
            ProtocolController.ProtocolListVo protocolListVo = new ProtocolController.ProtocolListVo();
            protocolListVo.setProtocolId(protocol.getId());
            protocolListVo.setProtocolName(protocol.getProtocolName());
            protocolListVo.setProtocolNo(String.valueOf(protocol.getId()));
            protocolListVo.setProtocolStatus(protocol.getSignStatus().name());
            protocolListVo.setLaborName(laborName);
            protocolListVo.setLaborIdCard(laborIdCard);
            protocolListVo.setCorporationName(corporation.getName());
            protocolListVo.setStartDate(protocol.getStartDate());
            protocolListVo.setEndDate(protocol.getEndDate());
            protocolListVo.setFileId(protocol.getFileId());
            return protocolListVo;
        }
    }
}
