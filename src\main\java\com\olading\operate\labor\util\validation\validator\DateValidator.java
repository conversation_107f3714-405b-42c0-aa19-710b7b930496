package com.olading.operate.labor.util.validation.validator;

import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.util.validation.constraints.Date;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2022/2/22 10:56
 */
public class DateValidator extends BaseValidator<Date> {
    private String format = "yyyy-MM-dd";

    public void setFormat(String format) {
        this.format = format;
    }

    public DateValidator() {}

    public DateValidator(String name) {
        setName(name);
    }

    @Override
    public void initialize(Date constraintAnnotation) {
        setRequired(constraintAnnotation.required());
        setName(constraintAnnotation.label());
        format = constraintAnnotation.format();
        super.initialize(constraintAnnotation);
    }

    /**
     * 一般用于传参，严格校验
     * @param o
     * @return
     */
    @Override
    protected boolean constraintCheck(Object o) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        try {
            LocalDate.parse(String.valueOf(o), df);
        } catch (Exception e) {
            throw new ValidationException(getName(), "格式不正确");
        }

        return true;
    }
}
