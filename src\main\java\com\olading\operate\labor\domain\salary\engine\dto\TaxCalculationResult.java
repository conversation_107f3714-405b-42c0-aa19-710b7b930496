package com.olading.operate.labor.domain.salary.engine.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 个税计算结果对象
 */
@Data
public class TaxCalculationResult {
    
    // ========== 更新后的累计数据 ==========
    
    /**
     * 更新后的累计数据
     */
    private AccumulatedTaxData newAccumulatedData;


    /**
     * 本期应预扣预缴税额 (理论增量)
     */
    private BigDecimal currentTaxAmount;
    
    /**
     * 本次应预扣预缴税额（实际扣款）
     * 对应数据库字段：current_withholding_tax
     */
    private BigDecimal currentWithholdingTax;
    
    /**
     * 实发金额
     */
    private BigDecimal netPayment;
    
    // ========== 增值税和附加税计算结果 ==========
    
    /**
     * 增值税额
     */
    private BigDecimal vatAmount;
    
    /**
     * 附加税总额
     */
    private BigDecimal additionalTaxAmount;
    
    /**
     * 城市维护建设税
     */
    private BigDecimal urbanConstructionTax;
    
    /**
     * 教育费附加
     */
    private BigDecimal educationSurcharge;
    
    /**
     * 地方教育附加
     */
    private BigDecimal localEducationSurcharge;
    
    /**
     * 构造方法
     */
    public TaxCalculationResult() {
        this.currentTaxAmount = BigDecimal.ZERO;
        this.currentWithholdingTax = BigDecimal.ZERO;
        this.netPayment = BigDecimal.ZERO;
        this.vatAmount = BigDecimal.ZERO;
        this.additionalTaxAmount = BigDecimal.ZERO;
        this.urbanConstructionTax = BigDecimal.ZERO;
        this.educationSurcharge = BigDecimal.ZERO;
        this.localEducationSurcharge = BigDecimal.ZERO;
    }
    
    /**
     * 构造方法
     */
    public TaxCalculationResult(AccumulatedTaxData newAccumulatedData,
                               BigDecimal currentTaxableAmount,
                               BigDecimal currentTaxAmount,
                               BigDecimal currentWithholdingTax,
                               BigDecimal netPayment) {
        this.newAccumulatedData = newAccumulatedData;
        this.currentTaxAmount = currentTaxAmount != null ? currentTaxAmount : BigDecimal.ZERO;
        this.currentWithholdingTax = currentWithholdingTax != null ? currentWithholdingTax : BigDecimal.ZERO;
        this.netPayment = netPayment != null ? netPayment : BigDecimal.ZERO;
    }
    
    /**
     * 校验计算结果
     */
    public void validate() {
        if (newAccumulatedData == null) {
            throw new IllegalArgumentException("累计数据不能为空");
        }
        
        newAccumulatedData.validate();

        if (currentTaxAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("本期应纳税额不能为负数");
        }
        if (currentWithholdingTax.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("本期应预扣预缴税额不能为负数");
        }
        if (netPayment.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("实发金额不能为负数");
        }
    }
    
    /**
     * 获取计算摘要信息
     */
    public String getSummary() {
        return String.format("本期应纳税额: %s, 本期应预扣预缴税额: %s, 实发金额: %s",
                currentTaxAmount, currentWithholdingTax, netPayment);
    }
    
    /**
     * 判断是否需要扣税
     */
    public boolean needWithholdTax() {
        return currentWithholdingTax.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 获取税负率 (本期应预扣预缴税额 / 应发金额)
     */
    public BigDecimal getTaxRate(BigDecimal payableAmount) {
        if (payableAmount == null || payableAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return currentWithholdingTax.divide(payableAmount, 4, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 获取实发率 (实发金额 / 应发金额)
     */
    public BigDecimal getNetPaymentRate(BigDecimal payableAmount) {
        if (payableAmount == null || payableAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return netPayment.divide(payableAmount, 4, BigDecimal.ROUND_HALF_UP);
    }
    
    @Override
    public String toString() {
        return "TaxCalculationResult{" +
                ", currentTaxAmount=" + currentTaxAmount +
                ", currentWithholdingTax=" + currentWithholdingTax +
                ", netPayment=" + netPayment +
                ", newAccumulatedData=" + newAccumulatedData +
                '}';
    }
}