package com.olading.operate.labor.util.valid;

import jakarta.validation.ConstraintValidator;

import java.lang.annotation.Annotation;

/**
 * 小数位数校验
 */
public abstract class AbstractValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected A constraint;

    @Override
    public void initialize(A constraintAnnotation) {
        this.constraint = constraintAnnotation;
    }
}
