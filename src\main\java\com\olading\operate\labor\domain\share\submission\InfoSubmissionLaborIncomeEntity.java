package com.olading.operate.labor.domain.share.submission;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("人员收入信息报送表")
@Entity
@Table(name = "t_info_submission_labor_income", schema = "olading_labor")
public class InfoSubmissionLaborIncomeEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 20)
    @Comment("开始日期")
    @Column(name = "start_date", length = 20)
    private String startDate;

    @Size(max = 20)
    @Comment("结束日期")
    @Column(name = "end_date", length = 20)
    private String endDate;

    @Size(max = 20)
    @Comment("生成状态")
    @Column(name = "status", length = 20)
    private String status;

    @Size(max = 64)
    @Comment("附件id")
    @Column(name = "file_id", length = 64)
    private String fileId;

    public InfoSubmissionLaborIncomeEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }

    public InfoSubmissionLaborIncomeEntity() {
    }

}