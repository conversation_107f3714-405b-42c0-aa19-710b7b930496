package com.olading.operate.labor.domain.corporation;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.node.LongNode;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.info.InfoManager;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.supplier.PayChannelConfigEntity;
import com.olading.operate.labor.domain.supplier.QPayChannelConfigEntity;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierManager;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import net.bytebuddy.asm.Advice;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class CorporationManager {

    private final EntityManager em;
    private final SupplierManager supplierManager;
    private final InfoManager infoManager;
    private final FileManager fileManager;

    public SupplierCorporationEntity saveCorporation(TenantInfo tenantInfo, CorporationData data) {
        final SupplierEntity supplierEntity = supplierManager.requireSupplier(Long.parseLong(tenantInfo.getId()));
        if(!Objects.equals(supplierEntity.getId(), data.getSupplierId())){
            throw new BusinessException("作业主体信息有误");
        }
        if(data.getSocialCreditCode() == null){
            throw new BusinessException("socialCreditCode不能为空");
        }
        SupplierCorporationEntity corporationEntity = querySupplierCorporation(t -> t.supplierId.eq(data.getSupplierId()).and(t.socialCreditCode.eq(data.getSocialCreditCode()))).fetchOne();
        if(corporationEntity != null && !Objects.equals(corporationEntity.getId(), data.getId())){
            throw new BusinessException("已存在该社会信用代码的作业主体");
        }
        if (data.getId() != null){
            corporationEntity = requireCorporation(data.getId());
        }
        if(corporationEntity == null){
            corporationEntity = new SupplierCorporationEntity(tenantInfo, data.getSupplierId(), data.getName(), data.getSocialCreditCode());
            em.persist(corporationEntity);
        }
        if(data.getName() != null){
            corporationEntity.setName(data.getName());
        }
        if(data.getSocialCreditCode() != null){
            corporationEntity.setSocialCreditCode(data.getSocialCreditCode());
        }
        if(data.getBankAccount() != null){
            corporationEntity.setBankAccount(data.getBankAccount());
        }
        if(data.getCompanyTel() != null){
            corporationEntity.setCompanyTel(data.getCompanyTel());
        }
        if (data.getBankName() != null){
            corporationEntity.setBankName(data.getBankName());
        }
        corporationEntity.setDisabled(data.isDisabled());

        EnterpriseInfoEntity info = infoManager.save(new OwnedByFragment(OwnerType.CORPORATION, corporationEntity.getId()),data.getInfo());
        corporationEntity.setEnterpriseInfoId(info.getId());

        return em.merge(corporationEntity);
    }

    public List<SupplierCorporationEntity> queryCorporationBySupplierId(Long supplierId){
       if(supplierId == null) return List.of();
       return querySupplierCorporation(t -> t.supplierId.eq(supplierId)).fetch();
    }


    public CorporationConfigEntity saveCorporationConfig(TenantInfo tenantInfo, CorporationData data) {
        final SupplierEntity supplierEntity = supplierManager.requireSupplier(Long.parseLong(tenantInfo.getId()));
        if (supplierEntity == null || !Objects.equals(supplierEntity.getId(), data.getSupplierId())){
            throw new BusinessException("作业主体信息有误");
        }
        requireSupplierCorporation(data.getId());
        CorporationConfigEntity entity = queryCorporationConfig(t -> t.supplierCorporationId.eq(data.getId())).fetchOne();
        if(entity == null){
            entity = new CorporationConfigEntity(tenantInfo, data.getSupplierId(), data.getId());
            em.persist(entity);
        }
        if(data.getConfigData().getMinAgeLimit() != null){
            entity.setMinAgeLimit(data.getConfigData().getMinAgeLimit());
        }
        if(data.getConfigData().getInvoiceCategory() != null){
            entity.setInvoiceCategory(data.getConfigData().getInvoiceCategory());
        }
        if(data.getConfigData().getVatStart() != null){
            entity.setVatStart(data.getConfigData().getVatStart());
        }
        if(data.getConfigData().getVatRate() != null){
            entity.setVatRate(data.getConfigData().getVatRate());
        }
        if(data.getConfigData().getSurtaxData() != null){
            entity.setSurtaxData(data.getConfigData().getSurtaxData());
        }
        if (data.getConfigData().getMinAgeLimit() != null){
            entity.setMinAgeLimit(data.getConfigData().getMinAgeLimit());
        }
        if (data.getConfigData().getMaxAgeLimit() != null){
            entity.setMaxAgeLimit(data.getConfigData().getMaxAgeLimit());
        }
        return em.merge(entity);
    }

    public void saveCorporationPayChannel(TenantInfo tenantInfo, CorporationData data) {
        final SupplierEntity supplierEntity = supplierManager.requireSupplier(Long.parseLong(tenantInfo.getId()));
        if (supplierEntity == null || !Objects.equals(supplierEntity.getId(), data.getSupplierId())){
            throw new BusinessException("作业主体信息有误");
        }
        requireSupplierCorporation(data.getId());
        if(CollectionUtil.isNotEmpty(data.getPayChannelDataList())){
            //删除
            queryCorporationPayChannel(t->t.supplierCorporationId.eq(data.getId())).fetch().forEach(em::remove);
            //保存
            data.getPayChannelDataList().forEach(o->{
                final CorporationPayChannelEntity entity = new CorporationPayChannelEntity(tenantInfo, data.getSupplierId(), data.getId());
                entity.setPayChannel(o.getPayChannel());
                entity.setIsDefault(o.getIsDefault());
                entity.setIsOpen(o.getIsOpen());
                entity.setChannelConfig(o.getChannelConfig());
                em.persist(entity);
            });
        }
    }

    public CorporationPayChannelEntity saveCorporationPayChannel(TenantInfo tenantInfo,Long supplierId, CorporationPayChannelData data) {
        final SupplierCorporationEntity corporationEntity = this.requireCorporation(data.getSupplierCorporationId());
        CorporationPayChannelEntity corporationPayChannelEntity = this.queryCorporationPayChannel(t -> t.supplierCorporationId.eq(data.getSupplierCorporationId()).and(t.payChannel.eq(data.getPayChannel()))).fetchOne();
        if(corporationPayChannelEntity == null){
            corporationPayChannelEntity = new CorporationPayChannelEntity(tenantInfo, supplierId, data.getSupplierCorporationId());
            corporationPayChannelEntity.setPayChannel(data.getPayChannel());
        }
        corporationPayChannelEntity.setChannelConfig(JSONUtil.toJsonStr(data.getChannelConfig()));
        corporationPayChannelEntity.setIsOpen(data.getIsOpen());
        return em.merge(corporationPayChannelEntity);
    }

    private SupplierCorporationEntity requireSupplierCorporation(Long id) {
        SupplierCorporationEntity entity = em.find(SupplierCorporationEntity.class, id);
        if (entity == null) {
            throw new IllegalStateException("SupplierCorporationEntity不存在" + id);
        }
        return entity;
    }

    public void editCustomer(long l, CorporationData of) {

    }



    private JPAQuery<SupplierCorporationEntity> querySupplierCorporation(Function<QSupplierCorporationEntity, Predicate> condition) {
        QSupplierCorporationEntity t = QSupplierCorporationEntity.supplierCorporationEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<CorporationConfigEntity> queryCorporationConfig(Function<QCorporationConfigEntity, Predicate> condition) {
        QCorporationConfigEntity t = QCorporationConfigEntity.corporationConfigEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<CorporationPayChannelEntity> queryCorporationPayChannel(Function<QCorporationPayChannelEntity, Predicate> condition) {
        QCorporationPayChannelEntity t = QCorporationPayChannelEntity.corporationPayChannelEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }


    public SupplierCorporationEntity requireCorporation(Long id) {
        SupplierCorporationEntity entity = querySupplierCorporation(t -> t.id.eq(id)).fetchOne();
        if (entity == null) {
            throw new IllegalStateException("没有此作业主体");
        }
        return entity;
    }

    public List<CorporationPayChannelEntity> queryChannelByCorporationId(Long corporationId) {
        return queryCorporationPayChannel(t -> t.supplierCorporationId.eq(corporationId)).fetch();
    }

    public CorporationConfigEntity queryCorporationConfig(Long corporationId) {
        return queryCorporationConfig(t -> t.supplierCorporationId.eq(corporationId)).fetchOne();
    }

    public List<CorporationPayChannelData> queryPayChannelList(Long corporationId) {
        QCorporationPayChannelEntity corporationPayChannel = QCorporationPayChannelEntity.corporationPayChannelEntity;
        QPayChannelConfigEntity payChannelConfig = QPayChannelConfigEntity.payChannelConfigEntity;

        final List<Tuple> fetch = new JPAQuery<>(em)
                .select(corporationPayChannel, payChannelConfig)
                .from(corporationPayChannel).leftJoin(payChannelConfig).on(payChannelConfig.payChannel.eq(corporationPayChannel.payChannel))
                .where(corporationPayChannel.supplierCorporationId.eq(corporationId))
                .fetch();
        return fetch.stream().map(tuple -> new CorporationPayChannelData(tuple.get(corporationPayChannel), tuple.get(payChannelConfig))).collect(Collectors.toList());
    }

    public void setDefaultPayChannel(TenantInfo tenantInfo,Long corporationId, String defaultPayChannel) {
        supplierManager.requireSupplier(Long.parseLong(tenantInfo.getId()));
        final CorporationPayChannelEntity corporationPayChannelEntity = queryCorporationPayChannel(t -> t.supplierCorporationId.eq(corporationId).and(t.payChannel.eq(defaultPayChannel))).fetchOne();
        if(!corporationPayChannelEntity.getIsDefault()){
            QCorporationPayChannelEntity qCorporationPayChannelEntity = QCorporationPayChannelEntity.corporationPayChannelEntity;
            queryCorporationPayChannel(t -> t.supplierCorporationId.eq(corporationId).and(t.isDefault.eq(true))).fetch().forEach(t -> {t.setIsDefault(false);em.merge(t);});
            new JPAQueryFactory(em).update(QCorporationPayChannelEntity.corporationPayChannelEntity).set(qCorporationPayChannelEntity.isDefault, false)
                    .where(qCorporationPayChannelEntity.supplierCorporationId.eq(corporationId)).execute();
            corporationPayChannelEntity.setIsDefault(true);
            em.merge(corporationPayChannelEntity);

        }

    }

    public CorporationPayChannelEntity queryDefaultPayChannel(Long supplierCorporationId) {
        return queryCorporationPayChannel(t->t.supplierCorporationId.eq(supplierCorporationId).and(t.isDefault.eq(true))).fetchOne();
    }

    public CorporationConfigEntity getCorporationConfig(Long corporationId) {
        return queryCorporationConfig(corporationId);
    }
}
