package com.olading.operate.labor.util.excel;

import com.lanmaoly.util.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/11 15:44
 */
public class ExcelColumnChecker {
    private Map<String, Map<String, Integer>> uniqueMap = new HashMap<>();

    /**
     * 重复数据校验
     * @param row
     */
    public void uniqueCheck(ExcelRow row, int rowNum)
    {
        List<ExcelColumnEntity> columns = row.getExcelHeader().getColumns();
        for (ExcelColumnEntity column : columns) {
            String excelTitle = column.getExcelTitle();
            String cellValue = StringUtils.toNoNullString(row.getCellValue(excelTitle));
            if (column.isUnique() && StringUtils.isNotBlank(cellValue)) {
                Map<String, Integer> valueMap = uniqueMap.containsKey(excelTitle) ? uniqueMap.get(excelTitle) : new HashMap<>();
                if (valueMap.containsKey(cellValue)) {
                    row.pushError(excelTitle, String.format("%s与第%s行重复", excelTitle, valueMap.get(cellValue)), null);
                } else {
                    valueMap.put(cellValue, rowNum);
                }

                uniqueMap.put(excelTitle, valueMap);
            }
        }
    }
}
