package com.olading.operate.labor.app.web.biz.enums;

public enum PersonalIncomeTaxDeclareStatusEnum {

    GENERATING("生成中"),
    GENERATED("已生成");

    private final String name;

    PersonalIncomeTaxDeclareStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameByKey(String key) {
        for (PersonalIncomeTaxDeclareStatusEnum e : values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getName();
            }
        }
        return "";
    }
}
