package com.olading.operate.labor.domain.share.customer.vo;

import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.domain.TenantInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.util.List;

@Data
public class CustomerVo {
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "营业执照图片id")
    private String businessLicenseImage;

    @Schema(description = "公司名称")
    private String name;

    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    @Schema(description = "注册地址")
    private String registerAddress;

    @Schema(description = "法定代表人证件正面照")
    private String certificateFrontImage;

    @Schema(description = "法定代表人证件背面照")
    private String certificateBackImage;

    @Schema(description = "法定代表人姓名")
    private String representativeName;

    @Schema(description = "法定代表人证件类型:")
    private CertificateTypeEnum certificateType;

    @Schema(description = "法定代表人证件号")
    private String certificateNo;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系人电话")
    private String contactMobile;

    @Schema(description = "服务商ID")
    private Long supplierId;

    @Schema(description = "公司名称")
    private String shortName;

    @Schema(description = "角色Id")
    private List<Long> roleIds;
}
