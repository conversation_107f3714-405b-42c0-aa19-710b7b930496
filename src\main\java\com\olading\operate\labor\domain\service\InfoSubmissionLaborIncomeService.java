package com.olading.operate.labor.domain.service;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionLaborIncomeEntity;
import com.olading.operate.labor.domain.share.submission.InfoSubmissionLaborIncomeManager;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborIncomeVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class InfoSubmissionLaborIncomeService {

    private final InfoSubmissionLaborIncomeManager infoSubmissionLaborIncomeManager;

    /**
     * 新增人员收入信息报送记录
     */
    public InfoSubmissionLaborIncomeEntity addInfoSubmissionLaborIncome(TenantInfo tenantInfo, InfoSubmissionLaborIncomeVo vo) {
        // 校验必填字段
        validateInfoSubmissionLaborIncomeVo(vo);

        InfoSubmissionLaborIncomeEntity entity = infoSubmissionLaborIncomeManager.addInfoSubmissionLaborIncome(tenantInfo, vo);

        return entity;
    }

    /**
     * 更新人员收入信息报送记录
     */
    public InfoSubmissionLaborIncomeEntity updateInfoSubmissionLaborIncome(TenantInfo tenantInfo, InfoSubmissionLaborIncomeVo vo) {
        InfoSubmissionLaborIncomeEntity entity = infoSubmissionLaborIncomeManager.updateInfoSubmissionLaborIncome(vo);
        return entity;
    }

    /**
     * 查询人员收入信息报送记录详情
     */
    public InfoSubmissionLaborIncomeVo queryInfoSubmissionLaborIncome(Long id) {
        InfoSubmissionLaborIncomeVo vo = infoSubmissionLaborIncomeManager.queryInfoSubmissionLaborIncome(id);
        return vo;
    }

    /**
     * 删除人员收入信息报送记录
     */
    public void deleteInfoSubmissionLaborIncome(TenantInfo tenantInfo, Long id) {
        // 删除记录
        infoSubmissionLaborIncomeManager.deleteInfoSubmissionLaborIncome(id);
    }

    /**
     * 校验人员收入信息报送记录VO
     */
    private void validateInfoSubmissionLaborIncomeVo(InfoSubmissionLaborIncomeVo vo) {
        if (vo.getSupplierCorporationId() == null) {
            throw new BusinessException("作业主体ID不能为空");
        }
        if (vo.getSupplierId() == null) {
            throw new BusinessException("灵工平台ID不能为空");
        }
    }
}
