package com.olading.operate.labor.domain.share.notification;

import com.olading.operate.labor.util.SpringUtils;
import jakarta.annotation.PostConstruct;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.FormBody;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.Reader;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
public class NotificationManager implements AutoCloseable {

    /**
     * 最大并发请求
     */
    private static final int PARALLELISM = 50;

    /**
     * 每个Host的最大并发请求
     */
    private static final int PARALLELISM_PER_HOST = 2;

    private final NotificationRepository notificationRepository;
    private final ThreadPoolExecutor executor;
    private final OkHttpClient client;
    private final AtomicBoolean closed = new AtomicBoolean(false);

    public NotificationManager(NotificationRepository notificationRepository) {
        this.notificationRepository = notificationRepository;
        executor = new ThreadPoolExecutor(1, PARALLELISM, 60, TimeUnit.SECONDS,
                new SynchronousQueue<>());
        Dispatcher dispatcher = new Dispatcher(executor);
        dispatcher.setMaxRequests(PARALLELISM);
        dispatcher.setMaxRequestsPerHost(PARALLELISM_PER_HOST);

        ConnectionPool pool = new ConnectionPool(PARALLELISM_PER_HOST, 20, TimeUnit.SECONDS);

        client = new OkHttpClient.Builder()
                .dispatcher(dispatcher)
                .hostnameVerifier((hostname, sslSession) -> true)
                .connectionPool(pool)
                .connectTimeout(Duration.ofSeconds(5))
                .readTimeout(Duration.ofSeconds(10))
                .writeTimeout(Duration.ofSeconds(10))
                .addInterceptor(this::intercept)
                .build();
    }

    @PostConstruct
    public void post() {

    }

    /**
     * 发送通知
     */
    @Transactional
    public void send(String receiver, String url, Map<String, String> form) {

        // 先入库
        Notification notification = save(new Notification(null, receiver, url, form));

        // 事务提交后发送消息
        SpringUtils.afterCommit(() -> sendAsync(notification));
    }

    private Notification save(Notification notification) {
        return notificationRepository.add(notification);
    }

    public void retryTask() {
        while (retry()) {
        }
    }

    @SneakyThrows
    public boolean retry() {
        List<Notification> list = notificationRepository.loadRetry();
        if (list.isEmpty()) {
            return false;
        } else {
            for (Notification notification : list) {
                sendAsync(notification);
                if (closed.get()) {
                    throw new InterruptedException();
                }
            }
            return true;
        }
    }

    private Response intercept(Interceptor.Chain chain) throws IOException {
        Request request = chain.request();
        Notification notification = request.tag(Notification.class);
        if (notification != null) {
            log.info("开始发送通知, id={}, url={}", notification.getId(), notification.getUrl());
        }
        return chain.proceed(request);
    }

    private void sendAsync(Notification notification) {

        FormBody.Builder builder = new FormBody.Builder();
        notification.getForm().forEach(builder::add);

        Request request = new Request.Builder()
                .url(notification.getUrl())
                .post(builder.build())
                .tag(Notification.class, notification)
                .build();

        client.newCall(request).enqueue(new Callback() {

            @Override
            public void onFailure(Call call, IOException e) {
                log.warn("通知失败, id={}, url={}, message={}", notification.getId(), notification.getUrl(), e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (ResponseBody body = response.body()) {

                    if (body != null) {
                        try (var reader = body.charStream()) {
                            var s = readFirst(reader, 200);
                            if (response.code() == 200 && s.contains("SUCCESS")) {
                                notificationRepository.finishSend(notification);
                            } else {
                                log.info("对端响应未成功: id={}, code={}, body前200字符: {}", notification.getId(), response.code(), s);
                            }
                        }
                    }

                } finally {
                    response.close();
                }
            }
        });
    }

    @Override
    public void close() {
        closed.set(true);
        executor.shutdown();
        try {
            executor.awaitTermination(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {

        }
    }

    /**
     * 读取前n个字符
     */
    private String readFirst(Reader reader, int chars) throws IOException {
        char[] buffer = new char[chars];
        int charactersRead = IOUtils.read(reader, buffer, 0, chars);
        if (charactersRead > 0) {
            return new String(buffer, 0, charactersRead);
        } else {
            return "";
        }
    }

}
