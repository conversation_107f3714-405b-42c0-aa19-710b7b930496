package com.olading.operate.labor.domain.share.protocol;


import com.olading.operate.labor.app.web.biz.enums.EnumContractSignState;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.domain.corporation.CorporationData;
import com.olading.operate.labor.domain.service.SupplierLaborService;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.signing.SignManager;
import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class ProtocolManager {

    private final ProtocolRepository protocolRepository;
    private final SignManager signManager;
    private final SupplierService supplierService;
    private final SupplierLaborService supplierLaborService;

    public void nextStep(CorporationProtocolEntity protocol, String fileId) {
        //查询协议步骤
        List<CorporationProtocolStepEntity> stepList = protocolRepository.queryStepByProtocolId(protocol.getId());
        stepList = stepList.stream().filter(step -> !EnumContractSignState.SUCCESS.equals(step.getSignStatus())).toList();
        if (CollectionUtils.isEmpty(stepList)) {
            if (!EnumContractSignStatus.COMPLETE.equals(protocol.getSignStatus())) {
                protocol.setSignStatus(EnumContractSignStatus.COMPLETE);
                protocol.setFinishDate(LocalDate.now());
                protocol.setProtocolFileId(fileId);
                protocol.setFileId(fileId);
                protocolRepository.updateProtocol(protocol);
                supplierLaborService.updateLaborProtocol(protocol);
                return;
            }
        }
        if (protocol.getSignStatus() == EnumContractSignStatus.CREATED) {
            protocol.setSignStatus(EnumContractSignStatus.SIGNING);
            protocolRepository.updateProtocol(protocol);
        }
        //当前步骤为第一个步骤
        CorporationProtocolStepEntity currStep = stepList.get(0);
//        if (currStep.getSignStatus() == EnumContractSignState.IN_PROCESS) {
//            return;
//        }

        currStep.setCurrentStep(true);
        currStep.setSignStatus(EnumContractSignState.ACCEPT);
        protocolRepository.updateProtocolStep(currStep);

        //如果当前步骤为公章签署人，准备待签署数据，生成签章，执行签署，更新状态为签署中，更新到成功状态后再调用nextstep
        //如果当前步骤为手写签署人，则等待签署
        if (currStep.getOperate().equals(EnumOperateType.SEAL)) {
            prepareSign(protocol, currStep);
            //请求云签签署
            signManager.requestSign(currStep);
            currStep.setSignStatus(EnumContractSignState.IN_PROCESS);

            //静默签后等待签署结果
//            nextStep(protocol);
        }
    }

    private void prepareSign(CorporationProtocolEntity protocol, CorporationProtocolStepEntity currStep) {
        CorporationData corporation = supplierService.getCorporation(currStep.getTenant(), currStep.getSupplierCorporationId());
        String content = corporation.getName();
        String signImage = signManager.buildSignImage(content, currStep);
        signManager.prepareSign(protocol, currStep, signImage, corporation);
    }

}
