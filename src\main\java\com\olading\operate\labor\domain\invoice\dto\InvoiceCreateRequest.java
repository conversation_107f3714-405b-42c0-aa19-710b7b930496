package com.olading.operate.labor.domain.invoice.dto;

import com.olading.operate.labor.domain.invoice.InvoiceType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "开票申请创建请求")
public class InvoiceCreateRequest {
    
    @NotNull(message = "客户ID不能为空")
    @Schema(description = "客户ID")
    private Long customerId;
    
    @NotNull(message = "合同ID不能为空")
    @Schema(description = "合同ID")
    private Long contractId;
    
    @NotNull(message = "发票类型不能为空")
    @Schema(description = "发票类型")
    private InvoiceType type;
    
    @NotBlank(message = "发票抬头不能为空")
    @Schema(description = "发票抬头")
    private String title;
    
    @NotBlank(message = "纳税识别号不能为空")
    @Schema(description = "纳税识别号")
    private String taxNo;
    
    @Schema(description = "开户行")
    private String bankName;
    
    @Schema(description = "银行账号")
    private String bankAccount;
    
    @Schema(description = "注册地址")
    private String registerAddress;
    
    @Schema(description = "企业电话")
    private String companyTel;
    
    @Schema(description = "发票备注")
    private String remark;
    
    @Schema(description = "申请备注")
    private String applyRemark;
    

    @Schema(description = "收件人姓名")
    private String addresseeName;
    

    @Schema(description = "收件人电话")
    private String addresseeMobile;
    

    @Schema(description = "收件人地址")
    private String addresseeAddress;
    
    @Schema(description = "收件人邮箱")
    private String addresseeEmail;
    
    @NotEmpty(message = "开票明细不能为空")
    @Valid
    @Schema(description = "开票明细列表")
    private List<InvoiceItemRequest> items;
}