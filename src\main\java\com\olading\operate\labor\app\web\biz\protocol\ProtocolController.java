package com.olading.operate.labor.app.web.biz.protocol;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.protocol.vo.ProtocolCreateDto;
import com.olading.operate.labor.domain.query.ProtocolQuery;
import com.olading.operate.labor.domain.service.ProtocolService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Tag(name = "合同协议接口")
@RestController
@RequestMapping("/api/supplier/protocol")
@RequiredArgsConstructor
@Slf4j
public class ProtocolController extends BusinessController {

    private final QueryService queryService;

    private final ProtocolService protocolService;

    @PostMapping("generateQrCode")
    @Operation(summary = "生成二维码")
    public void generateQrCode(@RequestBody GenerateQrCodeRequest request, HttpServletResponse response) {
        try {
            QrCodeUtil.generate(request.getContentStr(), 300,300, ImgUtil.IMAGE_TYPE_PNG, response.getOutputStream());
        } catch (Exception e) {
            log.info("二维码生成失败");
        }
    }

    @Operation(summary = "协议列表查询")
    @PostMapping(value = "list")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CORPORATION, spel = "#param.filters.corporationIds")
    })
    public WebApiQueryResponse<ProtocolListVo> protocolList(@RequestBody QueryFilter<ProtocolListFilter> param) {
        Long supplier = currentSupplier().getId();
        param.getFilters().setSupplierId(supplier);
        param.getFilters().setCorporationIds(currentDataScope().get(OwnerType.CORPORATION));
        QueryFilter<ProtocolQuery.Filters> filter = param.convert(ProtocolListFilter::convert);
        DataSet<ProtocolQuery.Record> result = queryService.queryProtocol(filter);
        return WebApiQueryResponse.success(result.getData().stream().map(ProtocolQuery.Record::toVo).toList(), result.getTotal());
    }

    //合同批量发起
    @Operation(summary = "发起签约")
    @RequestMapping(value = "signInit", method = RequestMethod.POST)
    public WebApiResponse<Void> signInit(@RequestBody ProtocolCreateDto protocolCreateDto) {
        TemplateController.SupplierAndUser supplierAndUser = new TemplateController.SupplierAndUser(currentUser(), currentSupplier(), currentTenant());
        protocolService.createProtocol(supplierAndUser, protocolCreateDto);
        return WebApiResponse.success();
    }

    @Operation(summary = "h5个人签署")
    @RequestMapping(value = "sign", method = RequestMethod.POST)
    public WebApiResponse<Void> sign(@RequestBody SignVo signVo) {
        TemplateController.SupplierAndUser supplierAndUser = new TemplateController.SupplierAndUser(currentUser(), currentSupplier(), currentTenant());
        protocolService.sign(signVo.getProtocolId(), signVo.getSignImage(), currentUserId(), supplierAndUser);
        return WebApiResponse.success();
    }

    //人员待签署列表查询
    @Operation(summary = "人员待签署列表查询")
    @PostMapping("/getLaborContract")
    public WebApiResponse<List<LaborProtocolVo>> getLaborContract() {
        TemplateController.SupplierAndUser supplierAndUser = new TemplateController.SupplierAndUser(currentUser(), currentSupplier(), currentTenant());
        List<LaborProtocolVo> laborContract = protocolService.getLaborContract(supplierAndUser);
        return WebApiResponse.success(laborContract);
    }

    @Data
    public static class GenerateQrCodeRequest {
        @Schema(description = "内容")
        @NotNull(message = "内容不能为空")
        @Size(min = 1, max = 200)
        private String contentStr;
    }

    @Data
    public static class SignVo {
        @Schema(description = "协议id")
        private Long protocolId;
        @Schema(description = "签名图片base64,为空则使用默认签名")
        private String signImage;
    }


    @Data
    public static class ProtocolListVo {
        private Long protocolId;
        private String protocolName;
        private String protocolNo;
        private String protocolStatus;
        private String laborName;
        private String laborIdCard;
        private String corporationName;
        private LocalDate startDate;
        private LocalDate endDate;
        private String fileId;
    }

    @Data
    public static class ProtocolListFilter {
        private String protocolNameOrLabor;
        private String protocolName;
        private String protocolStatus;
        private Long supplierId;
        private Set<Long> corporationIds;
        public ProtocolQuery.Filters convert() {
            ProtocolQuery.Filters filters = new ProtocolQuery.Filters();
            filters.setProtocolNameOrLabor(this.protocolNameOrLabor);
            filters.setProtocolName(this.protocolName);
            filters.setProtocolStatus(this.protocolStatus);
            filters.setSupplierId(this.supplierId);
            filters.setCorporationIds(this.corporationIds);
            return filters;
        }
    }

    @Getter
    @Setter
    public static class LaborProtocolVo {

        @Schema(name = "文件名称")
        private String protocolName;
        @Schema(name = "协议id（签署的时候要上传）")
        private Long protocolId;
        @Schema(name = "步骤id")
        private Long stepId;
        @Schema(name = "作业主体编号")
        private Long corporationId;
        @Schema(name = "合同主体名")
        private String corporationName;
        @Schema(name = "合同签约状态")
        private String contractSignStatus;
        @Schema(name = "云签文件编号")
        private Long signContractId;
        @Schema(name = "签名流水号")
        private String flowNo;
        @Schema(name = "流程创建时间")
        private LocalDateTime createdTime;
        @Schema(name = "合同预览文件id")
        private String archiveId;

    }

    @Data
    public static class QueryLaborVo {
        @Schema(name = "身份证号")
        private String idCard;
        @Schema(name = "作业主体id")
        private Long corporationId;
    }
}
