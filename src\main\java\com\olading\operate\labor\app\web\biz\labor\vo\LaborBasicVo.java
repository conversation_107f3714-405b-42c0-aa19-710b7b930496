package com.olading.operate.labor.app.web.biz.labor.vo;

import com.querydsl.core.types.dsl.StringPath;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.util.List;

@Data
public class LaborBasicVo {
    private Long contractId;
    private String idCard;
    private String cellphone;
    private String name;

    public static LaborBasicVo builder(@Nullable Long contractId, String idCard, String cellphone, String name) {
        LaborBasicVo laborBasicVo = new LaborBasicVo();
        laborBasicVo.setCellphone(cellphone);
        laborBasicVo.setContractId(contractId);
        laborBasicVo.setIdCard(idCard);
        laborBasicVo.setName(name);
        return laborBasicVo;
    }
}
