package com.olading.operate.labor.domain.share.signing;

import com.olading.operate.labor.app.web.biz.enums.EnumContractSignState;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.corporation.CorporationData;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolEntity;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolStepEntity;
import com.olading.operate.labor.domain.share.signing.common.FieldValue;
import com.olading.operate.labor.domain.share.signing.enums.*;
import com.olading.operate.labor.domain.share.signing.request.*;
import com.olading.operate.labor.domain.share.signing.response.*;
import com.olading.operate.labor.util.JSONUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class SignManager {

    private final CloudSigningService cloudSigningService;

    private final FileManager fileManager;

    public String downloadFileWaitSign(CorporationProtocolEntity protocol) throws IOException {
            //查询云签
            QueryFileRequest queryFileRequest = new QueryFileRequest();
            queryFileRequest.setId(protocol.getAgentContractId());
            BaseCloudSigningResponse<QueryFileResponse> resp = cloudSigningService.queryFile(queryFileRequest);
            if ("0".equals(resp.getCode())) {
                String downloadUrl = resp.getData().getDownloadUrl();
                //下载云签合同
                try (InputStream inputStream = cloudSigningService.downLoadByUrl(downloadUrl)) {
                    //上传文档到文档服务器
                    return fileManager.save(protocol.getProtocolName()+ ".pdf", inputStream, LocalDateTime.now().plusYears(1), OwnerType.SUPPLIER, String.valueOf(protocol.getSupplierId()));
                } catch (Exception e) {
                    log.error("下载云签合同异常", e);
                    throw new ApiException("下载云签合同异常", ApiException.SYSTEM_ERROR);
                }
            } else {
                log.error("查询云签待签署文件，查询失败，错误码:{}错误信息:{}", resp.getCode(), resp.getMessage());
                throw new ApiException(resp.getMessage(), ApiException.SYSTEM_ERROR);
            }
    }

    public Long createSignFile(CorporationProtocolEntity protocol, List<FieldValue> fieldValues) {
        try {
            CreateSignFileRequest createSignFileRequest = new CreateSignFileRequest();
            createSignFileRequest.setTemplateId(Long.valueOf(protocol.getAgentTemplate()));
            createSignFileRequest.setName(protocol.getProtocolName());
            createSignFileRequest.setFields(fieldValues);
            BaseCloudSigningResponse<CreateSignFileResponse> signFile = cloudSigningService.createSignFile(createSignFileRequest);
            //更新
            if (!"0".equals(signFile.getCode())) {
                log.error("云签-创建待签名文件失败:响应码:{}，响应信息:{}", signFile.getCode(), signFile.getMessage());
            }
            return signFile.getData().getId();
        } catch (Exception e) {
            log.error("请求云签接口异常，请求参数:{},异常:", JSONUtils.json(protocol), e);
        }
        return null;
    }

    public void prepareSign(CorporationProtocolEntity protocol, CorporationProtocolStepEntity currStep, String signImage, CorporationData corporation) {
        PrepareSignRequest prepareSignRequest = new PrepareSignRequest();
        prepareSignRequest.setSignImage(signImage);
        prepareSignRequest.setEnableSmsVerify(EnableSmsVerify.NO);
        prepareSignRequest.setEnableHandSign(EnableHandSign.YES);
        prepareSignRequest.setFileId(String.valueOf(protocol.getAgentContractId()));
        prepareSignRequest.setFlowNo(currStep.getFlowNo());
        prepareSignRequest.setIdNo(corporation.getSocialCreditCode());
        prepareSignRequest.setPhone(corporation.getInfo().getContactPhone());
        prepareSignRequest.setLegalRepresentative(corporation.getInfo().getRepresentativeName());
        prepareSignRequest.setLegalRepresentativeIdNo(corporation.getInfo().getCertificateNo());
        prepareSignRequest.setContactsPhone(corporation.getInfo().getContactPhone());
        prepareSignRequest.setIdType(IdType.SOCIAL_CREDIT_CODE);

        prepareSignRequest.setSigner(corporation.getName());
        prepareSignRequest.setStepName(currStep.getStepName());
        prepareSignData(protocol, currStep, prepareSignRequest);
    }

    private void prepareSignData(CorporationProtocolEntity protocol, CorporationProtocolStepEntity currStep, PrepareSignRequest prepareSignRequest) {
        BaseCloudSigningResponse<PrepareSignResponse> prepareSign;
        try {
            log.info("准备签名数据请求云签,合同Id:{},当前签署步骤:{}", protocol.getId(), currStep.getId());
            prepareSign = cloudSigningService.prepareSign(prepareSignRequest);
        } catch (Exception e) {
            log.error("云签准备签名数据异常:", e);
            throw new ApiException("云签准备签名数据异常", ApiException.SYSTEM_ERROR);
        }
        if (!"0".equals(prepareSign.getCode())) {
            log.error("云签准备签名数据失败，错误码:{},错误信息:{}", prepareSign.getCode(), prepareSign.getMessage());
            throw new ApiException("云签准备签名数据失败,错误信息:" + prepareSign.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }


    public String buildSignImage(String content, CorporationProtocolStepEntity protocolStep) {
        BuildSignImgRequest buildSignImgRequest = new BuildSignImgRequest();
        if (EnumOperateType.SEAL.equals(protocolStep.getOperate())) {
            buildSignImgRequest.setSignType(SignImageType.CIRCLE);
        } else {
            buildSignImgRequest.setSignType(SignImageType.SQUARE);
        }
        buildSignImgRequest.setContent(content);
        buildSignImgRequest.setFormat(ImageFormat.JPG);
        BaseCloudSigningResponse<BuildSignImgResponse> img;
        try {
            img = cloudSigningService.buildSignImg(buildSignImgRequest);
        } catch (IOException e) {
            log.error("签署人生成签章异常:", e);
            throw new ApiException("签署人生成签章失败", ApiException.SYSTEM_ERROR);
        }
        if (!"0".equals(img.getCode())) {
            log.error("签署人生成签章响应错误,错误码:{},错误信息:{}", img.getCode(), img.getMessage());
            throw new ApiException("签署人生成签章失败", ApiException.SYSTEM_ERROR);
        }
        return img.getData().getData();
    }


    public void prepareSign(String signImage, SupplierLaborEntity labor, CorporationProtocolStepEntity currStep, CorporationProtocolEntity protocol) {
        PrepareSignRequest prepareSignRequest = new PrepareSignRequest();
        prepareSignRequest.setSignImage(StringUtils.isNotBlank(signImage)? signImage : buildSignImage(labor.getName(), currStep));
        prepareSignRequest.setEnableSmsVerify(EnableSmsVerify.NO);
        prepareSignRequest.setEnableHandSign(EnableHandSign.YES);
        prepareSignRequest.setFileId(String.valueOf(protocol.getAgentContractId()));
        prepareSignRequest.setFlowNo(currStep.getFlowNo());
        prepareSignRequest.setIdNo(labor.getIdCard());
        prepareSignRequest.setPhone(labor.getCellphone());
        prepareSignRequest.setIdType(IdType.ID_CARD);

        prepareSignRequest.setSigner(labor.getName());
        prepareSignRequest.setStepName(currStep.getStepName());
        prepareSignData(protocol, currStep, prepareSignRequest);
    }

    public String querySignStatusAndFile(CorporationProtocolStepEntity step, CorporationProtocolEntity protocol) {
        QuerySignRequest querySignRequest = new QuerySignRequest();
        querySignRequest.setFlowNo(step.getFlowNo());
        BaseCloudSigningResponse<QuerySignResponse> querySignResponse = cloudSigningService.querySign(querySignRequest);
        if ("0".equals(querySignResponse.getCode())) {
            if (SignStatus.SUCCESS != querySignResponse.getData().getStatus()) {
                log.error("查询签名状态，返回未成功,返回状态:{}", querySignResponse.getData().getStatus());
                throw new ApiException("云签查询签署文件失败,错误信息:" + querySignResponse.getMessage(), ApiException.SYSTEM_ERROR);
            }
            //查询云签签署文件
            QueryFileRequest queryFileRequest = new QueryFileRequest();
            queryFileRequest.setId(protocol.getAgentContractId());
            BaseCloudSigningResponse<QueryFileResponse> queryFile = cloudSigningService.queryFile(queryFileRequest);
            if (!"0".equals(queryFile.getCode())) {
                log.error("云签查询签署文件失败,错误原因:{}", queryFile.getMessage());
                throw new ApiException("云签查询签署文件失败,错误信息:" + queryFile.getMessage(), ApiException.SYSTEM_ERROR);
            }
            String downloadUrl = queryFile.getData().getDownloadUrl();
            try (InputStream inputStream = cloudSigningService.downLoadByUrl(downloadUrl)) {
                return fileManager.save(protocol.getProtocolName() + ".pdf", inputStream, LocalDateTime.now().plusYears(1), OwnerType.SUPPLIER, String.valueOf(protocol.getSupplierId()));
            } catch (Exception e) {
                log.error("签约补单异常", e);
                throw new ApiException("云签查询签署文件失败,错误信息:" + queryFile.getMessage(), ApiException.SYSTEM_ERROR);
            }
        } else {
            log.error("云签签署查询失败,错误码:{},错误信息:{}", querySignResponse.getCode(), querySignResponse.getMessage());
            throw new ApiException("云签查询签署文件失败,错误信息:" + querySignResponse.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    /**
     * 请求签署
     */
    public Boolean requestSign(CorporationProtocolStepEntity currStep) {
        SignRequest signRequest = new SignRequest();
        signRequest.setFlowNo(currStep.getFlowNo());
        BaseCloudSigningResponse<SignResponse> signingResponse;
        try {
            signingResponse = cloudSigningService.sign(signRequest);
        } catch (Exception e) {
            log.error("签署-云签服务异常:", e);
            throw new ApiException("签署服务异常", ApiException.SYSTEM_ERROR);
        }
        log.info("静默签-云签签署结果:{}", JSONUtils.json(signingResponse));
        if ("0".equals(signingResponse.getCode())) {
            //更新合同
            return true;
            //等待异步通知和定时更新状态
        } else {
            throw new ApiException("签署文件云签处理中", ApiException.SYSTEM_ERROR);
        }
    }
}
