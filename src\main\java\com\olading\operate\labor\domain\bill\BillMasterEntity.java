package com.olading.operate.labor.domain.bill;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 账单主表（第一层）- 总账单统计
 */
@Entity
@Table(name = "t_bill_master")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("账单主表")
public class BillMasterEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Comment("账单编号")
    @Column(name = "bill_no", nullable = false, length = 64, unique = true)
    private String billNo;

    @NotNull
    @Comment("供应商ID")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("客户ID")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("客户合同ID")
    @Column(name = "contract_id", nullable = false)
    private Long contractId;

    @NotNull
    @Comment("账单月份")
    @Column(name = "bill_month", nullable = false)
    private LocalDate billMonth;

    @NotNull
    @Comment("账单应收总费用")
    @Column(name = "total_receivable_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal totalReceivableAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("薪酬费用总额")
    @Column(name = "salary_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal salaryAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("管理费总额")
    @Column(name = "management_fee_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal managementFeeAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("其他费用总额")
    @Column(name = "other_fee_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal otherFeeAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("开票总金额")
    @Column(name = "total_invoice_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal totalInvoiceAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("已开票金额")
    @Column(name = "invoiced_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal invoicedAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("已收款金额")
    @Column(name = "received_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal receivedAmount = BigDecimal.ZERO;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Comment("账单状态")
    @Column(name = "bill_status", nullable = false, length = 20)
    private BillMasterStatus billStatus = BillMasterStatus.GENERATING;

    @Comment("确认时间")
    @Column(name = "confirm_time")
    private LocalDateTime confirmTime;

    @Comment("确认人ID")
    @Column(name = "confirm_user_id")
    private Long confirmUserId;

    @Comment("备注")
    @Column(name = "remark", length = 500)
    private String remark;

    public BillMasterEntity(TenantInfo tenantInfo) {
        setTenant(tenantInfo);
    }

    protected BillMasterEntity() {
    }


}