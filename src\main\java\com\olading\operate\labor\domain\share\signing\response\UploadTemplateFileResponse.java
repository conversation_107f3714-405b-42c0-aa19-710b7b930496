package com.olading.operate.labor.domain.share.signing.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "上传模板文件响应参数")
public class UploadTemplateFileResponse {

    @Schema(description = "结果" )
    public Boolean success;
    @Schema(description = "消息" )
    private UploadTemplateFileMessage message;
}
