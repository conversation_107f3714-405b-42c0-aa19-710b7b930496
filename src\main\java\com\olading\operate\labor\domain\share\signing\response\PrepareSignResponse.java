package com.olading.operate.labor.domain.share.signing.response;


import com.olading.operate.labor.domain.share.signing.enums.SignStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Schema(description= "准备签名数据响应参数")
public class PrepareSignResponse {

    @Schema(description = "可以预览且签名的url地址", required = true)
    private String signUrl;
    @Schema(description = "签名状态")
    private SignStatus status;
    @Schema(description = "受理时间")
    private LocalDateTime acceptTime;
    @Schema(description = "移动端可以预览且签名的url地址")
    private String signUrlMobile;
}
