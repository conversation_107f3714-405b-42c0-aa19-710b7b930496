package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.share.authority.QRoleEntity;
import com.olading.operate.labor.domain.share.authority.QRoleMemberEntity;
import com.olading.operate.labor.domain.share.authority.QSupplierMemberEntity;
import com.olading.operate.labor.domain.share.authority.RoleEntity;
import com.olading.operate.labor.domain.share.authority.RoleMemberEntity;
import com.olading.operate.labor.domain.share.authority.SupplierMemberEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

public class SupplierMemberQuery implements EntityQuery<QueryFilter<SupplierMemberQuery.Filters>, SupplierMemberQuery.Record> {

    private final QRoleMemberEntity t2 = QRoleMemberEntity.roleMemberEntity;
    private final QSupplierMemberEntity t1 = QSupplierMemberEntity.supplierMemberEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();
        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t1.supplierId.eq(filters.getFilters().getSupplierId()));
        }
        if (filters.getFilters().getRoleId() != null) {
            criteria.and(t2.roleId.eq(filters.getFilters().getRoleId()));
        }
        if (filters.getFilters().getMemberId() != null) {
            criteria.and(t1.id.in(filters.getFilters().getMemberId()));
        }
        if (filters.getFilters().getNameOrCellphone() != null) {
            criteria.and(
                    t1.name.like(JpaUtils.fullLike(filters.getFilters().getNameOrCellphone()))
                            .or(t1.cellphone.like(JpaUtils.fullLike(filters.getFilters().getNameOrCellphone())))
            );
        }
        query.select(t1)
                .from(t1);
        if(filters.getFilters().getRoleId() != null){
            query.leftJoin(t2).on(t2.subjectId.eq(t1.id));
        }
        query.where(criteria);
    }

    @Override
    public Record transform(Object v) {
        SupplierMemberEntity supplierMember = (SupplierMemberEntity)v;
        Record record = new Record();
        record.setName(supplierMember.getName());
        record.setCellphone(supplierMember.getCellphone());
        record.setCreateTime(supplierMember.getCreateTime());
        record.setModifyTime(supplierMember.getModifyTime());
        record.setId(supplierMember.getId());
        record.setDisabled(supplierMember.getDisabled());
        return record;
    }

    @Data
    public static class Filters {


        @Schema(description = "角色ID")
        private Long roleId;
        @Schema(description = "成员ID")
        private Long memberId;
        @Schema(description = "姓名手机号")
        private String nameOrCellphone;
        private Long supplierId;
    }

    @Data
    public static class Record {
        private Long id;
        private String name;
        private String cellphone;
        private LocalDateTime createTime;
        private LocalDateTime modifyTime;
        private List<RoleEntity> roles;
        private boolean disabled;
    }

}
