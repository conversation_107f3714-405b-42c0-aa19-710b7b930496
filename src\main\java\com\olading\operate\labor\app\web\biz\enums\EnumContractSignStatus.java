package com.olading.operate.labor.app.web.biz.enums;

/**
 * 合同签署状态
 */
public enum EnumContractSignStatus {

    /**
     * 草稿（初始创建完成，还未进行处理）
     */
    DRAFT("创建中"),
    /**
     * 已发起
     */
    CREATED("已发起"),

    /**
     * 待签署
     */
    WAIT_SIGN("签署中"),

    /**
     * 签署中
     */
    SIGNING("签署中"),

    /**
     * 终止（协议未签署过期系统自动判断）
     */
    ABORT("被退回"),

    /**
     * 作废
     */
    CANCEL("已废弃"),

    /**
     * 已到期
     */
    EXPIRED("已终止"),

    /**
     * 完成
     */
    COMPLETE("已完成"),

    /**
     * 异步创建失败
     */
    ASYNC_CREATE_FAIL("异步创建失败"),
    /**
     * 已撤回
     */
    WITHDRAWN("已撤回");

    private String key;

    EnumContractSignStatus(String key){
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}
