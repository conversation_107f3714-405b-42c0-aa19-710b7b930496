package com.olading.operate.labor.domain.bill.dto;

import com.olading.operate.labor.domain.bill.BillMasterStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 账单查询请求
 */
@Data
@Schema(description = "账单查询请求")
public class BillQueryRequest {

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "合同ID")
    private Long contractId;

    @Schema(description = "账单编号")
    private String billNo;

    @Schema(description = "账单月份开始")
    private LocalDate billMonthStart;

    @Schema(description = "账单月份结束")
    private LocalDate billMonthEnd;

    @Schema(description = "账单状态")
    private BillMasterStatus billStatus;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "合同名称")
    private String contractName;

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "20")
    private Integer size = 20;
}