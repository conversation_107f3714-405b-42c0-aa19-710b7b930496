package com.olading.operate.labor.app.aspect;

import com.olading.operate.labor.domain.share.info.OwnerType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 数据权限校验注解，支持 SpEL 表达式提取字段值。
 *
 * 功能                 实现方式
 * 支持 SpEL 表达式      使用 SpelExpressionParser 解析
 * 多种写法兼容           #request.id, #arg0.id, #id 等
 * 嵌套字段支持           #request.corporation.id
 * 集合权限支持           T(java.util.Arrays).asList(#ids)
 * 参数自动绑定           使用 StandardEvaluationContext 绑定方法参数
 *
 *
 *
 * 写法                  含义
 * #request.id          提取名为 request 的参数的 id 字段
 * #corporation.id      提取名为 corporation 的参数的 id 字段
 * #arg0.id             提取第一个参数的 id 字段
 * #id                  直接使用变量名id
 * T(java.util.Arrays).asList(#ids)   将 #ids 转为集合处理
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuthorityDataScopGuard {

    /**
     * 权限类型与 SpEL 表达式的映射关系  用于编辑修改等方法
     */
    Mapping[] value() default {};

    @interface Mapping {
        OwnerType type();       // 数据权限类型，如 CORPORATION
        String spel();         // SpEL 表达式，如 "#request.id"
        boolean allowNull() default true; //允许字段为空或不存在
    }

    /**
     * 权限类型与 SpEL 表达式的映射关系 用于列表查询方法 会进行权限过滤，如果空值会把当前用户的权限数据进行注入,字段必须为Set<Long> 类型
     */
    QueryMapping[] query_value()  default {};

    @interface QueryMapping {
        OwnerType type();       // 数据权限类型，如 CORPORATION
        String spel();         // SpEL 表达式，如 "#request.id"
        boolean allowNull() default true; //允许字段为空或不存在
    }

    /**
     * 返回值字段校验映射
     */
    ReturnMapping[] return_value() default {};

    @interface ReturnMapping {
        OwnerType type();       // 数据类型，如 CORPORATION, CONTRACT 等
        String spel();          // SpEL 表达式，指向返回值中的字段
        boolean allowNull() default true; //允许字段为空或不存在
    }

}
