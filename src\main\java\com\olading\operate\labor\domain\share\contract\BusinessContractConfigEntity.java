package com.olading.operate.labor.domain.share.contract;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

@Getter
@Setter
@Comment("服务合同配置")
@Entity
@Table(name = "t_business_contract_config", schema = "olading_labor")
public class BusinessContractConfigEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("客户id")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Comment("作业主体id")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("合同名称")
    @Column(name = "contract_id", nullable = false)
    private Long contractId;

    @Size(max = 64)
    @Comment("抬头")
    @Column(name = "invoice_title", length = 64)
    private String invoiceTitle;

    @Size(max = 64)
    @Comment("纳税识别号")
    @Column(name = "invoice_tax_no", length = 64)
    private String invoiceTaxNo;

    @Size(max = 64)
    @Comment("开户行")
    @Column(name = "invoice_bank_name", length = 64)
    private String invoiceBankName;

    @Size(max = 64)
    @Comment("账号")
    @Column(name = "invoice_bank_account", length = 64)
    private String invoiceBankAccount;

    @Size(max = 64)
    @Comment("注册地址")
    @Column(name = "invoice_register_address", length = 64)
    private String invoiceRegisterAddress;

    @Size(max = 64)
    @Comment("企业电话")
    @Column(name = "invoice_company_tel", length = 64)
    private String invoiceCompanyTel;

    @Size(max = 500)
    @Comment("发票备注")
    @Column(name = "invoice_remark", length = 500)
    private String invoiceRemark;

    @Size(max = 20)
    @Comment("计算规则")
    @Column(name = "manage_calculation_rule", length = 20)
    private String manageCalculationRule;

    @Comment("金额")
    @Column(name = "manage_amount", precision = 16, scale = 2)
    private BigDecimal manageAmount;

    @Comment("费率")
    @Column(name = "manage_rate", precision = 16, scale = 2)
    private BigDecimal manageRate;

}