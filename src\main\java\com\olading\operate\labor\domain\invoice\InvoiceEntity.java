package com.olading.operate.labor.domain.invoice;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Comment("开票")
@Entity
@Table(name = "t_invoice")
public class InvoiceEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    
    public InvoiceEntity() {}
    
    public InvoiceEntity(TenantInfo tenantInfo) {
        setTenant(tenantInfo);
    }

    @NotNull
    @Comment("灵工平台id")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("客户id")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("合同id")
    @Column(name = "contract_id", nullable = false)
    private Long contractId;

    @Size(max = 64)
    @NotNull
    @Comment("申请编号")
    @Column(name = "sn", nullable = false, length = 64)
    private String sn;


    @Comment("账单月份")
    @Column(name = "bill_month", nullable = false)
    private LocalDate billMonth;

    @NotNull
    @Comment("状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 64)
    private InvoiceStatus status;

    @NotNull
    @Comment("发票类型-SPECIAL(专票)/GENERAL(普票)")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 64)
    private InvoiceType type;

    @NotNull
    @Comment("开票金额")
    @Column(name = "fee", nullable = false, precision = 12, scale = 2)
    private BigDecimal fee;

    @Size(max = 64)
    @NotNull
    @Comment("抬头")
    @Column(name = "title", nullable = false, length = 64)
    private String title;

    @Size(max = 64)
    @NotNull
    @Comment("纳税识别号")
    @Column(name = "tax_no", nullable = false, length = 64)
    private String taxNo;

    @Size(max = 64)
    @NotNull
    @Comment("开户行")
    @Column(name = "bank_name", nullable = false, length = 64)
    private String bankName;

    @Size(max = 64)
    @NotNull
    @Comment("银行账号")
    @Column(name = "bank_account", nullable = false, length = 64)
    private String bankAccount;

    @Size(max = 64)
    @NotNull
    @Comment("注册地址")
    @Column(name = "register_address", nullable = false, length = 64)
    private String registerAddress;

    @Size(max = 64)
    @NotNull
    @Comment("企业电话")
    @Column(name = "company_tel", nullable = false, length = 64)
    private String companyTel;

    @Size(max = 500)
    @Comment("发票备注")
    @Column(name = "remark", nullable = false, length = 500)
    private String remark;

    @Size(max = 500)
    @Comment("申请备注")
    @Column(name = "apply_remark", length = 500)
    private String applyRemark;

    @Size(max = 64)
    @Comment("收件人姓名")
    @Column(name = "addressee_name", nullable = false, length = 64)
    private String addresseeName;

    @Size(max = 64)
    @Comment("收件人电话")
    @Column(name = "addressee_mobile", nullable = false, length = 64)
    private String addresseeMobile;

    @Size(max = 64)
    @Comment("收件人地址")
    @Column(name = "addressee_address", nullable = false, length = 64)
    private String addresseeAddress;

    @Size(max = 64)
    @Comment("收件人邮箱")
    @Column(name = "addressee_email", length = 64)
    private String addresseeEmail;

    @Size(max = 300)
    @Comment("作废原因")
    @Column(name = "invalid_reason", length = 300)
    private String invalidReason;

    @Size(max = 300)
    @Comment("退回原因")
    @Column(name = "back_reason", length = 300)
    private String backReason;

    @Size(max = 256)
    @Comment("发票文件")
    @Column(name = "invoice_file", length = 256)
    private String invoiceFile;

}