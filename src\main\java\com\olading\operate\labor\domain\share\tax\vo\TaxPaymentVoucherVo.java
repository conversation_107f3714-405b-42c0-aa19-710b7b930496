package com.olading.operate.labor.domain.share.tax.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TaxPaymentVoucherVo {
    @Schema(description = "税务缴纳凭证ID")
    private Long id;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "税款所属期")
    private String taxPaymentPeriod;

    @Schema(description = "附件ID，逗号分隔")
    private String fileIds;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "灵工平台ID")
    private Long supplierId;

}
