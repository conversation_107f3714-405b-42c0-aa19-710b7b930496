package com.olading.operate.labor.domain.proxy;

import com.olading.operate.labor.domain.proxy.order.ProxyBatchEntity;
import com.olading.operate.labor.domain.proxy.order.ProxyBatchStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProxyOrderBatchDetailData {

    @Schema(description = "批次id")
    private Long batchId;

    @Schema(description = "灵工平台id")
    private Long supplierId;

    @Schema(description = "客户名称")
    private String customer;

    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "作业主体名称")
    private String corporation;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "合同id")
    private Long contractId;

    @Schema(description = "服务合同名称")
    private String businessContract;

    @Schema(description = "工资批次id")
    private Long salaryStatementId;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "通道编码")
    private String payChannel;

    @Schema(description = "总笔数")
    private Long count;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "确认出款时间")
    private LocalDateTime confirmTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "错误信息")
    private String lastErrorInfo;

    @Schema(description = "批次状态 CHECK/PROCESSING/COMPLETE")
    private ProxyBatchStatusEnum batchStatus;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "核验成功笔数")
    private Long checkSuccCount;
    @Schema(description = "核验成功金额")
    private BigDecimal checkSuccAmount;

    @Schema(description = "核验失败笔数")
    private Long checkFailCount;
    @Schema(description = "核验失败金额")
    private BigDecimal checkFailAmount;

    @Schema(description = "核验中笔数")
    private Long createCount;
    @Schema(description = "核验中金额")
    private BigDecimal createAmount;

    @Schema(description = "作业主体联系人手机号")
    private String corporationContactPhone;


    public static ProxyOrderBatchDetailData of(ProxyBatchEntity batchEntity) {
        ProxyOrderBatchDetailData batchData = new ProxyOrderBatchDetailData();
        batchData.setBatchId(batchEntity.getId());
        batchData.setSupplierId(batchEntity.getSupplierId());
        batchData.setCustomerId(batchEntity.getCustomerId());
        batchData.setSupplierCorporationId(batchEntity.getSupplierCorporationId());
        batchData.setContractId(batchEntity.getContractId());
        batchData.setSalaryStatementId(batchEntity.getSalaryStatementId());
        batchData.setTotalAmount(batchEntity.getTotalAmount());
        batchData.setPayChannel(batchEntity.getPayChannel());
        batchData.setCount(batchEntity.getCount());
        batchData.setConfirmTime(batchEntity.getConfirmTime());
        batchData.setCompleteTime(batchEntity.getCompleteTime());
        batchData.setLastErrorInfo(batchEntity.getLastErrorInfo());
        batchData.setBatchStatus(batchEntity.getBatchStatus());
        batchData.setRemark(batchEntity.getRemark());
        batchData.setCreateTime(batchEntity.getCreateTime());
        return batchData;
    }



}
