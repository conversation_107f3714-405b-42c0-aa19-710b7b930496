package com.olading.operate.labor.domain.share.submission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InfoSubmissionLaborIncomeVo {
    @Schema(description = "人员收入信息报送记录ID")
    private Long id;

    @Schema(description = "灵工平台ID")
    private Long supplierId;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

    @Schema(description = "生成状态")
    private String status;

    @Schema(description = "生成状态描述")
    private String statusDesc;

    @Schema(description = "附件ID")
    private String fileId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;
}
