package com.olading.operate.labor.domain.share.authority;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.util.Arrays;
import java.util.List;

/**
 * 角色
 */
@Table(name = "t_role")
@Entity
public class RoleEntity extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Long id;

    @Size(max = 20)
    @Comment("角色名")
    @Column(name = "name", length = 20)
    private String name;

    @Size(max = 10)
    @Comment("角色编码")
    @Column(name = "code", length = 10)
    private String code;

    @Size(max = 200)
    @Comment("备注")
    @Column(name = "remark", length = 200)
    private String remark;

    @Lob
    @Comment("角色拥有的功能权限")
    @Column(name = "authorities", length = 17000000)
    private String authorities;

    @NotNull
    @Comment("是否禁用")
    @ColumnDefault("b'0'")
    @Column(name = "disabled", nullable = false)
    private Boolean disabled = false;

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }


    public RoleEntity(TenantInfo tenant, String name) {
        this.setTenant(tenant);
        this.name = name;
    }

    protected RoleEntity() {
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getRemark() {
        return remark;
    }

    void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getAuthorities() {
        return Arrays.asList(StringUtils.split(StringUtils.defaultString(authorities), ","));
    }

    void setAuthorities(List<String> authorities) {
        this.authorities = StringUtils.join(authorities, ",");
    }

    }
