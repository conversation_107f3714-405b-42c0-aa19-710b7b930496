package com.olading.operate.labor.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.lanmaoly.util.lang.StringUtils;
import com.lanmaoly.util.lang.exception.ValidationException;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.app.web.biz.enums.TaxDeclareStatusEnum;
import com.olading.operate.labor.app.web.biz.salary.SalaryCalculateController;
import com.olading.operate.labor.app.web.biz.salary.vo.ImportPayrollStaffRow;
import com.olading.operate.labor.app.web.biz.salary.vo.ImportPreviousIncomeRow;
import com.olading.operate.labor.app.web.biz.salary.vo.PreviousIncomeImportRequest;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.salary.*;
import com.olading.operate.labor.domain.salary.engine.SalaryTaxCalculationService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.LaborInfoRepository;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareEntity;
import com.olading.operate.labor.domain.share.tax.PersonalIncomeTaxDeclareManager;
import com.olading.operate.labor.util.excel.ExcelRow;
import com.olading.operate.labor.util.validation.validator.IDNumberValidator;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class SalaryCalculateService {

    private static final Pattern PHONE_NUMBER_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    private final LaborInfoRepository laborInfoRepository;
    private final CorporationManager corporationManager;
    private final SalaryManager salaryManager;
    private final SalaryTaxCalculationService salaryTaxCalculationService;
    private final PersonalIncomeTaxDeclareManager personalIncomeTaxDeclareManager;

    private static final IDNumberValidator idNumberValidator = new IDNumberValidator();
    private static final String AMOUNT_REGEX = "^(?!0\\d)\\d*(\\.\\d{1,2})?$";
    private static final String ERROR_MSG = "%s格式错误，应为正数且最多两位小数（不能以0开头）";

    public void verifyImportPayrollStaffRow(SalaryCalculateController.payrollAddRequest request, List<ImportPayrollStaffRow> results, Map<OwnerType, Set<Long>> ownerTypeSetMap) {

        // 校验excel中人员是否重复
        validateUniquePayrollStaff(results);

        for (ImportPayrollStaffRow result : results) {
            if (StringUtils.isBlank(result.getName())) {
                result.pushError(result.getName(), "姓名不能为空", "");
                continue;
            }
            if (StringUtils.isBlank(result.getIdCard())) {
                result.pushError(result.getIdCard(), "身份证号不能为空", "");
                continue;
            }


            //校验文件中的数字金额格式
            if (!validateAddPayrollAmount(result)){
                continue;
            }
            if(StringUtils.isBlank(result.getAmount()) || BigDecimal.ZERO.compareTo(new BigDecimal(result.getAmount())) == 0){
                result.pushError(result.getIdCard(), "应发金额不能为0", "");
                continue;
            }
            try {
                // 调用验证方法并捕获异常
                if (!idNumberValidator.validate(result.getIdCard())) {
                    result.pushError(result.getIdCard(), "身份证号格式不正确", "");
                    continue;
                }
            } catch (ValidationException e) {
                // 捕获验证异常并添加错误信息
                result.pushError(result.getIdCard(), "身份证号验证失败: " + e.getMessage(), "");
                continue;
            }
            //校验作业主体年龄范围
            if (!validateAge(result,request.getSupplierCorporationId())){
                continue;
            }
            // 校验员工与作业主体是否签约
            if (!validateSignStatus(result,request.getSupplierCorporationId())){
                continue;
            }

            //校验人员是否在服务合同下
            SupplierLaborEntity laborEntity = laborInfoRepository.findLaborByIdCard(result.getIdCard(), request.getSupplierId());
            if (laborEntity == null || laborEntity.isDeleted()) {
                result.pushError(result.getName(), "未找到该员工", "");
                continue;
            }

            LaborInfoEntity laborInfoEntity = laborInfoRepository.findLaborInfoByCorporationAndContract(request.getSupplierCorporationId(), laborEntity.getId(), request.getContractId());
            if (laborInfoEntity == null) {
                result.pushError(result.getName(), "人员不在服务合同下", "");
                continue;
            }
            result.setCellPhone(laborEntity.getCellphone());
        }

    }

    private void validateUniquePayrollStaff(List<ImportPayrollStaffRow> results) {
        Map<String, Integer> uniqueKeyMap = new HashMap<>();
        for (int i = 0; i < results.size(); i++) {
            ImportPayrollStaffRow result = results.get(i);
            String uniqueKey = result.getIdCard() + "|" + result.getName();

            if (uniqueKeyMap.containsKey(uniqueKey)) {
                int firstRowNum = uniqueKeyMap.get(uniqueKey);
                //throw new ApiException(String.format("第%d行与第%d行数据重复（姓名：%s，身份证号：%s）", i + 1, firstRowNum + 1, result.getName(), result.getIdCard()), ApiException.API_PARAM_ERROR);
                throw new ApiException("人员信息重复", ApiException.API_PARAM_ERROR);
            }
            uniqueKeyMap.put(uniqueKey, i);
        }
    }

    private boolean validateAddPayrollAmount(ImportPayrollStaffRow result) {
        return validateAmount(result.getAmount(), "金额", result) &&
                validateAmount(result.getCurrentDeductionIncome(), "当期免税收入", result) &&
                validateAmount(result.getOtherDeduction(), "其他扣除", result) &&
                validateAmount(result.getReductionTax(), "减免税额", result);
        }

    private boolean validateSignStatus(ImportPayrollStaffRow result, @NotNull(message = "作业主体不能为空") Long supplierCorporationId) {
        LaborProtocolEntity laborProtocolEntity = laborInfoRepository.findLaborProtocolByCardAndSupplierCorporationId(result.getIdCard(), supplierCorporationId);
        if (laborProtocolEntity == null || laborProtocolEntity.isDeleted() || !EnumContractSignStatus.COMPLETE.name().equals(laborProtocolEntity.getProtocolStatus())) {
            result.pushError(result.getName(), "人员未签约", "");
            return false;
        }
        return true;
    }

    private boolean validateAge(ImportPayrollStaffRow result, Long supplierCorporationId) {
        // 从身份证提取出生日期
        String idCard = result.getIdCard();
        String birthDateStr = null;

        // 处理15位和18位身份证
        if (idCard.length() == 15) {
            birthDateStr = "19" + idCard.substring(6, 12);  // 15位身份证：6-12位是YYMMDD
        } else if (idCard.length() == 18) {
            birthDateStr = idCard.substring(6, 14);         // 18位身份证：6-14位是YYYYMMDD
        }

        if (birthDateStr == null) {
            // 非标准长度身份证处理
            result.pushError(result.getIdCard(), "不支持的身份证号长度", "");
            return  false;
        }

        try {
            // 解析出生日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
            LocalDate currentDate = LocalDate.now();

            // 计算周岁年龄
            Period period = Period.between(birthDate, currentDate);
            int age = period.getYears();

            // 获取作业主体配置
            CorporationConfigEntity config = corporationManager.queryCorporationConfig(supplierCorporationId);

            // 处理年龄限制（支持单边界和双边界校验）
            return validateAgeAgainstConfig(result, age, config);

        } catch (DateTimeParseException e) {
            // 日期解析失败处理
            result.pushError(result.getIdCard(), "身份证号包含无效的出生日期", "");
            return false;
        }
    }

    private boolean validateAgeAgainstConfig(ImportPayrollStaffRow result, int age, CorporationConfigEntity config) {
        Integer minAge = config.getMinAgeLimit();
        Integer maxAge = config.getMaxAgeLimit();

        // 如果没有任何年龄限制，直接返回
        if (minAge == null && maxAge == null) {
            return  true;
        }

        // 检查是否超出年龄范围
        boolean outOfRange = (minAge != null && age < minAge) ||
                (maxAge != null && age > maxAge);

        if (!outOfRange) {
            return  true; // 年龄在有效范围内
        }

        // 构建友好的范围描述
        String rangeDesc;
        if (minAge != null && maxAge != null) {
            rangeDesc = minAge + "周岁-" + maxAge + "周岁";
        } else if (minAge != null) {
            rangeDesc = "≥" + minAge + "周岁";
        } else {
            rangeDesc = "≤" + maxAge + "周岁";
        }

        // 添加错误信息
        result.pushError(
                result.getIdCard(),
                "年龄需在作业主体年龄范围: " + rangeDesc + " 之间（当前年龄：" + age + "周岁）",
                ""
        );
        return false;
    }

    private boolean isValidPhoneNumber(String phoneNumber) {
        return PHONE_NUMBER_PATTERN.matcher(phoneNumber).matches();
    }

    @Transactional(rollbackFor = Exception.class)
    public void addPayrollStaff(SalaryCalculateController.payrollAddRequest request, List<ImportPayrollStaffRow> successDataList, TenantInfo tenant) {

        YearMonth yearMonth = YearMonth.parse(request.getTaxPeriod());

        SalaryStatementEntity salaryStatementEntity = new SalaryStatementEntity(tenant);
        salaryStatementEntity.setSupplierId(request.getSupplierId());
        salaryStatementEntity.setCustomerId(request.getCustomerId());
        salaryStatementEntity.setContractId(request.getContractId());
        salaryStatementEntity.setSupplierCorporationId(request.getSupplierCorporationId());
        salaryStatementEntity.setTaxPeriod(request.getTaxPeriod());
        salaryStatementEntity.setTaxDeclarationMonth(yearMonth.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM")));
        salaryStatementEntity.setTotalPeople((long) successDataList.size());
        salaryStatementEntity.setStatus(SalaryStatementStatus.CALCULATING);
        salaryStatementEntity.setUploadTime(LocalDateTime.now());
        salaryManager.saveSalaryStatement(salaryStatementEntity);

        Long salaryStatementEntityId = salaryStatementEntity.getId();
        successDataList.forEach(model -> {
            SalaryDetailEntity salaryDetailEntity = new SalaryDetailEntity(tenant);
            salaryDetailEntity.setSalaryStatementId(salaryStatementEntityId);
            salaryDetailEntity.setName(model.getName());
            salaryDetailEntity.setIdCard(model.getIdCard());
            salaryDetailEntity.setPhoneNumber(model.getCellPhone());
            salaryDetailEntity.setPayableAmount(new BigDecimal(model.getAmount()));

            salaryDetailEntity.setTaxPeriod(request.getTaxPeriod());
            salaryDetailEntity.setPayableAmount(new BigDecimal(StringUtils.isBlank(model.getAmount()) ? "0.00" : model.getAmount()));
            salaryDetailEntity.setTaxFreeIncome(new BigDecimal(StringUtils.isBlank(model.getCurrentDeductionIncome()) ? "0.00" : model.getCurrentDeductionIncome()));
            salaryDetailEntity.setOtherDeductions(new BigDecimal(StringUtils.isBlank(model.getOtherDeduction()) ? "0.00" : model.getOtherDeduction()));
            salaryDetailEntity.setTaxReliefAmount(new BigDecimal(StringUtils.isBlank(model.getReductionTax()) ? "0.00" : model.getReductionTax()));
            salaryDetailEntity.setSupplierCorporationId(request.getSupplierCorporationId());
            salaryDetailEntity.setAccumulatedIncome(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedExpenses(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedDeductionExpenses(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxFreeIncome(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedOtherDeductions(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxRelief(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxableAmount(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedTaxAmount(BigDecimal.ZERO);
            salaryDetailEntity.setAccumulatedPrepaidTax(BigDecimal.ZERO);
            salaryDetailEntity.setCurrentTaxAmount(BigDecimal.ZERO);
            salaryDetailEntity.setCurrentWithholdingTax(BigDecimal.ZERO);
            salaryDetailEntity.setNetPayment(BigDecimal.ZERO);
            salaryDetailEntity.setVatAmount(BigDecimal.ZERO);
            salaryDetailEntity.setAdditionalTaxAmount(BigDecimal.ZERO);
            salaryDetailEntity.setUrbanConstructionTax(BigDecimal.ZERO);
            salaryDetailEntity.setEducationSurcharge(BigDecimal.ZERO);
            salaryDetailEntity.setLocalEducationSurcharge(BigDecimal.ZERO);



            salaryManager.saveSalaryDetail(salaryDetailEntity);
        });
    }

    public void verifyPreviousIncomeInfo(PreviousIncomeImportRequest request, List<ImportPreviousIncomeRow> results, Map<OwnerType, Set<Long>> ownerTypeSetMap) {
        for (ImportPreviousIncomeRow result : results) {
            if (StringUtils.isBlank(result.getName())) {
                result.pushError(result.getName(), "姓名不能为空", "");
                continue;
            }
            if (StringUtils.isBlank(result.getIdCard())) {
                result.pushError(result.getIdCard(), "身份证号不能为空", "");
                continue;
            }

            try {
                // 调用验证方法并捕获异常
                if (!idNumberValidator.validate(result.getIdCard())) {
                    result.pushError(result.getIdCard(), "身份证号格式不正确", "");
                    continue;
                }
            } catch (ValidationException e) {
                // 捕获验证异常并添加错误信息
                result.pushError(result.getIdCard(), "身份证号验证失败: " + e.getMessage(), "");
                continue;
            }

            if (!validatePreviousIncome(result)){
                continue;
            }

            SupplierLaborEntity laborEntity = laborInfoRepository.findUniqueLaborByIdCard(request.getSupplierCorporationId(), result.getIdCard(), result.getName());
            if (laborEntity == null || laborEntity.isDeleted()) {
                result.pushError(result.getName(), "未找到该员工", "");
            }
        }

    }

    private boolean validatePreviousIncome(ImportPreviousIncomeRow result) {
        return validateAmount(result.getTotalIncome(), "累计收入", result) &&
                validateAmount(result.getTotalExpenses(), "累计费用", result) &&
                validateAmount(result.getTotalTaxFreeIncome(), "累计免税收入", result) &&
                validateAmount(result.getTotalDeductions(), "累计减除费用", result) &&
                validateAmount(result.getTotalOtherDeductions(), "累计其他扣除", result) &&
                validateAmount(result.getTotalPrepaidTaxes(), "累计已预缴税额", result) &&
                validateAmount(result.getTotalTaxReductions(), "累计减免税额", result);
    }

    private boolean validateAmount(String value, String fieldName, ExcelRow result) {
        if (StringUtils.isNotBlank(value)) {
            if (!value.matches(AMOUNT_REGEX)) {
                result.pushError(value, String.format(ERROR_MSG, fieldName), "");
                return false;
            }

            // 额外检查是否为负数（如果需要）
            if (value.startsWith("-")) {
                result.pushError(value, fieldName + "不能为负数", "");
                return false;
            }
        }
        return true;
    }

    public void verifyPayrollStaff(SalaryCalculateController.payrollAddRequest request) {
        List<SalaryStatementEntity> salaryStatementEntities = salaryManager.queryUnconfirmedSalaryStatement(request.getSupplierCorporationId());
        if (CollectionUtil.isNotEmpty(salaryStatementEntities)){
            throw new ApiException("存在未确认的工资表，请确认后重试",ApiException.API_PARAM_ERROR);
        }

        //不能存在税款所属期 以后的工资表
        final List<SalaryStatementEntity> futureStatementEntities = salaryManager.querySalaryStatement(t -> t.supplierCorporationId.eq(request.getSupplierCorporationId()).and(t.taxPeriod.gt(request.getTaxPeriod()))).fetch();
        if (CollectionUtil.isNotEmpty(futureStatementEntities)){
            throw new ApiException("存在下期工资表,本期无法计算",ApiException.API_PARAM_ERROR);
        }

        //非一月的情况
        final LocalDate taxPeriod = convertTaxPeriodToLocalDate(request.getTaxPeriod());
        if (taxPeriod.getMonthValue() != 1) {
            //税款所属期上期如果存在工资表 必须存在个税申报状态为已申报的个税申报记录
            final String perTaxPeriod = convertLocalDateToTaxPeriod(taxPeriod.minusMonths(1));
            final List<SalaryStatementEntity> statementEntityList = salaryManager.querySalaryStatement(t -> t.supplierCorporationId.eq(request.getSupplierCorporationId()).and(t.taxPeriod.eq(perTaxPeriod))).fetch();
            if (CollectionUtil.isNotEmpty(statementEntityList)){
                // 查询所属期的个税申报记录
                PersonalIncomeTaxDeclareEntity lastDeclare = personalIncomeTaxDeclareManager
                        .queryPersonalIncomeTaxDeclareBySupplierCorporationAndTaxPeriod(
                                request.getSupplierCorporationId(), perTaxPeriod);

                if (lastDeclare == null || !TaxDeclareStatusEnum.DECLARED.name().equals(lastDeclare.getTaxStatus())) {
                    throw new ApiException("上期收入未报税无法导入",ApiException.API_PARAM_ERROR);
                }
            }
        }
    }

    /**
     * 将税期字符串(yyyy-MM格式)转换为 LocalDate
     * @param taxPeriod 税期字符串，如 "2025-02"
     * @return 该月第一天的 LocalDate
     */
    private LocalDate convertTaxPeriodToLocalDate(String taxPeriod) {
        if (taxPeriod == null || taxPeriod.trim().isEmpty()) {
            return null;
        }

        try {
            YearMonth yearMonth = YearMonth.parse(taxPeriod, DateTimeFormatter.ofPattern("yyyy-MM"));
            // 返回该月第一天
            return yearMonth.atDay(1);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("税期格式不正确: " + taxPeriod + "，应为 yyyy-MM 格式");
        }
    }

    /**
     * 将 LocalDate 转换为税期字符串(yyyy-MM格式)
     * @param date LocalDate
     * @return 税期字符串
     */
    private String convertLocalDateToTaxPeriod(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    public void verifyCurrentMonthPayrollStaff(PreviousIncomeImportRequest request) {

        //计算下个月的 taxPeriod
        YearMonth current = YearMonth.parse(request.getTaxPeriod());
        YearMonth nextMonth = current.plusMonths(1);
        // 格式化为 "yyyy-MM"
        String nextTaxPeriod = nextMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        //如果下个税款所属期是1月，直接返回
        if (nextMonth.getMonthValue() == 1) {
            return;
        }

        List<SalaryStatementEntity> entities = salaryManager.querySalaryStatement(nextTaxPeriod, request.getSupplierCorporationId(), SalaryStatementStatus.CONFIRMED);
        if (CollectionUtil.isNotEmpty(entities)){
            throw new ApiException("当前作业主体次月已存在已确认的工资表，请勿重复导入",ApiException.API_PARAM_ERROR);
        }
    }

    public void addPreviousIncomeStaff(PreviousIncomeImportRequest request, List<ImportPreviousIncomeRow> successDataList, TenantInfo tenant) {
        successDataList.forEach(model -> {
            PreviousIncomeDeductionEntity entity = salaryManager.queryPreviousIncomeDeduction(t -> t.idNumber.eq(model.getIdCard())
                    .and(t.supplierCorporationId.eq(request.getSupplierCorporationId()))
                    .and(t.taxPeriod.eq(request.getTaxPeriod()))
            ).fetchOne();
            if (entity == null) {
                entity = new PreviousIncomeDeductionEntity(tenant);
            }
            entity.setSupplierId(request.getSupplierId());
            entity.setSupplierCorporationId(request.getSupplierCorporationId());
            entity.setFullName(model.getName());
            entity.setIdNumber(model.getIdCard());
            entity.setTaxPeriod(request.getTaxPeriod());
            entity.setAccumulatedIncome(new BigDecimal(StringUtils.isBlank(model.getTotalIncome()) ? "0.00" : model.getTotalIncome()));
            entity.setAccumulatedExpenses(new BigDecimal(StringUtils.isBlank(model.getTotalExpenses()) ? "0.00" : model.getTotalExpenses()));
            entity.setAccumulatedTaxFreeIncome(new BigDecimal(StringUtils.isBlank(model.getTotalTaxFreeIncome()) ? "0.00" : model.getTotalTaxFreeIncome()));
            entity.setAccumulatedDeductionExpenses(new BigDecimal(StringUtils.isBlank(model.getTotalDeductions()) ? "0.00" : model.getTotalDeductions()));
            entity.setAccumulatedOtherDeductions(new BigDecimal(StringUtils.isBlank(model.getTotalOtherDeductions()) ? "0.00" : model.getTotalOtherDeductions()));
            entity.setAccumulatedPrepaidTax(new BigDecimal(StringUtils.isBlank(model.getTotalPrepaidTaxes()) ? "0.00" : model.getTotalPrepaidTaxes()));
            entity.setAccumulatedTaxReductions(new BigDecimal(StringUtils.isBlank(model.getTotalTaxReductions()) ? "0.00" : model.getTotalTaxReductions()));
            entity.setUploadTime(LocalDateTime.now());
            salaryManager.saveOrUpdatePreviousIncomeDeduction(entity);
        });
    }

    public void salaryTaxCalculateTask() {
        log.info("[工资表算税]开始计算工资批次的个税");
        List<SalaryStatementEntity> list = salaryManager.querySalaryStatementByStatus(SalaryStatementStatus.CALCULATING);
        if (CollectionUtil.isEmpty(list)) {
            log.info("[工资表算税]没有需要计算的工资批次!");
        }
        for (SalaryStatementEntity salaryStatement : list) {
            log.info("[工资表算税]开始计算工资批次ID: {}, 税期: {}", salaryStatement.getId(), salaryStatement.getTaxPeriod());
            salaryTaxCalculationService.batchCalculateSalaryTax(salaryStatement.getId());
        }
        log.info("[工资表算税]所有工资批次的个税计算完成");
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteSalary(Long id, long currentSupplierId, Set<Long> contractIds) {
        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(id);
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }
        if(salaryStatementEntity.getSupplierId() != currentSupplierId || !contractIds.contains(salaryStatementEntity.getContractId())){
            throw ApiException.paramError("无操作权限");
        }
        if (salaryStatementEntity.getStatus() == SalaryStatementStatus.CONFIRMED) {
            throw ApiException.apiError("工资表已确认，无法删除");
        }
        salaryManager.deleteSalaryDetailBySalaryStatementId(salaryStatementEntity.getId());
        salaryManager.deleteSalaryStatement(salaryStatementEntity);
    }

    public List<String> salaryOverStaffCount(Long statementId) {
        return salaryManager.queryHighSalaryEmployeesInStatementByTotalAmount(statementId);
    }

    /**
     * 确认工资表
     * @param statementId
     */
    public void confirmStatement(Long statementId) {
        //1. 查询工资表 校验工资表状态
        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(statementId);
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }
        if (salaryStatementEntity.getStatus() != SalaryStatementStatus.UNCONFIRMED) {
            throw ApiException.apiError("工资表状态不正确，无法确认");
        }

        //2. 查询工资表下有没有实发为0的工资明细
        List<SalaryDetailEntity> salaryDetailEntities = salaryManager.querySalaryDetailByStatementId(statementId);
        if (CollectionUtil.isNotEmpty(salaryDetailEntities)) {
            for (SalaryDetailEntity salaryDetailEntity : salaryDetailEntities) {
                if (salaryDetailEntity.getNetPayment().compareTo(BigDecimal.ZERO) <= 0) {
                    throw ApiException.apiError("工资表下存在实发为0的工资明细，无法确认,姓名:" + salaryDetailEntity.getName() + ",身份证号:" + salaryDetailEntity.getIdCard());
                }
            }
        }

        //3. 确认工资表
        salaryStatementEntity.setStatus(SalaryStatementStatus.CONFIRMED);
        salaryManager.updateSalaryStatement(salaryStatementEntity);
    }



}