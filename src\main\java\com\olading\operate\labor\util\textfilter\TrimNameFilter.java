package com.olading.operate.labor.util.textfilter;


import com.lanmaoly.util.lang.StringUtils;

import java.util.regex.Pattern;

/**
 * 无英文时全空格过滤，有英文时首尾过滤
 * 过滤名词，如姓名、岗位、城市……
 * <AUTHOR>
 * @date 2022/2/22 11:36
 */
public class TrimNameFilter implements TextFilter {
    public static final String REGEX = "^.*[a-zA-Z]+.*$";

    @Override
    public String filter(String text) {
        if (null == text) {
            return null;
        }

        String a;
        if (Pattern.matches(REGEX, text)){
            // 有英文时只做首尾过滤
            return StringUtils.RemoveEnter(text).trim();
        } else {
            // 无英文时全空格过滤
            return StringUtils.RemoveEnterBlank(text);
        }
    }
}
