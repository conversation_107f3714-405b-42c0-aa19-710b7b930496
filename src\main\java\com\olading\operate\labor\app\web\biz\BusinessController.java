package com.olading.operate.labor.app.web.biz;


import cn.hutool.core.collection.CollectionUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.boot.core.business.webapi.WebApiCodes;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.component.ratelimit.RateLimitException;
import com.olading.boot.util.RandomUtils;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.query.WebApiQuery;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.authority.AuthorityManager;
import com.olading.operate.labor.domain.share.authority.RoleDataScope;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.domain.supplier.SupplierDomainData;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import com.olading.operate.labor.domain.supplier.SupplierManager;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.File;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
public abstract class BusinessController extends com.olading.boot.core.business.webapi.BusinessController {

    @Autowired
    private SupplierManager supplierManager;

    @Autowired
    private BusinessContractManager businessContractManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private AuthorityManager authorityManager;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private PlatformTransactionManager transactionManager;
    
    protected TenantInfo currentTenant() {
        var t = currentTenantInfo();
        if (t == null) {
            throw new BusinessException("未登录或者没有切换企业");
        }
        return TenantInfo.of(t);
    }

    protected TenantInfo currentTenant(TenantInfo.TenantType type) {
        var tenant = currentTenant();
        if (tenant.getType() != type) {
            return null;
        }
        return tenant;
    }

    /**
     * 当前的服务商(服务商登入时使用)
     */
    protected SupplierEntity currentSupplier() {
        long supplierId = currentSupplierId();
        SupplierEntity supplier = supplierManager.getSupplierById(supplierId);
        if (supplier != null) {
            return supplier;
        }
        throw new BusinessException("没有切换企业");
    }

    protected UserEntity currentUser() {
        return userManager.requireUser(currentUserId());
    }

    /**
     * 当前的服务商(服务商登入时使用)
     */
    protected long currentSupplierId() {
        if (currentTenant().getType() == TenantInfo.TenantType.BOSS ) {
            throw new BusinessException("没有切换企业");
        }
        return Long.parseLong(currentTenant().getId());
    }

    protected long currentUserId() {
        var info = currentUserInfo();
        if (info == null) {
            throw new BusinessException("未登录");
        }
        return Long.parseLong(info.getId());
    }

    @SneakyThrows
    protected <T, F, R> void downloadExcel(HttpServletResponse response, String filename, WebApiQuery<T, F, R> query, QueryFilter<F> filter) {
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));
        var output = response.getOutputStream();
        var transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        transactionTemplate.execute(s -> {
            query.excel(filter, output, filename);
            return null;
        });
    }

    protected void downloadWithDay(HttpServletResponse response, String filename, File file) {
        var i = filename.lastIndexOf(".");
        if (i >= 0) {
            var random = RandomUtils.string("0123456789", 4);
            filename = filename.substring(0, i) + "-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "-" + random + filename.substring(i);
        }
        download(response, filename, file);
    }

    @SneakyThrows
    protected void download(HttpServletResponse response, String filename, File file) {
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));
        FileUtils.copyFile(file, response.getOutputStream());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler
    protected WebApiResponse<Void> handleRateLimitException(RateLimitException e) {
        log.warn("频次超限: {}", e.getMethod());
        return WebApiResponse.fail(WebApiCodes.BUSINESS_FAIL, "操作太频繁,请稍后再尝试");
    }


    public Map<OwnerType, Set<Long>> currentDataScope() {
        if(this.isAdmin()){
            return supplierService.queryAllDataScope(currentSupplierId());
        }

        final UserEntity userEntity = currentUser();
        final Set<RoleDataScope> roleDataScopes = authorityManager.getUserDataScopes(currentTenant(), userEntity.getId());

        final Map<OwnerType, List<RoleDataScope>> ownerTypeListMap = roleDataScopes.stream().collect(Collectors.groupingBy(RoleDataScope::getDataType));
        final Map<OwnerType, Set<Long>> ownerTypeSetMap = ownerTypeListMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(RoleDataScope::getDataId).collect(Collectors.toSet())));
        if(CollectionUtil.isEmpty(ownerTypeSetMap.get(OwnerType.CUSTOMER) )){
            ownerTypeSetMap.put(OwnerType.CUSTOMER, Set.of());
        }
        if(CollectionUtil.isEmpty(ownerTypeSetMap.get(OwnerType.CONTRACT))){
            ownerTypeSetMap.put(OwnerType.CONTRACT, new HashSet<>());
        }
        if(CollectionUtil.isEmpty(ownerTypeSetMap.get(OwnerType.CORPORATION) )){
            ownerTypeSetMap.put(OwnerType.CORPORATION, Set.of());
        }
        ownerTypeSetMap.get(OwnerType.CUSTOMER).forEach(customerId -> {
            //客户主体下能查看当前客户下所有合同
            businessContractManager.queryContract(t->t.customerId.eq(customerId)).fetch().forEach(contractVo -> {
                ownerTypeSetMap.get(OwnerType.CONTRACT).add(contractVo.getId());
            });
        });
        return ownerTypeSetMap;
    }



    protected long getSupplierId(HttpServletRequest  request) {
        final String supplierId = request.getHeader("supplier");
        if(StringUtils.isNotBlank(supplierId)){
            return Long.parseLong(supplierId);
        }
        String supplierDomain = request.getHeader("supplierDomain");

        if(StringUtils.isBlank(supplierDomain)){
            supplierDomain = extractDomain(request.getHeader("Host"));
        }

        return getSupplierId(supplierDomain);
    }


    protected long getSupplierId(String supplierDomain) {
        SupplierDomainData supplierDomainData = supplierManager.getSupplierByDomain(supplierDomain.trim());
        if(supplierDomainData != null){
            return supplierDomainData.getSupplierId();
        }

        throw new BusinessException("未携带正确的请求信息");
    }


    public static String extractDomain(String host) {
        if (host == null) {
            return null;
        }
        int portIndex = host.indexOf(':');
        if (portIndex > 0) {
            return host.substring(0, portIndex);
        }
        return host;
    }

    public boolean isAdmin(){
        return currentSupplier().getAdminUserId() != null && Objects.equals(currentSupplier().getAdminUserId(), currentUserId());
    }
}
