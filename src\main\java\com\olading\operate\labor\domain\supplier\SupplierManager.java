package com.olading.operate.labor.domain.supplier;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationPayChannelEntity;
import com.olading.operate.labor.domain.corporation.QCorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.QCorporationPayChannelEntity;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.share.authority.QSupplierMemberEntity;
import com.olading.operate.labor.domain.share.authority.SupplierMemberEntity;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoData;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import com.olading.operate.labor.domain.share.info.InfoManager;
import com.olading.operate.labor.domain.share.info.OwnedByFragment;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Transactional
public class SupplierManager {

    private final EntityManager em;
    private final InfoManager infoManager;
    private final FileManager fileManager;

    /**
     * 添加一个服务商
     */
    public SupplierEntity addSupplier(SupplierData data,TenantInfo tenantInfo) {

        if (data.getSupplierNo() != null) {
            List<SupplierEntity> supplier = querySupplier(t -> t.supplierNo.eq(data.getSupplierNo())).fetch();
            if (!supplier.isEmpty()) {
                throw new BusinessException("灵工平台服务商编号不可重复");
            }
        }

        SupplierEntity entity = new SupplierEntity(tenantInfo);
        entity.setSupplierNo(data.getSupplierNo());
        entity.setBusinessCreateTime(data.getBusinessCreateTime());
        entity.setSignatureCode(data.getSignatureCode());
        entity.setName(data.getName());
        entity = em.merge(entity);;
        EnterpriseInfoEntity info = infoManager.save(new OwnedByFragment(OwnerType.SUPPLIER, entity.getId()),data.getInfo());
        entity.setEnterpriseInfoId(info.getId());
        return em.merge(entity);
    }

    public void setSupplierDisabled(long supplierId, boolean disabled) {
        SupplierEntity supplier = requireSupplier(supplierId);
        supplier.setDisabled(disabled);
        em.merge(supplier);
    }

    /**
     * 修改服务商编号
     */
    public void saveSupplier(SupplierData data) {
        SupplierEntity entity = requireSupplier(data.getId());
        if (data.getBusinessCreateTime() != null) {
            entity.setBusinessCreateTime(data.getBusinessCreateTime());
        }
        if (data.getSupplierNo() != null) {
            SupplierEntity supplier = querySupplier(t -> t.supplierNo.eq(data.getSupplierNo())).fetchOne();
            if (supplier != null && !Objects.equals(supplier.getId(), entity.getId())) {
                throw new BusinessException("服务商编号不可重复");
            }
            entity.setSupplierNo(data.getSupplierNo());
        }
        if(StringUtils.isNotBlank(data.getSignatureCode())){
            entity.setSignatureCode(data.getSignatureCode());
        }
        if (data.getName() != null) {
            entity.setName(data.getName());
        }
        //管理员
        if(data.getAdminUserId() != null && !Objects.equals(entity.getAdminUserId(), data.getAdminUserId())){
            entity.setAdminUserId(data.getAdminUserId());
        }
        em.merge(entity);
    }

    /**
     * 修改服务商编号
     */
    public void saveSupplierDomain(SupplierDomainData data) {
        requireSupplier(data.getSupplierId());
        SupplierDomainConfigEntity supplierDomainConfigEntity = querySupplierDomainConfig(t-> t.supplierId.eq(data.getSupplierId())).fetchOne();

        if(supplierDomainConfigEntity == null){
            supplierDomainConfigEntity = new SupplierDomainConfigEntity();
            supplierDomainConfigEntity.setSupplierId(data.getSupplierId());
        }

        if(data.getBrandName() != null ){
            supplierDomainConfigEntity.setBrandName(data.getBrandName());
        }

        if (data.getDomainName() != null) {
            supplierDomainConfigEntity.setDomainName(data.getDomainName());
        }

        if (data.getH5DomainName() != null){
            supplierDomainConfigEntity.setH5DomainName(data.getH5DomainName());
        }

        if (data.getLogoUrl() != null) {
            supplierDomainConfigEntity.setLogoUrl(data.getLogoUrl());
        }

        if (data.getH5LogoUrl() != null) {
            supplierDomainConfigEntity.setH5LogoUrl(data.getH5LogoUrl());
        }

        if (data.getH5ServiceAgreement() != null) {
            supplierDomainConfigEntity.setH5ServiceAgreement(JSONUtil.toJsonStr(data.getH5ServiceAgreement()));
        }
        em.merge(supplierDomainConfigEntity);
    }

    public SupplierDomainData getSupplierDomain(long supplierId) {
        requireSupplier(supplierId);
        SupplierDomainConfigEntity supplierDomainConfigEntity =
                querySupplierDomainConfig(t-> t.supplierId.eq(supplierId)).fetchOne();
        if(supplierDomainConfigEntity == null){
            return null;
        }
        return new SupplierDomainData(supplierDomainConfigEntity);
    }

    /**
     * 修改服务商企业信息
     */
    public EnterpriseInfoEntity saveSupplierInfo(long supplierId, EnterpriseInfoData enterpriseInfo) {
        SupplierEntity entity = requireSupplier(supplierId);
        return infoManager.save(new OwnedByFragment(OwnerType.SUPPLIER, entity.getId()), enterpriseInfo);
    }


    /**
     * 绑定用户到客户
     */
    public SupplierMemberEntity addCustomerMember(long supplierId, long customerId, UserEntity user, String name) {
        SupplierMemberEntity entity = querySupplierMember(t -> t.supplierId.eq(supplierId).and(t.ownedBy.eq(new OwnedByFragment(OwnerType.CUSTOMER, customerId)))
                .and(t.userId.eq(user.getId()))).fetchOne();
        if (entity == null) {
            entity = new SupplierMemberEntity(supplierId,OwnerType.CUSTOMER,customerId, user.getId());
            entity.setCellphone(user.getCellphone());
        }
        entity.setName(name);
        entity.setDisabled(false);
        return em.merge(entity);
    }



    /**
     * 绑定用户到服务商
     */
    public SupplierMemberEntity addMember(long supplierId, UserEntity user, String name) {
        SupplierMemberEntity entity = querySupplierMember(t -> t.supplierId.eq(supplierId).and(t.ownedBy.eq(new OwnedByFragment(OwnerType.SUPPLIER, supplierId)))
                .and(t.userId.eq(user.getId()))).fetchOne();
        if (entity == null) {
            entity = new SupplierMemberEntity(supplierId,OwnerType.SUPPLIER,supplierId, user.getId());
            entity.setCellphone(user.getCellphone());
        }
        entity.setName(name);
        entity.setDisabled(false);
        return em.merge(entity);
    }

    public SupplierMemberEntity editMember(long supplierId, long memberId, String name) {
        var member = requireMember(supplierId, memberId);
        member.setName(name);
        return em.merge(member);
    }

    public void rebindMember(long supplierId, long memberId, UserEntity user) {
        var member = requireMember(supplierId, memberId);
        member.setCellphone(user.getCellphone());
        member.setUserId(user.getId());
        em.merge(member);
    }

    public void setMemberDisabled(long supplierId, long memberId, boolean disabled) {
        var supplier = requireSupplier(supplierId);
        SupplierMemberEntity entity = querySupplierMember(t -> t.supplierId.eq(supplierId).and(t.id.eq(memberId))).fetchOne();
        if (entity == null) {
            return;
        }

        if (disabled && Objects.equals(supplier.getAdminUserId(), entity.getUserId())) {
            throw new BusinessException("不可禁用或者删除管理员");
        }

        entity.setDisabled(disabled);
        em.merge(entity);
    }

    public List<SupplierMemberEntity> getSupplierMember(long supplierId, List<Long> memberId) {
        return querySupplierMember(t -> t.supplierId.eq(supplierId).and(t.id.in(memberId))).fetch();
    }

    public List<SupplierMemberEntity> getSupplierMemberByUserId(long userId) {
        return querySupplierMember(t -> t.userId.eq(userId)).fetch();
    }

    public SupplierMemberEntity getSupplierMemberByUserId(long supplierId,long customerId, long userId) {
        return querySupplierMember(t -> t.userId.eq(userId).and(t.supplierId.eq(supplierId)).and(t.ownedBy.eq(new OwnedByFragment(OwnerType.CUSTOMER, customerId)))).fetchOne();
    }

    public SupplierMemberEntity getSupplierMemberByUserId(long supplierId, long userId) {
        return querySupplierMember(t -> t.userId.eq(userId).and(t.supplierId.eq(supplierId)).and(t.ownedBy.eq(new OwnedByFragment(OwnerType.SUPPLIER, supplierId)))).fetchOne();
    }


    public List<SupplierMemberEntity> listCustomerMember(long supplierId,long userId) {
        return querySupplierMember(t -> t.supplierId.eq(supplierId).and(t.userId.eq(userId)).and(t.ownedBy.ownerType.eq(OwnerType.CUSTOMER))).fetch();
    }

    public SupplierMemberEntity getCustomerMember(long customerId,long userId) {
        return querySupplierMember(t -> t.userId.eq(userId).and(t.ownedBy.eq(new OwnedByFragment(OwnerType.CUSTOMER, customerId)))).fetchOne();
    }


    public void useSupplierMember(long supplierId, long userId) {
        SupplierMemberEntity member = querySupplierMember(t -> t.supplierId.eq(supplierId).and(t.userId.eq(userId))).fetchOne();
        if (member != null) {
            member.setLastUsedTime(LocalDateTime.now());
            em.merge(member);
        }
    }

    /**
     * 设置服务商的管理员
     */
    public void setAdmin(long supplierId, SupplierMemberEntity member) {

        if (member.getSupplierId() != supplierId) {
            throw new IllegalArgumentException();
        }

        SupplierEntity supplier = requireSupplier(supplierId);

        Long old = supplier.getAdminUserId();

        supplier.setAdminUserId(member.getUserId());
        em.merge(supplier);

        // 同步更新联系人
        EnterpriseInfoData data = new EnterpriseInfoData();
        data.setContacts(member.getName());
        data.setContactPhone(member.getCellphone());
        saveSupplierInfo(supplierId, data);
    }

    public SupplierEntity requireSupplier(long id) {
        SupplierEntity entity = em.find(SupplierEntity.class, id);
        if (entity == null) {
            throw new IllegalStateException("SupplierEntity不存在" + id);
        }
        return entity;
    }

    public EnterpriseInfoEntity getSupplierInfo(long id) {
        return infoManager.getEnterpriseInfo(OwnerType.SUPPLIER, id);
    }



    public SupplierMemberEntity requireMember(long supplierId, long memberId) {
        var member = querySupplierMember(t -> t.ownedBy.eq(new OwnedByFragment(OwnerType.SUPPLIER, supplierId)).and(t.id.eq(memberId))).fetchOne();
        if (member == null) {
            throw new IllegalStateException("服务商内没有此用户");
        }
        return member;
    }



    public SupplierEntity getSupplierById(long id) {
        return em.find(SupplierEntity.class, id);
    }

    public SupplierSmsTemplateEntity requireSupplierSmsTemplate(long supplierId,SmsBusinessType businessType) {
        final SupplierSmsTemplateEntity entity = querySupplierSmsTemplate(t -> t.supplierId.eq(supplierId).and(t.businessType.eq(businessType))).fetchOne();
        if (entity == null) {
            throw new IllegalStateException("SupplierSmsTemplateEntity不存在," + supplierId + ":" + businessType);
        }
        return entity;
    }

    public List<SupplierEntity> fetchSupplier(Function<QSupplierEntity, Predicate> condition) {
        return querySupplier(condition).fetch();
    }

    public SupplierDomainData getSupplierByDomain(String supplierDomain) {
        SupplierDomainConfigEntity supplierDomainConfigEntity = querySupplierDomainConfig(t -> t.domainName.eq(supplierDomain).or(t.h5DomainName.eq(supplierDomain))).fetchOne();
        if (supplierDomainConfigEntity == null) {
            return null;
        }
        return new SupplierDomainData(supplierDomainConfigEntity);
    }


    private JPAQuery<SupplierEntity> querySupplier(Function<QSupplierEntity, Predicate> condition) {
        QSupplierEntity t = QSupplierEntity.supplierEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<SupplierMemberEntity> querySupplierMember(Function<QSupplierMemberEntity, Predicate> condition) {
        QSupplierMemberEntity t = QSupplierMemberEntity.supplierMemberEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<SupplierDomainConfigEntity> querySupplierDomainConfig(Function<QSupplierDomainConfigEntity, Predicate> condition) {
        QSupplierDomainConfigEntity t = QSupplierDomainConfigEntity.supplierDomainConfigEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<SupplierSmsTemplateEntity> querySupplierSmsTemplate(Function<QSupplierSmsTemplateEntity, Predicate> condition) {
        QSupplierSmsTemplateEntity t = QSupplierSmsTemplateEntity.supplierSmsTemplateEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    public void changeMemberPhone(Long supplierId, Long userId, String  phone) {
        requireSupplier(supplierId);
        final SupplierMemberEntity memberEntity = getSupplierMemberByUserId(supplierId, userId);
        if(memberEntity != null){
            memberEntity.setCellphone(phone);
            em.merge(memberEntity);
        }
    }

    public void setSmsConfig(Long supplierId, SmsBusinessType businessType, String templateCode) {
        SupplierSmsTemplateEntity entity = querySupplierSmsTemplate(t -> t.supplierId.eq(supplierId).and(t.businessType.eq(businessType))).fetchOne();
        if (entity == null) {
            entity = new SupplierSmsTemplateEntity();
            entity.setSupplierId(supplierId);
            entity.setBusinessType(businessType);
            em.persist(entity);
        }
        entity.setTemplateCode(templateCode);
        em.merge(entity);
    }

    public List<SupplierSmsTemplateEntity> requireSupplierSmsTemplate(Long supplierId) {
        final List<SupplierSmsTemplateEntity> fetch = querySupplierSmsTemplate(t -> t.supplierId.eq(supplierId)).fetch();
        if (CollectionUtil.isEmpty(fetch)) {
            return List.of();
        }
        return fetch;
    }

    public void removeMember(long l, Long id) {
        SupplierMemberEntity member = requireMember(l, id);
        em.remove(member);
    }

    public List<SupplierPayChannelData> queryPayChannelList(Long id) {
        QSupplierPayChannelEntity supplierPayChannel = QSupplierPayChannelEntity.supplierPayChannelEntity;
        QPayChannelConfigEntity payChannelConfig = QPayChannelConfigEntity.payChannelConfigEntity;

        final List<Tuple> fetch = new JPAQuery<>(em)
                .select(supplierPayChannel, payChannelConfig)
                .from(supplierPayChannel)
                .leftJoin(payChannelConfig).on(payChannelConfig.payChannel.eq(supplierPayChannel.payChannel))
                .where(supplierPayChannel.supplierId.eq(id))
                .fetch();

        return fetch.stream().map(tuple -> new SupplierPayChannelData(tuple.get(supplierPayChannel), tuple.get(payChannelConfig)))
                .collect(Collectors.toList());
    }


    private JPAQuery<SupplierPayChannelEntity> querySupplierChannel(Function<QSupplierPayChannelEntity, Predicate> condition) {
        QSupplierPayChannelEntity t = QSupplierPayChannelEntity.supplierPayChannelEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<PayChannelConfigEntity> queryPayChannelConfig(Function<QPayChannelConfigEntity, Predicate> condition) {
        QPayChannelConfigEntity t = QPayChannelConfigEntity.payChannelConfigEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    /**
     * 添加平台的可用支付渠道
     * @param collect
     */
    public void addPayChannel(Set<SupplierPayChannelData> collect) {
        collect.forEach(channel -> {
            SupplierPayChannelEntity entity = new SupplierPayChannelEntity();
            entity.setSupplierId(channel.getSupplierId());
            entity.setPayChannel(channel.getPayChannel());
            em.persist(entity);
        });

    }
}
