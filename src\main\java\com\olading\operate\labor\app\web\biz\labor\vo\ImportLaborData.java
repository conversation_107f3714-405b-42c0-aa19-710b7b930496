package com.olading.operate.labor.app.web.biz.labor.vo;

import com.olading.operate.labor.util.excel.ExcelColumn;
import com.olading.operate.labor.util.excel.ExcelRow;
import com.olading.operate.labor.util.validation.constraints.Name;
import com.olading.operate.labor.util.validation.constraints.Text;
import lombok.Data;

@Data
public class ImportLaborData extends ExcelRow {

    @ExcelColumn(name = "姓名", required = true)
    @Name(required = true)
    private String name;

    @ExcelColumn(name = "证件号码", required = true)
    @Name(required = true, maxLength = 60)
    private String idCard;

    @ExcelColumn(name = "手机号码", required = true)
    @Text(required = true, maxLength = 11)
    private String cellPhone;

    //@ExcelColumn(name = "所属作业主体", required = true)
    @Name(required = true)
    private String corporationName;

    //@ExcelColumn(name = "所属客户", required = true)
    @Name(required = true)
    private String customerName;

    @ExcelColumn(name = "所属服务合同", required = true)
    @Name(required = true)
    private String contractName;

    @ExcelColumn(name = "银行卡号")
    @Name(required = true, maxLength = 60)
    private String bankCard;

    @ExcelColumn(name = "开户行")
    @Name(required = true, maxLength = 60)
    private String cardBank;

    @ExcelColumn(name = "反馈信息")
    @Name(required = true, maxLength = 60)
    private String errorMsg;
}
