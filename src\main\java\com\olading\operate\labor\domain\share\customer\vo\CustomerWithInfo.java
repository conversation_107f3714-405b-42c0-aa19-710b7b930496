package com.olading.operate.labor.domain.share.customer.vo;

import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.info.EnterpriseInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CustomerWithInfo {

    @Schema(description = "客户ID")
    private Long id;

    @Schema(description = "灵工平台id")
    private Long supplierId;

    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "客户编号")
    private String sn;

    @Schema(description = "客户简称")
    private String shortName;

    @Schema(description = "客户状态:1-未合作;2-合作中;3-停止合作")
    private String status;

    @Schema(description = "客户地区id")
    private String regionId;

    @Schema(description = "客户详细地址")
    private String address;

    @Schema(description = "客户行业")
    private String industry;

    @Schema(description = "客户性质")
    private String type;

    @Schema(description = "客户规模")
    private String size;

    @Schema(description = "客户来源")
    private String source;

    @Schema(description = "销售负责人姓名")
    private String salesName;

    @Schema(description = "销售负责人电话")
    private String serviceMobile;

    @Schema(description = "客户联系人姓名")
    private String contactName;

    @Schema(description = "客户联系人电话")
    private String contactMobile;

    @Schema(description = "客户备注")
    private String remark;

    @Schema(description = "是否禁用")
    private Boolean disabled;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "操作角色")
    private String operationRoleIds;

    @Schema(description = "企业信息ID")
    private Long enterpriseInfoId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "企业联系人")
    private String enterpriseContacts;

    @Schema(description = "企业联系电话")
    private String enterpriseContactPhone;

    @Schema(description = "企业备注")
    private String enterpriseRemark;

    @Schema(description = "企业附件文件ID列表")
    private String enterpriseAttachments;

    @Schema(description = "企业营业执照图片id")
    private String businessLicenseImage;

    @Schema(description = "企业统一社会信用代码")
    private String socialCreditCode;

    @Schema(description = "企业法定代表人姓名")
    private String representativeName;

    @Schema(description = "企业法定代表人证件类型")
    private String certificateType;

    @Schema(description = "企业法定代表人证件号")
    private String certificateNo;

    @Schema(description = "企业法定代表人证件正面照")
    private String certificateFrontImage;

    @Schema(description = "企业法定代表人证件背面照")
    private String certificateBackImage;

    @Schema(description = "企业注册地址")
    private String registeredAddress;

    @Schema(description = "角色Id列表")
    private List<Long> roleIds;

    @Schema(description = "管理员姓名")
    private String adminName;

    @Schema(description = "管理员手机")
    private String adminMobile;

    @Comment("创建时间")
    protected LocalDateTime createTime;

    public CustomerWithInfo() {
    }

    public CustomerWithInfo(CustomerEntity customerEntity, EnterpriseInfoEntity enterpriseInfoEntity) {
        this.id = customerEntity.getId();
        this.supplierId = customerEntity.getSupplierId();
        this.name = customerEntity.getName();
        this.sn = customerEntity.getSn();
        this.shortName = customerEntity.getShortName();
        this.status = customerEntity.getStatus();
        this.regionId = customerEntity.getRegionId();
        this.address = customerEntity.getAddress();
        this.industry = customerEntity.getIndustry();
        this.type = customerEntity.getType();
        this.size = customerEntity.getSize();
        this.source = customerEntity.getSource();
        this.salesName = customerEntity.getSalesName();
        this.serviceMobile = customerEntity.getServiceMobile();
        this.contactName = customerEntity.getContactName();
        this.contactMobile = customerEntity.getContactMobile();
        this.remark = customerEntity.getRemark();
        this.disabled = customerEntity.getDisabled();
        this.userId = customerEntity.getUserId();
        this.enterpriseInfoId = customerEntity.getEnterpriseInfoId();
        this.createTime = customerEntity.getCreateTime();

        if (enterpriseInfoEntity != null) {
            this.enterpriseName = enterpriseInfoEntity.getName();
            this.enterpriseContacts = enterpriseInfoEntity.getContacts();
            this.enterpriseContactPhone = enterpriseInfoEntity.getContactPhone();
            this.enterpriseRemark = enterpriseInfoEntity.getRemark();
            this.enterpriseAttachments = enterpriseInfoEntity.getAttachments().toString();
            this.businessLicenseImage = enterpriseInfoEntity.getBusinessLicenseImage();
            this.socialCreditCode = enterpriseInfoEntity.getSocialCreditCode();
            this.representativeName = enterpriseInfoEntity.getRepresentativeName();
            this.certificateType = enterpriseInfoEntity.getCertificateType() == null ? null : enterpriseInfoEntity.getCertificateType().name();
            this.certificateNo = enterpriseInfoEntity.getCertificateNo();
            this.certificateFrontImage = enterpriseInfoEntity.getCertificateFrontImage();
            this.certificateBackImage = enterpriseInfoEntity.getCertificateBackImage();
            this.registeredAddress = enterpriseInfoEntity.getRegisteredAddress();
        }
    }
}