package com.olading.operate.labor.util.excel;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel列的定义
 * <AUTHOR>
 * @date 2022/2/22 10:55
 */

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented

public @interface Excel {

    /**
     * 默认按照字段定义顺序导出
     * @return
     */
    ExcelColumnOrder writeColumnOrder() default ExcelColumnOrder.ORDER_BY_FIELD;
}