package com.olading.operate.labor.domain.service;

import com.lanmaoly.cloud.archive.ArchiveService;
import com.lanmaoly.cloud.archive.DefaultArchiveService;
import com.lanmaoly.cloud.archive.pdf.PdfMetadata;
import com.lanmaoly.cloud.archive.pdf.PdfPageMetadata;
import com.lanmaoly.cloud.archive.pdf.PdfService;
import com.olading.operate.labor.AppProperties;
import com.olading.operate.labor.app.provider.OladingEnvironmentProvider;
import com.olading.operate.labor.app.web.biz.enums.ProtocolTempEnum;
import com.olading.operate.labor.app.web.biz.protocol.TemplateController;
import com.olading.operate.labor.app.web.biz.protocol.vo.*;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.share.file.FileInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.protocol.*;
import com.olading.operate.labor.domain.share.signing.CloudSigningService;
import com.olading.operate.labor.domain.share.signing.common.Control;
import com.olading.operate.labor.domain.share.signing.common.Field;
import com.olading.operate.labor.domain.share.signing.common.Step;
import com.olading.operate.labor.domain.share.signing.enums.EnumOperateType;
import com.olading.operate.labor.domain.share.signing.enums.FieldType;
import com.olading.operate.labor.domain.share.signing.enums.TemplateStatus;
import com.olading.operate.labor.domain.share.signing.request.*;
import com.olading.operate.labor.domain.share.signing.response.*;
import com.olading.operate.labor.domain.share.user.UserEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
public class ProtocolTemplateService {

    private final ProtocolTemplateRepository templateRepository;

    private final CloudSigningService cloudSigningService;

    private final FileManager fileManager;

    private final AppProperties appProperties;

    private PdfService pdfService;

    private static final String SUCCESS = "0";

    /**
     * 定时任务，查询模板状态
     */
    public void queryTemplateStatus() {
        //查询24小时内的草稿状态的模板
        LocalDateTime timeEnd = LocalDateTime.now();
        LocalDateTime timeStart = timeEnd.plusHours(-48);
        List<CorporationProtocolTemplateEntity> tempList = templateRepository.queryTemplateByStatus(Arrays.asList(ProtocolTempEnum.ERROR, ProtocolTempEnum.DRAFT), timeStart, timeEnd);
        for (CorporationProtocolTemplateEntity t : tempList) {
            try {
                QueryTemplateRequest req = new QueryTemplateRequest();
                req.setId(Long.parseLong(t.getAgentTemplate()));
                BaseCloudSigningResponse<QueryTemplateResponse> response = cloudSigningService.queryTemplate(req);
                if (TemplateStatus.RELEASE == response.getData().getStatus()) {
                    t.setStatus(ProtocolTempEnum.ENABLED.name());
                    templateRepository.updateProtocolTemp(t);
                }
                if (TemplateStatus.DRAFT == response.getData().getStatus()) {
                    t.setStatus(ProtocolTempEnum.DRAFT.name());
                    templateRepository.updateProtocolTemp(t);
                }
            } catch (Exception e) {
                log.error("查询模板状态失败", e);
            }
        }
    }

    public Long createTemplate(AddUpdateTemplateVo param, TemplateController.SupplierAndUser supplierAndUser, Set<Long> corporationId) {
        checkTemplateName(param.getTempId(), param.getTemplateName(), supplierAndUser);
        param.getSteps().forEach(step -> {
            //填充域
            if (CollectionUtils.isEmpty(step.getFiledList())) {
                step.setFiledList(fillTemplateData(step));
            }
        });
        // 1. 调用云签接口进行模板创建
        CreateTemplateResponse templateResponse = createTemplateOfCloudSigning(param);
        // 2. 获取授权并上传模板文件
        uploadTemplateFileOfCloudSigning(supplierAndUser.getUser(), param, templateResponse);
        // 3. 保存模板数据
        return saveTemplate(supplierAndUser, param, templateResponse, corporationId);
    }

    List<TemplateFiled> fillTemplateData(TemplateStep step) {
        if (step.getOperate() == EnumOperateType.SEAL) {
            TemplateFiled filed1 = new TemplateFiled("公司名称", "corporationName", "SEAL", EnumOperateType.SEAL);
            TemplateFiled filed2 = new TemplateFiled("法人姓名", "representativeName", "SEAL", EnumOperateType.SEAL);
            TemplateFiled filed3 = new TemplateFiled("公司注册地址", "registerAddress", "SEAL", EnumOperateType.SEAL);
            return Arrays.asList(filed1, filed2, filed3);
        } else if (step.getOperate() == EnumOperateType.SIGN) {
            TemplateFiled filed1 = new TemplateFiled("姓名", "name", "SIGN", EnumOperateType.SIGN);
            TemplateFiled filed2 = new TemplateFiled("身份证号", "idCard", "SIGN", EnumOperateType.SIGN);
            TemplateFiled filed3 = new TemplateFiled("合同开始日期", "startDate", "SIGN", EnumOperateType.SIGN);
            TemplateFiled filed4 = new TemplateFiled("合同结束日期", "endDate", "SIGN", EnumOperateType.SIGN);
            return List.of(filed1, filed2, filed3, filed4);
        }
        return Collections.emptyList();
    }

    public TemplateController.SetTemplateResponse getSetTemplateUrl(Long userId, Long tempId) {
        CorporationProtocolTemplateEntity template = templateRepository.queryProtocolTempById(tempId);        //获取token
        AuthorizeRequest request = new AuthorizeRequest();
        request.setUser(String.valueOf(userId));
        request.setResource("sig/tpl/" + template.getAgentTemplate());
        BaseCloudSigningResponse<AuthorizeResponse> response = cloudSigningService.authorize(request);
        if (!"0".equals(response.getCode())) {
            throw new ApiException("获取token失败", ApiException.SYSTEM_ERROR);
        }
        TemplateController.SetTemplateResponse resp = new TemplateController.SetTemplateResponse();
        resp.setToken(response.getData().getToken());
        resp.setUrl(template.getEditUrl());
        return resp;
    }

    @Transactional
    public AddUpdateTemplateVo getTemplateDetail(Long tempId) {
        AddUpdateTemplateVo response = new AddUpdateTemplateVo();
        //获取模板信息
        CorporationProtocolTemplateEntity template = templateRepository.queryProtocolTempById(tempId);
        response.setTempId(tempId);
        response.setTemplateName(template.getTempName());
        response.setTemplateType(template.getTempType());
        response.setArchiveId(template.getTemplateFileId());
        //根据模板获取使用范围
        List<CorporationProtocolTempRangeEntity> rangeList = templateRepository.queryTempRangeByTempId(tempId);
        if (CollectionUtils.isNotEmpty(rangeList)) {
            List<Long> ranges = rangeList.stream().map(CorporationProtocolTempRangeEntity::getSupplierCorporationId).collect(Collectors.toList());
            response.setCorporationIds(ranges);
        }
        //获取步骤
        List<CorporationProtocolTempStepEntity> tempStepList = templateRepository.queryTempStepByTempId(tempId);

        if (CollectionUtils.isNotEmpty(tempStepList)){
            List<TemplateStep> steps = tempStepList.stream()
                    .map(step -> {
                        TemplateStep templateStep = new TemplateStep();
                        templateStep.setOperate(step.getOperate());
                        templateStep.setSortBy(step.getSortBy());
                        templateStep.setStepName(step.getStepName());
                        //获取填充域列表
                        List<CorporationProtocolTempFiledEntity> templateFileds = templateRepository.queryTempFiledByStep(step.getId());
                        if (CollectionUtils.isNotEmpty(templateFileds)) {
                            List<TemplateFiled> filedList = templateFileds.stream().map(filed -> {
                                TemplateFiled f = new TemplateFiled();
                                f.setFieldName(filed.getFieldName());
                                f.setRelationCode(filed.getFieldCode());
                                f.setRelationName(filed.getRelationName());
                                return f;
                            }).collect(Collectors.toList());
                            templateStep.setFiledList(filedList);
                        }
                        return templateStep;
                    }).collect(Collectors.toList());
            response.setSteps(steps);
        }

        return response;
    }

    public void checkTemplateName(Long tempId, String tempName, TemplateController.SupplierAndUser supplierAndUser) {
        List<CorporationProtocolTemplateEntity> entities = templateRepository.queryProtocolTempByName(tempName, supplierAndUser.getSupplier().getId());
        if (CollectionUtils.isNotEmpty(entities) && tempId == null) {
            throw new ApiException("模板名称已存在", ApiException.API_PARAM_ERROR);
        }
        if (CollectionUtils.isNotEmpty(entities) && tempId != null && !entities.get(0).getId().equals(tempId)) {
            throw new ApiException("模板名称已存在", ApiException.API_PARAM_ERROR);
        }
    }



//    public TemplateVo getTemplate(TemplateController.SupplierAndUser supplierAndUser, Long id) {
//        TemplateController.GetTemplateDetailVo template = getTemplateDetail(supplierAndUser, id);
//        QueryTemplateResponse cloudTemplate = getCloudTemplate(template.getCloudSigningTemplateId());
//        return TemplateVo.from(template, cloudTemplate);
//    }

    private QueryTemplateResponse getCloudTemplate(String id) {
        QueryTemplateRequest request = new QueryTemplateRequest();
        request.setId(Long.valueOf(id));
        BaseCloudSigningResponse<QueryTemplateResponse> response
                = cloudSigningService.queryTemplate(request);
        if (response == null || !SUCCESS.equals(response.getCode())) {
            throw new ApiException("查询云签合同模板出错", ApiException.SYSTEM_ERROR);
        }
        return response.getData();
    }


    public TemplateController.GetTemplateDetailVo getTemplateDetail(TemplateController.SupplierAndUser supplierAndUser, Long tempId) {
        TemplateController.GetTemplateDetailVo response = new TemplateController.GetTemplateDetailVo();
        //获取模板信息
        CorporationProtocolTemplateEntity template = templateRepository.queryProtocolTempById(tempId);
        response.setTempId(tempId);
        response.setTemplateName(template.getTempName());
        response.setTemplateType(template.getTempType());
        response.setCloudSigningTemplateId(template.getAgentTemplate());
        response.setArchiveId(template.getTemplateFileId());
        try {
            PdfMetadata pdfMetadata = pdfService.describe(template.getTemplateFileId());
            List<String> list = pdfMetadata.getPages()
                    .stream()
                    .map(PdfPageMetadata::getThumbnailUrl)
                    .toList();
            response.setArchives(list);
        } catch (Exception e) {
            log.error("查询模板详情-获取模板文件失败", e);
            throw new ApiException("查询模板失败", ApiException.SYSTEM_ERROR);
        }
        //根据模板获取使用范围
        List<CorporationProtocolTempRangeEntity> rangeList = templateRepository.queryTempRangeByTempId(tempId);
        if (CollectionUtils.isNotEmpty(rangeList)) {
            List<Long> ranges = rangeList.stream().map(CorporationProtocolTempRangeEntity::getSupplierId).collect(Collectors.toList());
            response.setCorporationIds(ranges);
        }
        //获取步骤
        List<CorporationProtocolTempStepEntity> tempStepList = templateRepository.queryTempStepByTempId(tempId);
        if (CollectionUtils.isNotEmpty(tempStepList)) {
            List<TemplateStep> steps = tempStepList.stream()
                    .sorted(Comparator.comparingLong(CorporationProtocolTempStepEntity::getSortBy))
                    .map(step -> {
                        //非抄送人汇总
                        TemplateStep templateStep = new TemplateStep();
                        templateStep.setStepId(step.getId());
                        templateStep.setOperate(step.getOperate());
                        templateStep.setSortBy(step.getSortBy());
                        templateStep.setStepName(step.getStepName());
                        //获取填充域列表
                        List<CorporationProtocolTempFiledEntity> templateFileds = templateRepository.queryTempFiledByStep(step.getId());
                        if (CollectionUtils.isNotEmpty(templateFileds)) {
                            List<TemplateFiled> filedList = templateFileds.stream()
                                    .map(filed -> {
                                        TemplateFiled f = new TemplateFiled();
                                        f.setFieldName(filed.getFieldName());
                                        f.setRelationCode(filed.getFieldCode());
                                        f.setRelationName(filed.getRelationName());
                                        f.setFieldType(filed.getOperate());
                                        if (step.getOperate() != null)
                                            f.setSignatory(step.getOperate());
                                        return f;
                                    }).collect(Collectors.toList());
                            templateStep.setFiledList(filedList);
                        }
                        return templateStep;
                    }).collect(Collectors.toList());
            response.setSteps(steps);
        }
        return response;
    }


    private CreateTemplateResponse createTemplateOfCloudSigning(AddUpdateTemplateVo param) {
        List<Step> steps = Optional.ofNullable(param.getSteps()).orElse(Collections.emptyList())
                .stream().map(TemplateStep::to).collect(Collectors.toList());
        CreateTemplateRequest request = new CreateTemplateRequest();
        request.setName(param.getTemplateName());
        request.setSteps(steps);
        List<Field> fields = new ArrayList<>();
        Optional.ofNullable(param.getSteps()).orElse(Collections.emptyList())
                .forEach(step -> {
                    step.getFiledList().forEach(field -> {
                        Field f = new Field();
                        f.setName(field.getFieldName());
                        f.setType(FieldType.TEXT);
                        fields.add(f);
                    });
                });
        request.setFields(fields);
        if (param.getTempId() != null) {
            CorporationProtocolTemplateEntity template = templateRepository.queryProtocolTempById(param.getTempId());
            request.setId(Long.parseLong(template.getAgentTemplate()));
        }
        request.setRemark("备注");
        BaseCloudSigningResponse<CreateTemplateResponse> resp = cloudSigningService.createTemplate(request);
        if (!SUCCESS.equals(resp.getCode())) {
            throw new ApiException(resp.getMessage(), ApiException.SYSTEM_ERROR);
        }
        return resp.getData();
    }

    private void uploadTemplateFileOfCloudSigning(UserEntity user,
                                                  AddUpdateTemplateVo param,
                                                  CreateTemplateResponse templateResponse) {
        //获取token
        AuthorizeRequest request = new AuthorizeRequest();
        request.setUser(String.valueOf(user.getId()));
        request.setResource("sig/tpl/" + templateResponse.getId());
        BaseCloudSigningResponse<AuthorizeResponse> response = cloudSigningService.authorize(request);
        if (!SUCCESS.equals(response.getCode())) {
            throw new ApiException("获取token失败", ApiException.SYSTEM_ERROR);
        }
        //上传文件
        UploadTemplateFileRequest uploadReq = new UploadTemplateFileRequest();
        uploadReq.setToken(response.getData().getToken());
        uploadReq.setUploadUrl(appProperties.getSigningTempUploadUrl());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        fileManager.load(param.getArchiveId(), outputStream);
        UploadTemplateFileResponse resp;
        try {
            resp = cloudSigningService.uploadTemplateFile(outputStream, uploadReq);
        } catch (IOException e) {
            throw new ApiException("获取文件失败，请重新上传", ApiException.SYSTEM_ERROR);
        }
        if (resp != null && !resp.success) {
            throw new ApiException(resp.getMessage() != null ? resp.getMessage().getMessage() : "上传文件失败", ApiException.SYSTEM_ERROR);
        }
    }

    private Long saveTemplate(TemplateController.SupplierAndUser supplierAndUser,
                              AddUpdateTemplateVo param,
                              CreateTemplateResponse templateResponse,
                              Set<Long> corporationIdSet) {
        //插入模板信息
        CorporationProtocolTemplateEntity template = null;
        if (param.getTempId() == null) {
            template = new CorporationProtocolTemplateEntity(supplierAndUser.getTenant());
        } else {
            template = templateRepository.queryProtocolTempById(param.getTempId());
        }

        template.setSupplierId(supplierAndUser.getSupplier().getId());
        template.setTempName(param.getTemplateName());
        template.setTempType(param.getTemplateType());
        template.setStatus(ProtocolTempEnum.DRAFT.name());
        template.setAgentTemplate(String.valueOf(templateResponse.getId()));
        template.setEditUrl(templateResponse.getUrl());
        template.setUploadUrl(templateResponse.getUploadUrl());
        if (param.getTempId() == null) {
            template.setTemplateFileId(param.getArchiveId());
            templateRepository.addProtocolTemplate(template);
        } else {
            templateRepository.updateProtocolTemp(template);
        }

        //插入模板使用范围
        if (param.getTempId() != null) {
            List<CorporationProtocolTempRangeEntity> tempRangeList = templateRepository.queryTempRangeByTempId(param.getTempId());
            //非数据权限内不可操作的数据
            List<Long> idOutOfAuth = tempRangeList.stream()
                    .map(CorporationProtocolTempRangeEntity::getSupplierCorporationId)
                    .filter(supplierCorporationId -> !corporationIdSet.contains(supplierCorporationId))
                    .toList();
            
            List<Long> mergedCorporationIds = new ArrayList<>(param.getCorporationIds());
            mergedCorporationIds.addAll(idOutOfAuth);
            
            // 去重并设置回参数对象
            param.setCorporationIds(mergedCorporationIds.stream().distinct().toList());
            //编辑操作，提前删除适用范围，步骤和域
            templateRepository.deleteRangeByTemp(param.getTempId());
        }
        if (CollectionUtils.isNotEmpty(param.getCorporationIds())) {
            CorporationProtocolTemplateEntity finalTemplate = template;
            List<CorporationProtocolTempRangeEntity> ranges = param.getCorporationIds().stream().map(id -> {
                CorporationProtocolTempRangeEntity rangeEntity = new CorporationProtocolTempRangeEntity();
                rangeEntity.setTemplateId(finalTemplate.getId());
                rangeEntity.setRangeType(1);//1：当前仅有1中范围类型，作业主体
                rangeEntity.setSupplierId(supplierAndUser.getSupplier().getId());
                rangeEntity.setSupplierCorporationId(id);
                rangeEntity.setSortedBy(param.getCorporationIds().indexOf(id));
                return rangeEntity;
            }).toList();
            templateRepository.saveTemplateRangeList(ranges);
        }
        //签署流程禁止变更
        //插入模板签署步骤
        if (CollectionUtils.isNotEmpty(param.getSteps())) {
            if (param.getTempId() == null) {
                CorporationProtocolTemplateEntity finalTemplate1 = template;
                param.getSteps().forEach(step -> {
                    CorporationProtocolTempStepEntity s = new CorporationProtocolTempStepEntity();
                    s.setTemplateId(finalTemplate1.getId());
                    s.setOperate(step.getOperate());
                    s.setStepName(step.getStepName());
                    finalTemplate1.setSupplierId(supplierAndUser.getSupplier().getId());
                    s.setSortBy(step.getSortBy());
                    templateRepository.saveTemplateStep(s);
                    if (CollectionUtils.isNotEmpty(step.getFiledList())) {
                        step.getFiledList().forEach(f -> {
                            CorporationProtocolTempFiledEntity protocolField = new CorporationProtocolTempFiledEntity();
                            protocolField.setSupplierId(supplierAndUser.getSupplier().getId());
                            protocolField.setTenantId(supplierAndUser.getSupplier().getTenantId());
                            protocolField.setFieldCode(f.getRelationCode());
                            protocolField.setFieldName(f.getFieldName());
                            protocolField.setTemplateId(finalTemplate1.getId());
                            protocolField.setTemplateStepId(s.getId());
                            protocolField.setTemplateStepName(s.getStepName());
                            protocolField.setOperate(f.getFieldType());
                            protocolField.setSignatory(f.getSignatory());
                            templateRepository.saveTemplateField(protocolField);
                        });
                    }
                });

            }
        }
        return template.getId();
    }
}
