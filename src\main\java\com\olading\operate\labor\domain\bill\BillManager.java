package com.olading.operate.labor.domain.bill;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.enums.ManageCalculationRuleEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.bill.dto.BillGenerateRequest;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportConfirmRequest;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportResult;
import com.olading.operate.labor.domain.bill.dto.BillOtherFeeImportRow;
import com.olading.operate.labor.domain.bill.repository.BillCategoryRepository;
import com.olading.operate.labor.domain.bill.repository.BillManagementFeeDetailRepository;
import com.olading.operate.labor.domain.bill.repository.BillMasterRepository;
import com.olading.operate.labor.domain.bill.repository.BillOtherFeeDetailRepository;
import com.olading.operate.labor.domain.bill.repository.BillSalaryDetailRepository;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.salary.QSalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractConfigEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单管理器
 */
@Component
@RequiredArgsConstructor
@Transactional
@Slf4j
public class BillManager {

    private final EntityManager em;
    private final ManagementFeeCalculationService managementFeeCalculationService;
    private final BillManagementFeeDetailRepository billManagementFeeDetailRepository;
    private final BillSalaryDetailRepository billSalaryDetailRepository;
    private final BillOtherFeeDetailRepository billOtherFeeDetailRepository;
    private final BillCategoryRepository billCategoryRepository;
    private final BillMasterRepository billMasterRepository;

    /**
     * 生成账单
     */
    public BillMasterEntity generateBill(TenantInfo tenantInfo,
                                         Long supplierId,
                                         BillGenerateRequest request,
                                         BusinessContractEntity contractInfo,
                                         BusinessContractConfigEntity contractConfig,
                                         List<SalaryDetailEntity> availableSalaryData
                                         ) {

        // 7. 创建账单主表
        BillMasterEntity billMaster = createBillMaster(tenantInfo, contractInfo, request);
        billMaster.setBillStatus(BillMasterStatus.GENERATING);
        em.persist(billMaster);
        em.flush(); // 确保获得ID

        // 8. 创建账单分类和明细
        createBillCategoriesAndDetails(tenantInfo, billMaster, contractConfig, availableSalaryData);

        // 9. 计算并更新账单总金额
        updateBillMasterAmounts(billMaster);

        // 10. 更新状态为已生成
        billMaster.setBillStatus(BillMasterStatus.GENERATED);
        em.merge(billMaster);

        log.info("账单生成完成, supplierId: {}, 合同: {}, 客户: {}, billId: {}, billNo: {}, totalAmount: {}, salaryBatchCount: {}",
                supplierId, contractInfo.getId(), contractInfo.getCustomerId(),
                billMaster.getId(), billMaster.getBillNo(), billMaster.getTotalReceivableAmount(),
                availableSalaryData.stream().map(SalaryDetailEntity::getSalaryStatementId).distinct().count());

        return billMaster;
    }

    /**
     * 提交账单确认
     */
    public void submitBillForConfirm(Long supplierId, Long billId) {
        BillMasterEntity bill = getBillWithPermissionCheck(supplierId, billId);
        if (bill.getBillStatus() != BillMasterStatus.GENERATED) {
            log.warn("账单状态不正确，无法提交确认, billId: {}, status: {}",billId, bill.getBillStatus());
            throw new BusinessException("只有已生成状态的账单才能提交确认");
        }
        bill.setBillStatus(BillMasterStatus.PENDING_CONFIRM);
        em.merge(bill);
        log.info("账单提交确认完成, billId: {}",billId);
    }

    /**
     * 确认账单
     */
    public void confirmBill(Long supplierId, Long billId) {
        BillMasterEntity bill = getBillWithPermissionCheck(supplierId,billId);
        if (bill.getBillStatus() != BillMasterStatus.PENDING_CONFIRM) {
            log.warn("账单状态不正确，无法确认, supplierId: {}, billId: {}, status: {}",
                    supplierId, billId, bill.getBillStatus());
            throw new BusinessException("只有待确认状态的账单才能确认");
        }

        bill.setBillStatus(BillMasterStatus.CONFIRMED);
        bill.setConfirmTime(LocalDateTime.now());
        em.merge(bill);

        log.info("账单确认完成, supplierId: {}, billId: {}", supplierId, billId);
    }

    /**
     * 删除账单（带权限检查）
     */
    public void deleteBill(Long supplierId, Long billId) {
        BillMasterEntity bill = getBillWithPermissionCheck(supplierId,billId);

        // 只有已确认之前的账单才能删除
        if (bill.getBillStatus() == BillMasterStatus.CONFIRMED ) {
            throw new BusinessException("已确认的账单不能删除");
        }

        physicalDeleteBill(billId);
        log.info("账单删除完成, billId: {}", billId);
    }


    /**
     * 获取账单并检查权限
     */
    public BillMasterEntity getBillWithPermissionCheck(Long supplierId, Long billId) {
        BillMasterEntity bill = em.find(BillMasterEntity.class, billId);
        if (bill == null || bill.isDeleted()) {
            throw new BusinessException("账单不存在");
        }
        if (!bill.getSupplierId().equals(supplierId)) {
            throw new BusinessException("无权限访问该账单");
        }
        return bill;
    }


    // ==================== 私有方法 ====================









    /**
     * 创建账单主表
     */
    private BillMasterEntity createBillMaster(TenantInfo tenantInfo, BusinessContractEntity contractInfo,
                                              BillGenerateRequest request) {
        BillMasterEntity billMaster = new BillMasterEntity(tenantInfo);
        billMaster.setBillNo(generateBillNo());
        billMaster.setSupplierId(contractInfo.getSupplierId());
        billMaster.setCustomerId(contractInfo.getCustomerId());
        billMaster.setSupplierCorporationId(contractInfo.getSupplierCorporationId());
        billMaster.setContractId(request.getContractId());
        billMaster.setBillMonth(request.getBillMonth());
        billMaster.setBillStatus(BillMasterStatus.GENERATING);
        billMaster.setRemark(request.getRemark());
        return billMaster;
    }

    /**
     * 创建账单分类和明细
     */
    private void createBillCategoriesAndDetails(TenantInfo tenantInfo, BillMasterEntity billMaster,
                                                BusinessContractConfigEntity contractConfig,
                                                List<SalaryDetailEntity> salaryDataList) {

        // 创建薪酬费用分类和明细
        createSalaryCategoryAndDetails(tenantInfo, billMaster, salaryDataList);

        // 创建管理费分类和明细（使用版本，支持重复收费控制）
        createManagementFeeCategoryAndDetails(tenantInfo, billMaster, contractConfig, salaryDataList);

    }

    /**
     * 创建薪酬费用分类和明细
     */
    private void createSalaryCategoryAndDetails(TenantInfo tenantInfo, BillMasterEntity billMaster,
                                                List<SalaryDetailEntity> salaryDataList) {

        // 创建薪酬费用分类
        BillCategoryEntity salaryCategory = new BillCategoryEntity(tenantInfo);
        salaryCategory.setBillMasterId(billMaster.getId());
        salaryCategory.setFeeType(BillFeeType.SALARY);
        salaryCategory.setDetailCount(salaryDataList.size());
        salaryCategory.setPersonCount((int) salaryDataList.stream().map(SalaryDetailEntity::getIdCard).distinct().count());
        salaryCategory.setCalculationRule("薪酬费用统计");
        salaryCategory.setBillMonth(billMaster.getBillMonth());

        BigDecimal totalSalaryAmount = BigDecimal.ZERO;

        // 创建薪酬明细
        List<BillSalaryDetailEntity> salaryDetails = new ArrayList<>();
        for (SalaryDetailEntity salaryDetail : salaryDataList) {
            BillSalaryDetailEntity billSalaryDetail = new BillSalaryDetailEntity(tenantInfo);
            billSalaryDetail.setBillMasterId(billMaster.getId());
            billSalaryDetail.setSalaryDetailId(salaryDetail.getId());
            billSalaryDetail.setSalaryBatchId(salaryDetail.getSalaryStatementId());
            billSalaryDetail.setLaborName(salaryDetail.getName());
            billSalaryDetail.setIdCard(salaryDetail.getIdCard());
            billSalaryDetail.setGrossSalary(salaryDetail.getPayableAmount());
            billSalaryDetail.setNetSalary(salaryDetail.getNetPayment());
            billSalaryDetail.setIncomeTax(salaryDetail.getCurrentTaxAmount());
            billSalaryDetail.setVatTax(salaryDetail.getVatAmount());
            billSalaryDetail.setAdditionalTax(salaryDetail.getAdditionalTaxAmount());
            billSalaryDetail.setBillMonth(billMaster.getBillMonth());
            billSalaryDetail.setSalaryPeriod(billMaster.getBillMonth().format(DateTimeFormatter.ofPattern("yyyy-MM")));

            salaryDetails.add(billSalaryDetail);
            totalSalaryAmount = totalSalaryAmount.add(salaryDetail.getPayableAmount());
        }

        salaryCategory.setTotalAmount(totalSalaryAmount);
        em.persist(salaryCategory);
        em.flush();

        // 设置分类ID并保存明细
        for (BillSalaryDetailEntity detail : salaryDetails) {
            detail.setBillCategoryId(salaryCategory.getId());
        }
        billSalaryDetailRepository.saveAll(salaryDetails);
    }

    /**
     * 创建管理费分类和明细（支持按人员汇总和重复收费控制）
     */
    private void createManagementFeeCategoryAndDetails(TenantInfo tenantInfo,
                                                         BillMasterEntity billMaster,
                                                         BusinessContractConfigEntity contractConfig,
                                                         List<SalaryDetailEntity> salaryDataList) {

        // 获取当月按人数计算 且已收费的人员信息
        Map<String, PersonManagementFeeInfo> personFeeMap = getPersonManagementFeeInfo(billMaster.getContractId(), billMaster.getBillMonth());

        //汇总当前需要处理的人员
        Map<String, PersonManagementFeeInfo> currPersonFeeMap = new HashMap<>();

        // 添加本次薪酬数据中的人员
        for (SalaryDetailEntity salary : salaryDataList) {
            String personKey = salary.getIdCard();
            PersonManagementFeeInfo pe = personFeeMap.get(personKey);
            if (pe == null) {
                pe = new PersonManagementFeeInfo(
                        salary.getName(), salary.getIdCard(), false,BigDecimal.ZERO , salary.getPayableAmount());
            }else{
                pe.setPayableAmount(pe.getPayableAmount().add(salary.getPayableAmount()));
            }
            currPersonFeeMap.put(personKey, pe);
            personFeeMap.put(personKey, pe);
        }



        // 创建管理费分类
        BillCategoryEntity managementFeeCategory = new BillCategoryEntity(tenantInfo);
        managementFeeCategory.setBillMasterId(billMaster.getId());
        managementFeeCategory.setFeeType(BillFeeType.MANAGEMENT_FEE);
        managementFeeCategory.setDetailCount(currPersonFeeMap.size());
        managementFeeCategory.setPersonCount(currPersonFeeMap.size());
        managementFeeCategory.setCalculationRule(contractConfig.getManageCalculationRule());
        managementFeeCategory.setBillMonth(billMaster.getBillMonth());

        BigDecimal totalManagementFee = BigDecimal.ZERO;

        // 为每个人员创建管理费明细
        List<BillManagementFeeDetailEntity> managementFeeDetails = new ArrayList<>();
        for (PersonManagementFeeInfo personInfo : currPersonFeeMap.values()) {
            BigDecimal feeAmount = BigDecimal.ZERO;

            // 计算费用
            feeAmount = managementFeeCalculationService.calculatePersonManagementFee(contractConfig, personInfo);


            BillManagementFeeDetailEntity detail = new BillManagementFeeDetailEntity(tenantInfo);
            detail.setBillMasterId(billMaster.getId());
            detail.setLaborName(personInfo.getLaborName());
            detail.setIdCard(personInfo.getIdCard());
            detail.setFeeItem("管理费");
            detail.setManagementFeeAmount(feeAmount);
            detail.setBillMonth(billMaster.getBillMonth());
            detail.setCalculationBase(contractConfig.getManageAmount());
            detail.setCalculationRate(contractConfig.getManageRate());
            detail.setCalculationRule(ManageCalculationRuleEnum.valueOf(contractConfig.getManageCalculationRule()));

            managementFeeDetails.add(detail);
            totalManagementFee = totalManagementFee.add(feeAmount);
        }

        managementFeeCategory.setTotalAmount(totalManagementFee);
        em.persist(managementFeeCategory);
        em.flush();

        // 设置分类ID并保存明细
        for (BillManagementFeeDetailEntity detail : managementFeeDetails) {
            detail.setBillCategoryId(managementFeeCategory.getId());
        }
        billManagementFeeDetailRepository.saveAll(managementFeeDetails);
    }

    /**
     * 获取人员管理费信息（按人数计算） 已收费人员
     */
    private Map<String, PersonManagementFeeInfo> getPersonManagementFeeInfo(Long contractId, java.time.LocalDate billMonth) {
        // 获取当月已收费的人员信息
        QBillManagementFeeDetailEntity billManagementFeeDetailEntity = QBillManagementFeeDetailEntity.billManagementFeeDetailEntity;
        QBillMasterEntity billMasterEntity = QBillMasterEntity.billMasterEntity;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(em);
        List<Tuple> chargedPersons = jpaQueryFactory.select(
                        billManagementFeeDetailEntity.laborName,
                        billManagementFeeDetailEntity.idCard,
                        billManagementFeeDetailEntity.managementFeeAmount.sum())
                .from(billManagementFeeDetailEntity)
                .join(billMasterEntity).on(billManagementFeeDetailEntity.billMasterId.eq(billMasterEntity.id))
                .where(
                        billMasterEntity.contractId.eq(contractId),
                        billMasterEntity.billMonth.eq(billMonth),
                        billMasterEntity.deleted.isFalse(),
                        billManagementFeeDetailEntity.deleted.isFalse(),
                        billManagementFeeDetailEntity.calculationRule.eq(ManageCalculationRuleEnum.EMPLOYEE_COUNT),
                        billManagementFeeDetailEntity.managementFeeAmount.gt(BigDecimal.ZERO),
                        billMasterEntity.billStatus.eq(BillMasterStatus.CONFIRMED)
                )
                .groupBy(billManagementFeeDetailEntity.idCard)
                .fetch();

        Map<String, PersonManagementFeeInfo> personFeeMap = new HashMap<>();

        for (Tuple row : chargedPersons) {
            String laborName = row.get(billManagementFeeDetailEntity.laborName);
            String idCard = row.get(billManagementFeeDetailEntity.idCard);
            BigDecimal chargedAmount = row.get(billManagementFeeDetailEntity.managementFeeAmount.sum());
            personFeeMap.put(idCard, new PersonManagementFeeInfo(
                    laborName, idCard, true, chargedAmount));
        }

        return personFeeMap;
    }



    /**
     * 生成账单编号
     */
    private static String generateBillNo() {
        // 生成账单编号：BILL + 年月日时分秒 + 6位随机数
        String dateStr = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyMMddHHmmSSS"));
        String randomStr = String.format("%06d", (int) (Math.random() * 1000000));
        return "BILL" + dateStr + randomStr;
    }

    /**
     * 物理删除账单及其相关数据
     */
    private void physicalDeleteBill(Long billId) {
        // 1. 删除薪酬明细
        billSalaryDetailRepository.deleteByBillMasterId(billId);

        // 2. 删除管理费明细
        billManagementFeeDetailRepository.deleteByBillMasterId(billId);

        // 3. 删除其他费用明细
        billOtherFeeDetailRepository.deleteByBillMasterId(billId);

        // 4. 删除账单分类
        billCategoryRepository.deleteByBillMasterId(billId);

        // 5. 删除账单主表
        billMasterRepository.deleteById(billId);

        log.info("账单物理删除完成, billId: {}", billId);
    }

    public List<Long> getUsedSalaryBatchIds(BusinessContractEntity contractInfo, LocalDate billMonth) {
        final QSalaryStatementEntity salaryStatementEntity = QSalaryStatementEntity.salaryStatementEntity;
        final QBillSalaryDetailEntity billSalaryDetailEntity = QBillSalaryDetailEntity.billSalaryDetailEntity;
        final JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(em);
        return jpaQueryFactory.select(salaryStatementEntity.id)
                .distinct()
                .from(billSalaryDetailEntity)
                .leftJoin(salaryStatementEntity).on(salaryStatementEntity.id.eq(billSalaryDetailEntity.salaryBatchId))
                .where(salaryStatementEntity.contractId.eq(contractInfo.getId())
                        .and(salaryStatementEntity.taxPeriod.eq(billMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")))
                                .and(salaryStatementEntity.deleted.eq(false))))
                .fetch();
    }

    public List<BillCategoryEntity> getCategoriesByBillId(Long billId) {

        return billCategoryRepository.findByBillMasterIdAndDeletedFalse(billId);
    }

    /**
     * 获取合同下已确认的账单
     */
    public List<BillMasterEntity> getConfirmedBillsByContract(Long contractId, LocalDate billMonth) {
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(em);
        QBillMasterEntity billMaster = QBillMasterEntity.billMasterEntity;
        
        var query = jpaQueryFactory.selectFrom(billMaster)
                .where(billMaster.contractId.eq(contractId)
                        .and(billMaster.billStatus.eq(BillMasterStatus.CONFIRMED))
                        .and(billMaster.deleted.eq(false)));
        
        if (billMonth != null) {
            query.where(billMaster.billMonth.eq(billMonth));
        }
        
        return query.orderBy(billMaster.billMonth.desc()).fetch();
    }


    /**
     * 创建导入其他费用
     * @param supplierId
     * @param bill
     * @param request
     * @return
     */
    public BillOtherFeeImportResult createImportOtherFee(Long supplierId, BillMasterEntity bill, BillOtherFeeImportConfirmRequest request){
        // 3. 生成导入批次号
        String importBatchNo = generateImportBatchNo();

        // 4. 创建或更新其他费用分类
        BillCategoryEntity otherFeeCategory = createOrUpdateOtherFeeCategory(TenantInfo.ofSupplier(supplierId), bill);

        // 5. 批量创建明细数据
        List<BillOtherFeeDetailEntity> details = createOtherFeeDetails(TenantInfo.ofSupplier(supplierId),
                bill, otherFeeCategory, request.getImportData(), importBatchNo);

        // 6. 更新分类统计
        updateCategoryStatistics(otherFeeCategory, details);

        // 7. 更新账单主表金额
        this.updateBillMasterAmounts(bill);

        // 8. 构建返回结果
        BigDecimal totalAmount = details.stream()
                .map(BillOtherFeeDetailEntity::getFeeAmount)
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

        int personCount = (int) details.stream()
                .map(BillOtherFeeDetailEntity::getIdCard)
                .distinct()
                .count();

        log.info("确认导入其他费用数据完成, supplierId: {}, billId: {}, importCount: {}, totalAmount: {}, batchNo: {}",
                supplierId, bill.getId(), details.size(), totalAmount, importBatchNo);

        return BillOtherFeeImportResult.success(
                details.size(), importBatchNo, totalAmount, personCount);
    }



    /**
     * 生成导入批次号
     */
    private String generateImportBatchNo() {
        return "IMP" + java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss"))
                + String.format("%04d", (int)(Math.random() * 10000));
    }



    /**
     * 创建或更新其他费用分类
     */
    private BillCategoryEntity createOrUpdateOtherFeeCategory(TenantInfo tenantInfo, BillMasterEntity bill) {
        // 查找是否已存在其他费用分类
        List<BillCategoryEntity> existingCategories = billCategoryRepository
                .findByBillMasterIdAndFeeTypeAndDeletedFalse(bill.getId(), BillFeeType.OTHER_FEE);

        BillCategoryEntity category;
        if (!existingCategories.isEmpty()) {
            // 使用现有分类
            category = existingCategories.get(0);
        } else {
            // 创建新分类
            category = new BillCategoryEntity(tenantInfo);
            category.setBillMasterId(bill.getId());
            category.setFeeType(BillFeeType.OTHER_FEE);
            category.setTotalAmount(java.math.BigDecimal.ZERO);
            category.setDetailCount(0);
            category.setPersonCount(0);
            category.setCalculationRule("其他费用导入");
            category.setBillMonth(bill.getBillMonth());
            category = billCategoryRepository.save(category);
        }

        return category;
    }

    /**
     * 创建其他费用明细数据
     */
    private List<BillOtherFeeDetailEntity> createOtherFeeDetails(TenantInfo tenantInfo, BillMasterEntity bill,
                                                                 BillCategoryEntity category,
                                                                 List<BillOtherFeeImportRow> importRows,
                                                                 String importBatchNo) {
        List<BillOtherFeeDetailEntity> details = new ArrayList<>();

        for (BillOtherFeeImportRow row : importRows) {
            BillOtherFeeDetailEntity detail = new BillOtherFeeDetailEntity(tenantInfo);
            detail.setBillMasterId(bill.getId());
            detail.setBillCategoryId(category.getId());

            // 费用名称
            detail.setFeeName(row.getFeeName());
            // 费用金额
            detail.setFeeAmount(row.getFeeAmount());
            // 产生人
            detail.setLaborName(row.getLaborName());
            // 身份证号
            detail.setIdCard(row.getIdCard());
            // 费用用途
            detail.setFeePurpose(row.getFeePurpose());
            // 产生时间
            detail.setOccurDate(row.getOccurDate());

            // 设置账单月份（使用账单主表的月份）
            detail.setBillMonth(bill.getBillMonth());

            // 设置导入批次号
            detail.setImportBatchNo(importBatchNo);

            // 可选字段设置默认值
            detail.setFeeCategory("导入费用");
            detail.setFeeDescription(String.format("费用产生时间：%s",
                    row.getOccurDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));

            details.add(detail);
        }

        return billOtherFeeDetailRepository.saveAll(details);
    }

    /**
     * 更新分类统计信息
     */
    private void updateCategoryStatistics(BillCategoryEntity category, List<BillOtherFeeDetailEntity> details) {
        // 重新计算该分类下的所有明细统计
        List<BillOtherFeeDetailEntity> allDetails = billOtherFeeDetailRepository
                .findByBillCategoryIdAndDeletedFalse(category.getId());

        java.math.BigDecimal totalAmount = allDetails.stream()
                .map(BillOtherFeeDetailEntity::getFeeAmount)
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

        int detailCount = allDetails.size();
        int personCount = (int) allDetails.stream()
                .map(BillOtherFeeDetailEntity::getIdCard)
                .distinct()
                .count();

        category.setTotalAmount(totalAmount);
        category.setDetailCount(detailCount);
        category.setPersonCount(personCount);

        billCategoryRepository.save(category);
    }



    /**
     * 更新账单主表金额（支持其他费用导入后的金额更新）
     */
    public void updateBillMasterAmounts(BillMasterEntity billMaster) {
        // 重新计算所有分类的金额
        List<BillCategoryEntity> categories = billCategoryRepository
            .findByBillMasterIdAndDeletedFalse(billMaster.getId());
        
        BigDecimal salaryAmount = BigDecimal.ZERO;
        BigDecimal managementFeeAmount = BigDecimal.ZERO;
        BigDecimal otherFeeAmount = BigDecimal.ZERO;
        
        for (BillCategoryEntity category : categories) {
            switch (category.getFeeType()) {
                case SALARY:
                    salaryAmount = category.getTotalAmount();
                    break;
                case MANAGEMENT_FEE:
                    managementFeeAmount = category.getTotalAmount();
                    break;
                case OTHER_FEE:
                    otherFeeAmount = category.getTotalAmount();
                    break;
            }
        }
        
        BigDecimal totalAmount = salaryAmount.add(managementFeeAmount).add(otherFeeAmount);
        billMaster.setSalaryAmount(salaryAmount);
        billMaster.setManagementFeeAmount(managementFeeAmount);
        billMaster.setOtherFeeAmount(otherFeeAmount);
        billMaster.setTotalReceivableAmount(totalAmount);
        billMaster.setTotalInvoiceAmount(totalAmount);
        
        em.merge(billMaster);
        
        log.info("更新账单主表金额完成, billId: {}, 薪酬: {}, 管理费: {}, 其他费用: {}, 总计: {}", 
                billMaster.getId(), salaryAmount, managementFeeAmount, otherFeeAmount, totalAmount);
    }

    // ==================== 内部类 ====================

    /**
     * 合同账单信息封装类
     */
    @lombok.Data
    @lombok.Builder
    public static class ContractBillInfo {
        private Long contractId;
        private String contractName;
        private Long supplierId;
        private Long customerId;
        private String customerName;
        private Long supplierCorporationId;
        private String corporationName;
        private BusinessContractEntity contract;
        private CustomerEntity customer;
        private SupplierCorporationEntity corporation;
    }

    /**
     * 人员管理费信息
     */
    public static class PersonManagementFeeInfo {
        private final String laborName;
        private final String idCard;
        private final boolean alreadyCharged;
        private BigDecimal chargedAmount = BigDecimal.ZERO;;
        private BigDecimal payableAmount = BigDecimal.ZERO;

        public PersonManagementFeeInfo(String laborName, String idCard, boolean alreadyCharged, BigDecimal chargedAmount) {
            this.laborName = laborName;
            this.idCard = idCard;
            this.alreadyCharged = alreadyCharged;
            this.chargedAmount = chargedAmount;
        }

        public PersonManagementFeeInfo(String laborName, String idCard, boolean alreadyCharged, BigDecimal chargedAmount, BigDecimal payableAccount) {
            this.laborName = laborName;
            this.idCard = idCard;
            this.alreadyCharged = alreadyCharged;
            this.chargedAmount = chargedAmount;
            this.payableAmount = payableAccount;
        }

        public String getLaborName() { return laborName; }
        public String getIdCard() { return idCard; }
        public boolean isAlreadyCharged() { return alreadyCharged; }
        public BigDecimal getChargedAmount() { return chargedAmount; }
        public BigDecimal getPayableAmount() { return payableAmount; }
        public void setPayableAmount(BigDecimal payableAmount) { this.payableAmount = payableAmount; }
        public void setChargedAmount(BigDecimal chargedAmount) { this.chargedAmount = chargedAmount; }
    }
}