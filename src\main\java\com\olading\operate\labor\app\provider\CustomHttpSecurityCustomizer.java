package com.olading.operate.labor.app.provider;

import com.olading.boot.core.spi.HttpSecurityCustomizer;
import com.olading.operate.labor.app.Authority;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.stereotype.Component;

@Component
public class CustomHttpSecurityCustomizer implements HttpSecurityCustomizer {

    @Override
    public void customizer(HttpSecurity http) throws Exception {
        http.authorizeHttpRequests(z -> {
            z.requestMatchers("/api/boss/**").hasAuthority(Authority.SYS_BOSS)
                    .requestMatchers("/api/supplier/**").hasAuthority(Authority.SYS_SUPPLIER)
                    .requestMatchers("/api/customer/**").hasAuthority(Authority.SYS_CUSTOMER)
                    .requestMatchers("/api/personal/**").hasAuthority(Authority.SYS_PERSONAL)
                    .requestMatchers("/**").permitAll();
        });
    }
}
