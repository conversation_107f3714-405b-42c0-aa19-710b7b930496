package com.olading.operate.labor.domain.bill.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 账单生成请求 - 极简版
 */
@Data
@Schema(description = "账单生成请求")
public class BillGenerateRequest {

    @NotNull(message = "合同ID不能为空")
    @Schema(description = "合同ID", required = true)
    private Long contractId;

    @NotNull(message = "账单月份不能为空")
    @Schema(description = "账单月份", required = true, example = "2025-07-01")
    private LocalDate billMonth;

    @Schema(description = "备注")
    private String remark;
}