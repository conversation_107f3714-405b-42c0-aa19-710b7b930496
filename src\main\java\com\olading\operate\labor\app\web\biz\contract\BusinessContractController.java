package com.olading.operate.labor.app.web.biz.contract;

import com.olading.boot.core.business.webapi.WebApiQueryData;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.ManageCalculationRuleEnum;
import com.olading.operate.labor.domain.query.BusinessContractQuery;
import com.olading.operate.labor.domain.service.BusinessContractService;
import com.olading.operate.labor.domain.service.CustomerService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.supplier.SupplierEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Tag(name = "企业客户服务合同相关接口")
@RestController
@RequestMapping("/api/supplier/contract")
@RequiredArgsConstructor
@Slf4j
public class BusinessContractController extends BusinessController {

    private final CustomerService customerService;
    private final BusinessContractService businessContractService;
    private final QueryService queryService;


    @Operation(summary = "查询服务合同列表")
    @PostMapping("/listContract")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<ContractVo> listContract(@RequestBody QueryFilter<WebContractFilters> request) {
        QueryFilter<BusinessContractQuery.Filters> filter = request.convert(WebContractFilters ::convert);
        filter.sort("id", Direction.DESCENDING);
        filter.getFilters().setContractIds(currentDataScope().get(OwnerType.CONTRACT));
        DataSet<ContractVo> ds = queryService.queryContract(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "新增服务合同")
    @PostMapping("/addContract")
    @AuthorityGuard(any = Authority.SUPPLIER_CUSTOMER_CONTRACT)
    public WebApiResponse<Void> addContract(@Valid @RequestBody ContractParam contractParam) {
        validateContractParam(contractParam);
        ContractVo contractVo = new ContractVo();
        BeanUtils.copyProperties(contractParam, contractVo);
        contractVo.setCreatorId(currentUserId());
        contractVo.setSupplierId(currentSupplierId());
        businessContractService.addContract(currentTenant(),contractVo);
        return WebApiResponse.success();
    }

    @Operation(summary = "更新服务合同")
    @PostMapping("/updateContract")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#contractParam.id")
    })
    @AuthorityGuard(any = Authority.SUPPLIER_CUSTOMER_CONTRACT)
    public WebApiResponse<Void> updateContract(@Valid @RequestBody ContractParam contractParam) {
        validateContractParam(contractParam);
        ContractVo contractVo = new ContractVo();
        BeanUtils.copyProperties(contractParam, contractVo);
        contractVo.setUpdaterId(currentUserId());
        contractVo.setSupplierId(currentSupplierId());
        businessContractService.updateContract(currentTenant(),contractVo);
        return WebApiResponse.success();
    }

    private void validateContractParam(ContractParam param) {
        if (param == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (param.getManageCalculationRule() != null &&
                ManageCalculationRuleEnum.getNameByKey(param.getManageCalculationRule()).isEmpty()) {
            throw new IllegalArgumentException("不支持的计算规则：" + param.getManageCalculationRule());
        }
    }

    @Operation(summary = "查询服务合同")
    @PostMapping("/queryContract")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#request.id")
    })
    public WebApiResponse<ContractVo> queryContract(@Valid @RequestBody IdRequest request) {
        currentUserId();
        ContractVo vo = businessContractService.queryContract(request.getId());
        return WebApiResponse.success(vo);
    }

    @Data
    public static class IdRequest {
        @NotNull(message = "企业客户不能为空")
        @Schema(description = "企业客户id")
        private Long id;
    }

    @Data
    public static class ContractTerminateParam {
        @NotNull(message = "合同ID不能为空")
        @Schema(description = "合同ID")
        private Long id;

        @NotBlank(message = "提前终止原因不能为空")
        @Schema(description = "提前终止原因")
        private String stopReason;
    }

    @Operation(summary = "查询服务合同")
    @PostMapping("/queryContractBySupplier")
    public WebApiResponse<WebApiQueryData<ContractVo>> queryContractBySupplier(@Valid @RequestBody ContractParam contractParam) {
        SupplierEntity supplierEntity = currentSupplier();
        List<ContractVo> contractVos = businessContractService.queryContractBySupplier(supplierEntity.getId());
        return WebApiResponse.query(contractVos);
    }

    @Operation(summary = "提前终止服务合同")
    @PostMapping("/terminateContract")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#param.id")
    })
    public WebApiResponse<Void> terminateContract(@Valid @RequestBody ContractTerminateParam param) {
        businessContractService.terminateContract(currentTenant(), param.getId(), param.getStopReason(), currentUserId());
        return WebApiResponse.success();
    }

    @Operation(summary = "根据客户ID和作业主体ID查询服务合同列表")
    @PostMapping("/listContractByCustomerAndCorporation")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<ContractVo> listContractByCustomerAndCorporation(@Valid @RequestBody QueryFilter<ContractQueryByCustomerAndCorporationFilters> request) {
        QueryFilter<BusinessContractQuery.Filters> filter = request.convert(ContractQueryByCustomerAndCorporationFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        filter.getFilters().setContractIds(currentDataScope().get(OwnerType.CONTRACT));
        DataSet<ContractVo> ds = queryService.queryContract(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }



    @Data
    public static class WebContractFilters {

        @Schema(description = "合同名称")
        private String name;

        @Schema(description = "编号")
        private Long id;

        @Schema(description = "作业主体名称")
        private String supplierName;

        @Schema(description = "作业主体id")
        private Long supplierCorporationId;

        @Schema(description = "客户id")
        private Long customerId;

        @Schema(description = "客户名称")
        private String customerName;

        @Schema(description = "创建时间起")
        private LocalDate createTimeStart;

        @Schema(description = "创建时间止")
        private LocalDate createTimeEnd;

        @Schema(description = "合同状态")
        private String status;

        @Schema(description = "合同ID集合")
        private Set<Long> contractIds;

        public BusinessContractQuery.Filters convert() {
            return Beans.copyBean(this, BusinessContractQuery.Filters.class);
        }
    }

    @Data
    public static class ContractQueryByCustomerAndCorporationFilters {
        @NotNull(message = "客户ID不能为空")
        @Schema(description = "客户ID")
        private Long customerId;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @Schema(description = "合同ID集合")
        private Set<Long> contractIds;

        public BusinessContractQuery.Filters convert() {
            BusinessContractQuery.Filters filters = new BusinessContractQuery.Filters();
            filters.setCustomerId(this.customerId);
            filters.setSupplierCorporationId(this.supplierCorporationId);
            filters.setContractIds(this.contractIds);
            return filters;
        }
    }

    @Data
    public static class ContractParam {
        @Schema(description = "合同ID")
        private Long id;

        @NotNull(message = "客户ID不能为空")
        @Schema(description = "客户ID")
        private Long customerId;

        @NotNull(message = "作业主体ID不能为空")
        @Schema(description = "作业主体ID")
        private Long supplierCorporationId;

        @NotBlank(message = "合同名称不能为空")
        @Schema(description = "合同名称")
        private String name;

        @Schema(description = "合同编号")
        private String sn;

        @Schema(description = "合同期限是否固定")
        private Boolean timeFixed;

        @Schema(description = "开始日期")
        private LocalDate startDate;

        @Schema(description = "结束日期")
        private LocalDate endDate;

        @Schema(description = "是否提前中止")
        private Boolean stopped;

        @Schema(description = "提前中止时间")
        private Instant stopTime;

        @Schema(description = "提前中止原因")
        private String stopReason;

        @Schema(description = "业务类型")
        private String businessType;

        @Schema(description = "备注")
        private String remark;

        @Schema(description = "附件IDs")
        private String fileIds;

        @Schema(description = "发票抬头")
        private String invoiceTitle;

        @Schema(description = "税号")
        private String invoiceTaxNo;

        @Schema(description = "开户行")
        private String invoiceBankName;

        @Schema(description = "银行账号")
        private String invoiceBankAccount;

        @Schema(description = "注册地址")
        private String invoiceRegisterAddress;

        @Schema(description = "企业电话")
        private String invoiceCompanyTel;

        @Schema(description = "发票备注")
        private String invoiceRemark;

        @NotBlank(message = "计算规则不能为空")
        @Schema(description = "计算规则")
        private String manageCalculationRule;

        @Schema(description = "金额")
        private BigDecimal manageAmount;

        @Schema(description = "费率")
        private BigDecimal manageRate;

        @Schema(description = "角色Id")
        private List<Long> roleIds;
    }
}
