package com.olading.operate.labor.domain.bill;

import com.olading.operate.labor.app.web.biz.enums.ManageCalculationRuleEnum;
import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单管理费明细表（第三层）- 管理费明细
 */
@Entity
@Table(name = "t_bill_management_fee_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("账单管理费明细表")
public class BillManagementFeeDetailEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Comment("账单主表ID")
    @Column(name = "bill_master_id", nullable = false)
    private Long billMasterId;

    @NotNull
    @Comment("账单分类ID")
    @Column(name = "bill_category_id", nullable = false)
    private Long billCategoryId;

    @NotNull
    @Comment("人员姓名")
    @Column(name = "labor_name", nullable = false, length = 50)
    private String laborName;

    @NotNull
    @Comment("身份证号")
    @Column(name = "id_card", nullable = false, length = 18)
    private String idCard;

    @NotNull
    @Comment("收费项目")
    @Column(name = "fee_item", nullable = false, length = 100)
    private String feeItem;

    @NotNull
    @Comment("管理费金额")
    @Column(name = "management_fee_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal managementFeeAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("账单月份")
    @Column(name = "bill_month", nullable = false)
    private LocalDate billMonth;

    @Comment("人头/管理费金额")
    @Column(name = "calculation_base", precision = 19, scale = 2)
    private BigDecimal calculationBase;

    @Comment("应发/管理费比率")
    @Column(name = "calculation_rate", precision = 8, scale = 4)
    private BigDecimal calculationRate;

    @Comment("计算规则")
    @Column(name = "calculation_rule", length = 50)
    @Enumerated(EnumType.STRING)
    private ManageCalculationRuleEnum calculationRule;

    @Comment("备注")
    @Column(name = "remark", length = 200)
    private String remark;

    public BillManagementFeeDetailEntity(TenantInfo info) {
        setTenant(info);
    }

    protected BillManagementFeeDetailEntity() {
    }
}